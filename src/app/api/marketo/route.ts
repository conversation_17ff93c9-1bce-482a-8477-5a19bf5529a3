export async function POST(request: Request) {
  try {
    const requestBody = await request.json();

    await fetch(`${process.env.NEXT_PUBLIC_API_URL}/form-handler`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });
  } catch (error: any) {
    return new Response(`Form submission error: ${error}`, {
      status: 400,
    });
  }

  return new Response("Success!", {
    status: 200,
  });
}
