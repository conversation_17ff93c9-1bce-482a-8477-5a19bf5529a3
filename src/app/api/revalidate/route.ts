import { revalidatePath } from "next/cache";
import type { NextRequest } from "next/server";
import { siteUrl } from "@/common/constants";
import { validateSignature } from "@/common/utils";

const webhookSecret = process.env.STORYBLOK_WEBHOOK_SECRET ?? "";

interface WebhookTrigger {
  text: string;
  action: string;
  full_slug?: string;
}

export async function POST(request: NextRequest) {
  const payload = await request.text();
  const signature = request.headers.get("webhook-signature");
  if (!signature) {
    return new Response("Invalid signature", { status: 401 });
  }
  const isValid = validateSignature(webhookSecret, payload, signature);

  if (isValid) {
    try {
      const data = JSON.parse(payload) as WebhookTrigger;

      if (data.action === "published" || data.action === "unpublished") {
        const path = `/${data.full_slug}`;
        console.log("revalidatePath", path);

        // clear Vercel cache
        // https://nextjs.org/docs/app/api-reference/functions/revalidatePath
        revalidatePath(path);

        // clear Fastly cache
        // https://www.fastly.com/documentation/reference/api/purging/#purge-single-url
        const response = await fetch(siteUrl + path, { method: "PURGE" });
        console.log("FASTLY:", response);
      }
    } catch (err: any) {
      console.error(err);
    }
  }

  return Response.json({
    isValid,
    payload,
  });
}
