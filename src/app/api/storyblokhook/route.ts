import { cacheTagBuilder, getStoryVersions } from "@/common/storyblok";
import {
  ILearningCollectionContent,
} from "@/common/types";
import { validateSignature } from "@/common/utils";
import {
  IBlogStoryContent,
} from "@/components/bloks/NewsAndArticles/utils";
import { revalidateTag } from "next/cache";
import type { NextRequest } from "next/server";

const webhookSecret = process.env.STORYBLOK_WEBHOOK_SECRET ?? "";

export interface IWebhookPayload {
  text: string;
  action: "published" | "unpublished" | "entries_updated" | "entries_deleted";
  space_id: number;
}

export interface IWebhookPayloadEntriesEvent extends IWebhookPayload {
  action: "entries_updated" | "entries_deleted";
  datasource_slug: string;
}

export interface IWebhookPayloadOrUnpublished extends IWebhookPayload {
  action: "published" | "unpublished";
  full_slug: string;
  story_id: number;
}

const getTagNameFromText = (text: string) => {
  const re = /The datasource entry ([a-zA-Z0-9_-\s]+) has been/;
  const match = re.exec(text);
  if (match) {
    return match[1];
  }
  return null;
};

const getLocaleBySlug = (slug: string) => {
  return slug.split("/")[0];
};

const handleBlogChange = async (
  content: IBlogStoryContent,
  fullSlug: string,
) => {
  const locale = getLocaleBySlug(fullSlug);
  const { blogTags } = content;
  blogTags.forEach((t) => {
    revalidateTag(cacheTagBuilder("tag-story", { locale, tag: t.tag }));
  });
};

const handleTagHomepageChange = async (fullSlug: string) => {
  revalidateTag(
    cacheTagBuilder("blog-homepage", { locale: getLocaleBySlug(fullSlug) }),
  );
};

const handleLearningCollectionChange = async (
  content: ILearningCollectionContent,
  fullSlug: string,
) => {
  const locale = getLocaleBySlug(fullSlug);
  const tags = content.tags.map((t) => t.tag);
  tags.forEach((t) => {
    revalidateTag(cacheTagBuilder("learning-collection", { locale, tag: t }));
  });
};

const handleStoryChange = async (payload: IWebhookPayloadOrUnpublished) => {
  const recentVersions = await getStoryVersions<{ component: string }>(
    payload.space_id,
    payload.story_id,
    1,
    10,
  );
  const theOne = recentVersions.find((v) => v.status === payload.action);
  if (theOne) {
    const { component } = theOne.content;
    if (component === "blogPageV2") {
      await handleBlogChange(
        theOne.content as IBlogStoryContent,
        payload.full_slug,
      );
    } else if (component === "learningCollection") {
      await handleLearningCollectionChange(
        theOne.content as ILearningCollectionContent,
        payload.full_slug,
      );
    } else if (component === "blogHomePage") {
      await handleTagHomepageChange(payload.full_slug);
    }
  }
};

const handleDatasourceEntryChange = async (datasource: string) => {
  return revalidateTag(`datasource-entries-${datasource}`);
};

export async function POST(request: NextRequest) {
  const payloadS = await request.text();
  const signature = request.headers.get("webhook-signature");
  if (!signature) {
    return new Response("Invalid signature", { status: 401 });
  }
  const isValid = validateSignature(webhookSecret, payloadS, signature);
  if (!isValid) {
    return new Response("Invalid signature", { status: 401 });
  }
  const payload = JSON.parse(payloadS) as IWebhookPayload;
  if (["published", "unpublished"].includes(payload.action)) {
    await handleStoryChange(payload as IWebhookPayloadOrUnpublished);
  } else if (["entries_updated", "entries_deleted"].includes(payload.action)) {
    await handleDatasourceEntryChange(
      (payload as IWebhookPayloadEntriesEvent).datasource_slug,
    );
  }

  return new Response("OK", { status: 200 });
}
