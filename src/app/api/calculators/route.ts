import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const requestBody = await request.json();

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/marketo`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    const responseText = await response.text();

    if (!response.ok) {
      return NextResponse.json(
        { error: `Marketo API error: ${response.status} - ${responseText}` },
        { status: response.status },
      );
    }

    let data;
    try {
      data = JSON.parse(responseText);
    } catch {
      data = { rawResponse: responseText };
    }

    return NextResponse.json(
      {
        success: true,
        message: "Form submitted successfully",
        data,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log(`Error in form submission:`, error);
    return NextResponse.json(
      {
        error: `Form submission error: ${(error as { message?: string })?.message ?? "Unknown error"}`,
      },
      { status: 500 },
    );
  }
}
