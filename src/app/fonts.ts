import { Bricolage_Grotesque, Lato, Covered_By_Your_<PERSON> } from "next/font/google";
import localFont from "next/font/local";

export const bricolage = Bricolage_Grotesque({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-bricolage",
  weight: ["200", "300", "400", "600", "700"],
  fallback: ["Arial", "sans-serif"],
});

export const lato = Lato({
  weight: ["300", "400", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-lato",
  fallback: ["Arial", "sans-serif"],
});

export const itcGaramond = localFont({
  src: [
    {
      path: "../../public/fonts/itc-garamond/itc-garamond_narrow-light.woff2",
      weight: "300",
      style: "normal",
    },
    {
      path: "../../public/fonts/itc-garamond/itc-garamond_narrow-book.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/itc-garamond/itc-garamond_narrow-light-italic.woff2",
      weight: "300",
      style: "italic",
    },
  ],
  display: "swap",
  variable: "--font-itc-garamond",
  fallback: ["Georgia", "serif"],
});

export const sourceHanSerifCN = localFont({
  src: [
    {
      path: "../../public/fonts/source-han-serif/SourceHanSerifCN-Regular.woff2",
      weight: "400",
      style: "normal",
    },
  ],
  display: "swap",
  variable: "--font-source-han-serif-cn",
  fallback: ["Georgia", "serif"],
});

export const sourceHanSerifJP = localFont({
  src: [
    {
      path: "../../public/fonts/source-han-serif/SourceHanSerifJP-Regular.woff2",
      weight: "400",
      style: "normal",
    },
  ],
  display: "swap",
  variable: "--font-source-han-serif-jp",
  fallback: ["Georgia", "serif"],
});

export const sourceHanSerifKR = localFont({
  src: [
    {
      path: "../../public/fonts/source-han-serif/SourceHanSerifKR-Regular.woff2",
      weight: "400",
      style: "normal",
    },
  ],
  display: "swap",
  variable: "--font-source-han-serif-kr",
  fallback: ["Georgia", "serif"],
});

export const coveredByYourGrace = Covered_By_Your_Grace({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-covered-by-your-grace",
  fallback: ["Georgia"],
});
