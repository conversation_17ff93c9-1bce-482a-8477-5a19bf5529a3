import {
  fetchSiteShellWithFallback,
  fetchStoryBySlug,
  fetchTagPageData,
  isTagPagePath,
  fetchStoryWithRelations,
} from "@/common/storyblok";
import { PageContextProvider } from "@/components/context/PageContext";
import { StoryData } from "@/common/types";
import { StoryblokComponent } from "@storyblok/react/rsc";
import SiteShell from "@/components/bloks/layout/SiteShell";
import { getProfilePathValidationResult } from "@/common/profile/validateProfileSlug";
import { ProfileSlugErrorMessage } from "@/components/ui/ProfilePage/ProfileSlugErrorMessage";
import { headers } from "next/headers";

const draftMode = true;

// https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#dynamic
export const dynamic = "force-dynamic";

type Props = {
  searchParams: Promise<{ path: string }>;
};

async function getFormFieldData() {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/form/data-source/course-interest-list`,
  );
  const data = await res.json();
  return data;
}

const defaultLocale = "us";

export default async function Page({ searchParams }: Props) {
  const { path } = await searchParams;
  const locale = path.split("/")[0] ?? defaultLocale;
  const headersList = await headers();
  const lang = headersList.get("Accept-Language")?.split(",")?.[0] ?? "en";
  const tagPageInfo = isTagPagePath(path);
  const siteShellContent = await fetchSiteShellWithFallback(
    locale,
    draftMode,
    "en",
  );
  try {
    if (tagPageInfo) {
      const { locale, tag } = tagPageInfo;
      const tagPageData = await fetchTagPageData(locale, tag, 1, 10);
      return (
        <PageContextProvider
          values={{
            locale,
            coursesInterestedIn: [],
            isVisualComposer: true,
          }}
        >
          <SiteShell
            locale={locale}
            pageType={"TagPageV2"}
            siteShellContent={siteShellContent}
          >
            <StoryblokComponent
              blok={tagPageData.content}
              siteShellContent={siteShellContent}
              // publishedAt={story.published_at}
              AuthorsList={[]}
              blogTagsList={[]}
            />
          </SiteShell>
        </PageContextProvider>
      );
    }
    const { data } = await fetchStoryBySlug(path, draftMode);
    const { story } = data as StoryData;

    const approvedPageTypes = [
      "WebinarEventPageV2",
      "homePage",
      "blogPageV2",
      "NceaToGpaCacResultPageV2",
      "NceaToGpaCalculatorV2",
      "infoPage",
      "ALevelIBToGpaCalculatorV2",
      "ALevelIBToGpaCalculatorResultsV2",
      "UshToGpaCalculatorV2",
      "UshToGpaResultPageV2",
      "profilePage",
      "Next-demo-our-page",
      "generalpageV2",
      "campaignptcpage",
      "caseStudyPage",
      "ebookPage",
    ];

    if (!approvedPageTypes.includes(story?.content?.component)) {
      return (
        <div className="container mx-auto w-full px-4 py-24 text-center text-3xl">
          <p className="text-red-500">
            {`This is not a NextJS page.  Please change the Preview URL to " - Default (https://www.crimsoneducation.org/visual-composer/) to preview the page layout.`}
          </p>
        </div>
      );
    }

    const pathSegments = path.split("/").filter(Boolean);
    const isValidEventsPath =
      pathSegments[1] === "events" && pathSegments.length === 3;

    if (
      !isValidEventsPath &&
      story?.content?.component === "WebinarEventPageV2"
    ) {
      return (
        <div className="container mx-auto w-full px-4 py-24 text-center text-3xl">
          <p className="text-red-500">
            {`EventPageV2 only renders in "{locale}/events" folder, and not in any subfolders inside "events".  Please move this story to the appropriate folder.`}
          </p>
        </div>
      );
    }

    if (story?.content?.component === "profilePage") {
      const result = getProfilePathValidationResult(story);
      if (!result.valid) {
        return (
          <ProfileSlugErrorMessage
            typeList={result.typeList}
            reason={result.reason}
            slug={result.slug}
          />
        );
      }
    }

    // All event pages have forms, so lets pre-fetch the data needed for the primary interest and school prioritisation fields
    const formFieldData = getFormFieldData();

    const [coursesInterestedIn] = await Promise.all([formFieldData]);

    const pageContext = {
      locale,
      coursesInterestedIn,
      isVisualComposer: true,
      lang: lang.toLowerCase(),
    };

    return (
      <PageContextProvider values={pageContext}>
        <SiteShell
          locale={locale}
          pageType={story.content?.component}
          siteShellContent={siteShellContent}
        >
          <StoryblokComponent
            blok={story.content}
            story={story}
            siteShellContent={siteShellContent}
            locale={locale}
            slug={path}
            publishedAt={story.published_at}
            fullSlug={story.full_slug}
          />
        </SiteShell>
      </PageContextProvider>
    );
  } catch (err: any) {
    return (
      <>
        <p>PATH: {path}</p>
        <p>ERROR: {err}</p>
      </>
    );
  }
}
