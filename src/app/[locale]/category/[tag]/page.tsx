import { fetchStoryBySlug, fetchTagPageData } from "@/common/storyblok";
import SiteShell from "@/components/bloks/layout/SiteShell";
import { PageContextProvider } from "@/components/context/PageContext";
import TagPage from "@/components/pages/TagPage";

export interface ITagPageParams {
  locale: string;
  tag: string;
}

export const dynamicParams = true;

export const revalidate = 3600;

// SEO -- Metadata (TODO : add metadata for tag pages)

export default async function Page({
  params,
}: {
  params: Promise<ITagPageParams>;
}) {
  const { locale, tag } = await params;
  const pageContext = {
    locale,
    coursesInterestedIn: [],
  };
  const story = await fetchTagPageData(locale, tag, 1, 15);
  const { content } = story;


  const SiteShell_slug = `${locale}/site-shell`;
  let _data: { story?: { content?: any } } | null = (
    await fetchStoryBySlug(
      SiteShell_slug,
      process.env.NEXT_PUBLIC_BUILD_ENV === "prod" ? false : true,
    )
  ).data;
  _data = (await fetchStoryBySlug(`en/site-shell`, true)).data;
  const siteShellContent = _data?.story?.content ?? null;
  return (
    <PageContextProvider values={pageContext}>
      <SiteShell
        locale={locale}
        pageType={content?.component}
        siteShellContent={siteShellContent}
      >
        <div className="mx-auto">
          <TagPage blok={content} locale={locale} />
        </div>
      </SiteShell>
    </PageContextProvider>
  );
}
