import { HydrateClient } from "@/trpc/server";
import { TRPCReactProvider } from "@/trpc/react";
import Button from "@/components/ui/Button";
import Text from "@/components/ui/Text";
import Container from "@/components/ui/Container";

export default async function Home() {
  return (
    <TRPCReactProvider>
      <HydrateClient>
        <main className="flex min-h-screen flex-col items-center justify-center bg-white pb-16 text-grey-900">
          <div className="container flex flex-col items-center justify-center gap-12 px-4 py-16">
            <h1 className="text-5xl font-extrabold tracking-tight sm:text-[5rem]">
              Hello, <span className="text-primary01-75">Crimson!</span>
            </h1>
            <p className="max-w-2xl text-center text-xl text-grey-500">
              Explore our design system featuring comprehensive UI components.
              Below you'll find our typography system with Bricolage, ITC
              Garamond, and Lato fonts, button variants with multiple themes and
              colors, and standardized text styles for consistent visual
              hierarchy.
            </p>
          </div>

          {/* Container */}
          <div>
            <Container>
              <h1 className="mb-8 text-4xl">Container Usage</h1>

              <p className="mb-8">
                This is our container component that maintains a max-width of
                1440px and applies responsive padding based on screen size. On
                mobile devices, it has 25px padding, on medium and large screens
                it increases to 30px padding, from XL to 2XL screens it expands
                to 75px padding, and at 3XL screen sizes the padding is removed
                (0px).
              </p>

              {/* Create a debug line to show the padding and max width */}
              <div className="w-full text-center text-white">
                <span className="block bg-red-800 p-4 text-2xl md:hidden">
                  <p>Padding: 25px</p>
                </span>
                <span className="hidden w-full bg-grays-G2 p-4 text-2xl md:block xl:hidden">
                  <p>Padding: 30px</p>
                </span>
                <span className="hidden bg-blue-600 p-4 text-2xl xl:block 3xl:hidden">
                  <p>Padding: 75px</p>
                  <p>Max Width: 1440px</p>
                </span>
                <span className="hidden bg-green-900 p-4 text-2xl 3xl:block">
                  <p>Padding: 0px</p>
                  <p>Max Width: 1440px</p>
                </span>
              </div>
            </Container>
          </div>

          {/* Typography */}
          <div className="mt-8">
            <h1 className="mb-8 text-4xl">Typography</h1>
            {/* h1 to h5 */}
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              <h2 className="text-2xl font-bold underline">
                Headings - h1 to h5
              </h2>
              <Text tag="h1" style="h1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="h2" style="h2">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="h3" style="h3">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="h4" style="h4">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="h5" style="h5">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              {/* sh1 to sh5 */}
              <h2 className="text-2xl font-bold underline">
                Subheadings (sh1 to sh5)
              </h2>
              <Text tag="p" style="sh1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="sh2">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="sh3">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="sh4">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="sh5">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              <h2 className="text-2xl font-bold underline">
                Pre-heading (ph1 to ph2)
              </h2>
              {/* ph1 to ph2 */}
              <Text tag="p" style="ph1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="ph2">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              {/* q1 to q5 */}
              <h2 className="text-2xl font-bold underline">Quote (q1 to q6)</h2>
              <Text tag="p" style="q1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="q2">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="q3">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="q4">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="q5" className="italic">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="q6">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              {/* n1 to n2 */}
              <h2 className="text-2xl font-bold underline">Body (n1 to n2)</h2>
              <Text tag="p" style="n1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="n2">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              {/* a1 to a2 */}
              <h2 className="text-2xl font-bold underline">Body (a1 to a2)</h2>
              <Text tag="p" style="a1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="a2">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              <h2 className="text-2xl font-bold underline">Body (b1 to b4)</h2>
              <Text tag="p" style="b1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="b2">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="b3">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="b4">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              {/* c1 */}
              <h2 className="text-2xl font-bold underline">Caption (c1)</h2>
              <Text tag="p" style="c1">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              {/* button */}
              <h2 className="text-2xl font-bold underline">Button (button)</h2>
              <Text tag="p" style="button">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              {/* t1 to t2 */}
              <h2 className="text-2xl font-bold underline">Text (t1 to t2)</h2>
              <Text tag="p" style="t1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="t2">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              <h2 className="text-2xl font-bold underline">
                Handwritten (hw1 to hw5)
              </h2>
              <Text tag="p" style="hw1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="hw2">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="hw3">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="hw4">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="hw5">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
            <div className="my-6 flex flex-col gap-8 border-t border-red-500 py-12">
              {/* Mobile Headings */}
              <h2 className="text-2xl font-bold underline">
                Mobile Headings (mh1 to mb1)
              </h2>
              <Text tag="p" style="mh1">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="mh1.5">
                The quick brown fox jumps over the lazy dog
              </Text>
              <Text tag="p" style="mb1">
                The quick brown fox jumps over the lazy dog
              </Text>
            </div>
          </div>

          {/* Buttons */}
          <div className="mt-8">
            <h1 className="mb-8 text-4xl">BUTTONS (Updated)</h1>
            <div>
              <h3>Primary</h3>
              <div className="flex gap-4 bg-grays-G5 p-4">
                <Button colour="red" theme="primary">
                  Lorem ipsum
                </Button>
                <Button colour="maroon" theme="primary">
                  Lorem ipsum
                </Button>
                <Button colour="navy" theme="primary">
                  Lorem ipsum
                </Button>

                <Button colour="white" theme="primary">
                  Lorem ipsum
                </Button>
              </div>
            </div>

            <div>
              <h3>Secondary</h3>
              <div className="flex gap-4 bg-grays-G5 p-4">
                <Button colour="red" theme="secondary">
                  Lorem ipsum
                </Button>
                <Button colour="maroon" theme="secondary">
                  Lorem ipsum
                </Button>
                <Button colour="navy" theme="secondary">
                  Lorem ipsum
                </Button>

                <Button colour="white" theme="secondary">
                  Lorem ipsum
                </Button>
              </div>
            </div>

            <div>
              <h3>Disabled</h3>
              <div className="flex gap-4 bg-grays-G1 p-4">
                <Button colour="red" theme="primary" disabled>
                  Lorem ipsum
                </Button>
                <Button colour="maroon" theme="secondary" disabled>
                  Lorem ipsum
                </Button>
              </div>
            </div>
          </div>
        </main>
      </HydrateClient>
    </TRPCReactProvider>
  );
}
