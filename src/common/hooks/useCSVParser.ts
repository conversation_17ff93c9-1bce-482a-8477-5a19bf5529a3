import { useState, useEffect } from "react";
import { readRemoteFile } from "react-papaparse";

type CSVRow = Record<string, string | number | boolean | null>;

interface CSVParseResult {
  data: CSVRow[];
  errors: any[];
  meta: object;
}

const useCSVParser = (path?: string): CSVRow[] | undefined => {
  const [parsedCSVData, setParsedCSVData] = useState<CSVParseResult | null>(
    null,
  );

  useEffect(() => {
    if (path) {
      readRemoteFile<CSVRow>(path, {
        download: true,
        header: true,
        complete: (results: CSVParseResult) => {
          setParsedCSVData(results);
        },
      });
    }
  }, [path]);

  return parsedCSVData ? parsedCSVData.data : undefined;
};

export default useCSVParser;
