import { useState, useEffect } from "react";
import { fetchDataSource } from "../storyblok";
import { ISbResult } from "@storyblok/react/rsc";

interface SbDataValue extends ISbResult {
  data: {
    datasource_entries?: {
      dimension_value?: string;
      id: number;
      name: string;
      value: string;
    }[];
  };
}

export function useStoryblokDataSource(
  slug: string,
  dimension?: string,
): {
  data: SbDataValue | null;
  loading: boolean;
  error: unknown;
} {
  const [data, setData] = useState<SbDataValue | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<unknown>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await fetchDataSource(slug, false, dimension);
        setData(result as SbDataValue);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    void fetchData();
  }, [slug, dimension]);

  return { data, loading, error };
}
