import { useEffect } from "react";
import Cookies from "js-cookie";

export const useMetaPageView = () => {
  useEffect(() => {
    const fetchCountry = async () => {
      try {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        let userLocation = Cookies.get("userCountry");

        if (!userLocation) {
          await fetch("https://api.country.is/")
            .then((response) => response.json())
            .then((data) => {
              // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
              userLocation = data?.country;

              // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
              Cookies.set("userCountry", userLocation ?? "US", {
                expires: 1,
              });
            })
            .catch((error) => {
              console.error("Error fetching country:", error);
            });
        }

        return userLocation;
      } catch (error) {
        console.error("Error in fetchCountry:", error);
        return "US";
      }
    };

    void fetchCountry();
  }, []);
};
