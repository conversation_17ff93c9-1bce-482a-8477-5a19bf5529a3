"use client";

import { useMemo } from "react";
import { useSearchParams } from "next/navigation";

// Store cookies for 30 days
const COOKIE_DURATION_DAYS = 30 * 24 * 60 * 60;

const useCaptureUtmParameters = () => {
  const searchParams = useSearchParams();

  useMemo(() => {
    if (typeof window !== `undefined`) {
      const utmParameters = [
        "utm_campaign",
        "utm_medium",
        "utm_source",
        "utm_term",
        "gclid",
        "utm_content",
        "fbclid",
      ];
      utmParameters.forEach((parameter) => {
        const queryParam = searchParams.get(parameter);
        if (queryParam)
          document.cookie = `x-crimson-${parameter}=${queryParam}; path=/; max-age=${COOKIE_DURATION_DAYS}`;
      });
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps
};

export default useCaptureUtmParameters;
