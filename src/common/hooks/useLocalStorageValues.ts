import { IFormValues } from "@/components/ui/Forms/types";
import { IUserFormAttributes } from "@/components/ui/Forms/utils/getMarketoActivityData";
import { useEffect, useState } from "react";

type SetLocalStorageValue<T> = (newValue: T) => void;

export const useLocalStorageValues = (
  key: string,
  defaultValues: IFormValues,
  keysToExclude: string[],
): [IFormValues, SetLocalStorageValue<IFormValues>] => {
  const isWindowObjectAvailable = typeof window !== "undefined" && localStorage;
  const itemsInStorage =
    typeof window !== "undefined" ? (localStorage?.getItem(key) ?? "") : "";

  const parsedLocalStorage: IFormValues | undefined =
    isWindowObjectAvailable && itemsInStorage
      ? JSON.parse(itemsInStorage)
      : undefined;

  if (keysToExclude && parsedLocalStorage) {
    keysToExclude.forEach((keyToExclude) => {
      delete parsedLocalStorage[keyToExclude];
    });
  }

  const defaultValue: IFormValues = parsedLocalStorage
    ? { ...defaultValues, ...parsedLocalStorage }
    : defaultValues;
  const [value, setValue] = useState<IFormValues>(defaultValue);

  const setLocalStorageValue: SetLocalStorageValue<IFormValues> = (
    newValue,
  ) => {
    localStorage.setItem(key, JSON.stringify(newValue));
    setValue(newValue);
  };

  return [value, setLocalStorageValue];
};

/**
 * useLocalStorage hook that deals with all types of inputs.
 * taken from https://usehooks.com/useLocalStorage/
 */
export const useLocalStorage = (
  key: string,
  defaultValue: IUserFormAttributes,
): [IUserFormAttributes, (value: IUserFormAttributes) => void] => {
  // State to store our value
  const [storedValue, setStoredValue] = useState(defaultValue);

  // Delay reading local storage values to avoid rehydration issue.
  useEffect(() => {
    try {
      // Get from local storage by key
      const item = window.localStorage.getItem(key)!;
      // Parse stored json or if none return defaultValue
      const valueToStore: IUserFormAttributes = item
        ? JSON.parse(item)
        : defaultValue;

      setStoredValue(valueToStore);
    } catch (error) {
      // If error also return defaultValue
      console.error(error);
      setStoredValue(defaultValue);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Return a wrapped version of useState's setter function that ...
  // ... persists the new value to localStorage.
  const setValue = (value: IUserFormAttributes) => {
    try {
      // Allow value to be a function so we have same API as useState
      const valueToStore: IUserFormAttributes =
        value instanceof Function ? value(storedValue) : value;
      // Save state
      setStoredValue(valueToStore);
      // Save to local storage
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      // A more advanced implementation would handle the error case
      console.error(error);
    }
  };
  return [storedValue, setValue];
};
