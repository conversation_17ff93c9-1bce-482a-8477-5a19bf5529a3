import { useEffect } from "react";

const useScrollSpy = (options: {
  ids: string[];
  offset: number;
  onSectionChange: (id: string) => void;
}) => {
  useEffect(() => {
    if (!options.ids || options.ids.length === 0) return;

    const handleScroll = () => {
      const { ids, offset, onSectionChange } = options;
      let currentId: string | null = null;

      for (const id of ids.filter(Boolean)) {
        const el = document.getElementById(id);
        if (!el) continue;

        const rect = el.getBoundingClientRect();
        const top = rect.top - offset;

        if (top <= 0 && rect.bottom > 0) {
          currentId = id;
        }
      }

      if (currentId) {
        onSectionChange(currentId);
        return;
      }

      const scrollY = window.scrollY || window.pageYOffset;
      const viewportHeight = window.innerHeight;
      const fullHeight = document.documentElement.scrollHeight;

      const isNearBottom =
        scrollY + viewportHeight >= fullHeight - 10 &&
        fullHeight - (scrollY + viewportHeight) < 150;

      if (isNearBottom) {
        const lastId = ids[ids.length - 1];
        if (lastId) {
          onSectionChange(lastId);
        }
      }
    };

    document.addEventListener("scroll", handleScroll, { passive: true });
    window.addEventListener("resize", handleScroll);
    handleScroll();

    return () => {
      document.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, [options]);
};

export default useScrollSpy;