"use client";

import { useMediaQuery } from "@uidotdev/usehooks";

export const useDevice = () => {
  const isMobile = useMediaQuery("(max-width: 767px)") ?? false;
  const isTablet =
    useMediaQuery("(min-width: 768px) and (max-width: 1023px)") ?? false;
  const isDesktop =
    useMediaQuery("(min-width: 1024px) and (max-width: 1224px)") ?? false;
  const isXL =
    useMediaQuery("(min-width: 1225px) and (max-width: 1439px)") ?? false;
  const is2XL = useMediaQuery("(min-width: 1440px)") ?? false;

  return {
    isMobile,
    isTablet,
    isDesktop,
    isXL,
    is2XL,
    // Convenience flags
    isAboveMobile: !isMobile,
    isAboveTablet: isDesktop || isXL || is2XL,
    isAboveDesktop: isXL || is2XL,
    isAboveXL: is2XL,
  };
};
