import { useMemo } from "react";

const defaultFormComponentNames = [
  "formBuilder",
  "sideImageFormSection",
  "sideTextImageFormSection",
  "sideTextImageGridFormSection",
];

/**
 * Counts the number of form components in a sections array.
 * @param sections The array of section objects (each should have a `component` property)
 * @param formComponentNames Optional array of component names to count as forms
 * @returns The number of form components found
 */
export function useFormCount(
  sections: Array<{ component?: string }> = [],
  formComponentNames: string[] = defaultFormComponentNames,
): number {
  return useMemo(() => {
    return (
      sections?.filter(
        (section) =>
          section.component && formComponentNames.includes(section.component),
      ).length || 0
    );
  }, [sections, formComponentNames]);
}
