import rawTranslations from "@/common/data/label_translations.json";
import { usePageContext } from "@/components/context/PageContext";
import { useEffect, useState } from "react";

type Translations = Record<string, Record<string, string>>;
const labelTranslations = rawTranslations as Translations;

const languageMap: Record<string, string> = {
  zh: "zh-cn",
  "zh-hk": "zh-tw",
  "pt-br": "pt",
  "pt-pt": "pt",
};

const normalizeLang = (lang: string | null | undefined): string => {
  if (!lang) return "en";
  const lower = lang.toLowerCase();
  return languageMap[lower] ?? lower;
};

// Translation cache
const translationCache = new Map<string, string>();

/**
 * Resolves a label translation with caching.
 * @param label - The translation key
 * @param lang - Normalized language code (e.g. zh-cn)
 * @returns Translated label or fallback
 */
function resolveLabel(label: string, lang: string): string {
  const cacheKey = `${lang}:${label}`;
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey)!;
  }

  const resolved =
    labelTranslations[label]?.[lang] ?? labelTranslations[label]?.en ?? label;

  translationCache.set(cacheKey, resolved);
  return resolved;
}

/**
 * React hook for client-side label translation
 * based on the user's browser language.
 */
export const useLabelTranslation = () => {
  const { lang: serverLang = "en" } = usePageContext();
  const [lang, setLang] = useState<string>(serverLang);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (typeof window !== "undefined" && typeof navigator !== "undefined") {
      setLang(normalizeLang(navigator.language));
    }
    setLoading(false);
  }, []);

  const t = (label: string): string => {
    return resolveLabel(label, lang);
  };

  return { t, isLoading: loading };
};

/**
 * Server-side label translation function
 * based on provided language code.
 */
export const getServerLableTTranslation = (lang: string) => {
  const normalized = normalizeLang(lang);
  return (label: string): string => {
    return resolveLabel(label, normalized);
  };
};
