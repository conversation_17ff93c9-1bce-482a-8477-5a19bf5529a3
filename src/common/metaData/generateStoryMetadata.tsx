import type { Metadata } from "next";
import { siteUrl } from "@/common/constants";
import { IStoryblokAssetProps, StoryData } from "@/common/types";
import { IBlogStoryContent } from "@/components/bloks/NewsAndArticles/utils";
import { fetchStoryBySlug } from "@/common/storyblok";
import { generateImageAltFromFilename, getShareImage } from "@/common/utils";
import { console } from "inspector";
import { componentStrategies } from "./componentStrategies";

const draftMode = process.env.NEXT_PUBLIC_BUILD_ENV === "prod" ? false : true;


export async function generateStoryMetadata(slug: string): Promise<Metadata> {
    try {
      const { data } = await fetchStoryBySlug(slug, draftMode);
      const { story } = data as StoryData<IBlogStoryContent>;
      if (!story) return { metadataBase: new URL(siteUrl) };
  
      const { content, full_slug } = story;
      const component = content.component;
  
      const shareImageSource =
        componentStrategies[component]?.shareImage?.(content) ??
        content?.shareImage;
  
      const shareImageData = resolveShareImage(shareImageSource);
  
      shareImageData.alt = componentStrategies[component]?.alt?.(
        story.content,
        shareImageData.alt
      ) ?? shareImageData.alt;
  

      const canonicalUrl = resolveCanonicalUrl(content?.canonicalUrl, full_slug);
  
      const pageTitle = content.pageTitle ?? content.title ?? story.name;
      const metaDescription = content.metaDescription ?? content.subtitle;
      const keywords = content?.keywords;
  
      return {
        metadataBase: new URL(siteUrl),
        title: pageTitle,
        description: metaDescription,
        keywords,
        openGraph: {
          title: pageTitle,
          description: metaDescription,
          type: "website",
          url: canonicalUrl,
          images: [shareImageData],
        },
        twitter: {
          card: "summary_large_image",
          title: pageTitle,
          description: metaDescription,
          images: [shareImageData],
        },
        other: {
          robots: content?.hideFromGoogle ? "noindex" : "all",
        },
        alternates: {
          canonical: canonicalUrl,
        },
      };
    } catch (err) {
      console.error(err);
      return { metadataBase: new URL(siteUrl) };
    }
  }

// hundle share image
export function resolveShareImage(
    shareImage?: string | IStoryblokAssetProps | null,
    defaultShareImage = `${siteUrl}/images/crimson-logo.svg`,
) {
    const url = getShareImage(shareImage ?? null) || defaultShareImage;

    const alt =
        typeof shareImage === "object" &&
            shareImage !== null &&
            "alt" in shareImage &&
            shareImage.alt?.trim()
            ? shareImage.alt
            : generateImageAltFromFilename(url);

    return {
        url,
        width: 1200,
        height: 630,
        alt,
    };
}


// hundle canonical link
function resolveCanonicalUrl(
    canonicalUrl: string | undefined,
    fullSlug: string,
): string {
    return canonicalUrl?.trim() && canonicalUrl.trim().length > 0
        ? canonicalUrl.trim()
        : `${siteUrl}/${fullSlug}`;
}

