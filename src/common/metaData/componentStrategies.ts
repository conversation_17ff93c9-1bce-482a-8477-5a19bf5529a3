import { IStoryblokAssetProps } from "@/common/types";
import { generateImageAltFromFilename } from "@/common/utils";

type ShareImageResolver = (
  content: any,
) => string | IStoryblokAssetProps | null | undefined;
type AltResolver = (storyContent: any, currentAlt: string) => string;

interface ComponentStrategy {
  shareImage?: ShareImageResolver;
  alt?: AltResolver;
}

export const componentStrategies: Record<string, ComponentStrategy> = {
  WebinarEventPageV2: {
    alt: (storyContent, currentAlt) => {
      // TODO: Need to revisit this and remove blog specificity - christiane
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      const eventHeader = storyContent?.eventHeader ?? [];
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      if (eventHeader.length > 0) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        const thumbnailImage = eventHeader[0]?.thumbnailImage?.[0]?.image;
        return generateImageAltFromFilename(
          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
          thumbnailImage ?? "Webinar Event Header",
        );
      }
      return currentAlt;
    },
  },
};
