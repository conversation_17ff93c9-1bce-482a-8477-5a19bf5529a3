export function getResponsiveImageUrl(
  url: string,
  width?: number,
  height?: number,
  options?: string,
): string {
  if (!url) return "";

  if (url.includes("/m/")) return url;
  if (!url.includes("a.storyblok.com")) return url;

  const imageWidth = width ?? 0;

  let path = `m/${imageWidth}x${height}`;

  // Add filters if provided, but default to 75% quality if not
  if (options) {
    path += `/filters:${options}`;
  } else {
    path += `/filters:quality(75)`;
  }

  const urlParts = url.split("/");
  const filename = urlParts.pop();

  if (!filename) return url;

  return [...urlParts, filename, path].join("/");
}
