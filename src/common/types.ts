/* eslint-disable @typescript-eslint/no-empty-object-type */
import { ISbResult } from "@storyblok/react/rsc";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";
import {
  ILearningCollectionStory,
  IPopulatedLearningCollectionStory,
} from "@/components/articlePage/types";
import { ICaseStudySection } from "@/components/bloks/CaseStudiesSection/types";


export type TButtonColour =
  | "navy"
  | "charcoal"
  | "indigo"
  | "mulberry"
  | "salmon"
  | "yellow"
  | "white"
  | "salmonCoral" // Salmon & Coral
  | "orangeYellow" // Orange & Yellow
  | "navyIndigo" // Navy & Indigo
  | "black"; // Black

export type TButtonAppearance = "solid" | "outline";

export type Theme = "light" | "dark";

export type TBackgroundColour =
  | "navy"
  | "charcoal"
  | "indigo"
  | "salmon"
  | "mulberry"
  | "white"
  | "yellow"
  | "whisper"
  | "mist";

interface IStudentGuardianField {
  label?: string;
  marketoField: string;
  plugin: string;
  _uid: string;
}

export interface IField {
  _uid: string;
  label: string;
  component: string;
  placeholder?: string;
  validation?: string[];
  removeFieldAndSetValue?: string;
  name?: string;
  defaultPhoneCountryCode?: string;
  guardianField: IStudentGuardianField;
  studentField: IStudentGuardianField;
  guardianValidation: string[];
  studentValidation: string[];
}

export interface IStoryblokAssetProps {
  alt: string;
  copyright?: string;
  fieldtype?: string;
  filename: string;
  focus?: string;
  id?: number;
  is_external_url?: boolean;
  meta_data?: object;
  name?: string;
  source?: string;
  title?: string;
}

export interface IStoryblokLink {
  url: string;
  linktype: string;
  cached_url: string;
  onClick?: () => void;
  newTab?: boolean;
  target?: string;
  story?: {
    name: string;
    id: number;
    uuid: string;
    slug: string;
    full_slug: string;
    url: string;
  };
}

export type IStoryblokLinkProps = {
  link: IStoryblokLink;
  newTab?: boolean;
  component?: string;
  targetAnchorId?: string;
};

export interface ICtaButtonProps {
  colour?: TButtonColour;
  appearance?: TButtonAppearance;
  link: IStoryblokLink[];
  text: string;
  _uid: string;
  component: string;
  gtmTrigger?: string;
}

export interface IFieldSection {
  _uid: string;
  fields: IField[];
  header?: string;
}

export interface ISubmissionDetails {
  nextButtonLabel?: string;
  prevButtonLabel?: string;
  submitButtonLabel?: string;
  submittingLabel?: string;
  successPage?: IStoryblokLinkProps[];
  submitButtonColour?: TButtonColour;
  submitButtonAppearance?: TButtonAppearance;
}
export interface IForm {
  type: string;
  submissionDetails: ISubmissionDetails[];
  fieldSections: IFieldSection[];
  numberErrorMessage: string;
  emailErrorMessage: string;
  isRequiredErrorMessage: string;
  countryCodeErrorMessage: string;
  marketoProgrammeName: string;
  fbEvent: string;
  enableABTest?: boolean;
  abTestType?: string;
  theme?: Theme;
  anchorId?: string;
  dynamicErrorLabel?: string;
  isInvalidErrorMessage?: string;
}

export interface ISubmissionParams {
  fbEvent: string;
  marketoProgrammeName: string;
  successPage: IStoryblokLinkProps[];
  reason?: string;
  fbEventIdPrefix?: string;
  disableRememberMeOptIn?: boolean;
}

export interface IQueryOptions {
  uuids?: string[];
  slug?: string;
  sort_by?: "recent" | "published_at";
  starts_with?: string;
  filter_query?: Record<string, any>;
  per_page?: number;
  draftMode?: boolean;
  content_type?: string;
  page?: number;
}

export interface IDropdownOption {
  _uid: string;
  label: string;
  plugin: string;
  marketoField: string;
}

export interface IAutocompleteOption {
  label: string | undefined;
  value: string | string[] | number | number[];
}

export type TReactSelectStyles = Record<string, string | number | object>;

export interface IStandardDataSource {
  dimension_value?: string;
  id: number;
  name: string;
  value: string;
}
export interface IStudent {
  college: string;
  classYear: string;
  name: string;
  image: IStoryblokAssetProps;
}

export type SectionItem = {
  _uid?: string;
  theme?: Theme;
  anchorId: string;
  component?: string;
  bodyContent?: string;
  callToAction?: any[];
  stickyNavTitle?: string;
  backgroundImage?: string;
  backgroundColour?: TBackgroundColour;
  flipBackgroundImage?: boolean;
  colour?: TButtonColour;
  appearance?: ButtonTheme;
  content?: any[];
  children?: RichEditorChildren[];
};

export type RichEditorChildren = {
  type?: string;
  text?: string;
  id?: string;
  children?: RichEditorChildren[];
  stickyNavTitle?: string;
};

export type SmallPrintLink = {
  text: string;
  component: string;
  link: [
    {
      link: {
        cached_url: string;
      };
      newTab: boolean;
    },
  ];
};

export interface IStoryContent {
  items: never[];
  title: string;
  component: string;
  pageTitle: string;
  subtitle: string;
  metaDescription: string;
  keywords: string;
  shareImage?: string;
  hideFromGoogle?: boolean;
  excludeCTA?: boolean;
  canonicalUrl?: string;
  smallPrints?: SmallPrintLink[];
  caseStudySection?: ICaseStudySection[];
  titleImage?: string;
  headerCallToAction: [
    {
      colour: TButtonColour;
      appearance: TButtonAppearance;
      link: any[];
      text: string;
    },
  ];
  removeHeaderLogoLink: boolean;
  author: any;
  Authors?: any;
  persona?: string;
  blogTags: blogTags;
  Type?: string[];
  BlogTagV2?: blogTags;
  hideNavFooter?: boolean;
  navLogoOverride?: string | null;
}

export interface IRelatedTagGroup {
  primary_tag: ITag[];
  related_tags: ITag[];
}

export interface IBlogTagTrending {
  Tag: ITag[];
  blogs: string[];
}

export interface IBlogHomePageTagInfo {
  includingTags?: ITag[];
  relatedTagGroups: IRelatedTagGroup[];
  trending: IBlogTagTrending[];
}

export interface IBlogHomePageContent
  extends IStoryContent,
    IBlogHomePageTagInfo {}

export type IBlogHomePageStory = IStory<IBlogHomePageContent>;

export interface IStory<T = IStoryContent> {
  created_at: string;
  content: T;
  published_at: string;
  full_slug: string;
  uuid: string;
  id: number;
  name: string;
  shareImage: string;
  slug: string;
}

export interface StoryData<T = IStoryContent> {
  story: IStory<T>;
}

export interface IMentorProfile {
  Awards: string;
  Byline: string;
  CrimsonRole: string;
  Followers: string;
  LargeBiography: string;
  LinkedinProfile: {
    id: string;
    url: string;
    linktype: string;
    fieldtype: string;
    cached_url: string;
  };
  Name: string;
  ProfilePicture: {
    id: number;
    alt: string;
    name: string;
    focus: string;
    title: string;
    fieldtype: string;
    filename: string;
  };
  SmallBiography: string;
  Specialties: "Classics\nInternational Admissions\nLatin\nEnglish\nHistory";
  Type: string[];
  Universities: string;
  component: string;
}

export type Authors = {
  AuthorPage: string;
  _uid: string;
  Persona: string;
  component: string;
};

export type AuthorsList = {
  name: string;
  uuid: string;
  slug: string;
  full_slug: string;
  mappedPersona?: string;
  content: {
    name?: string;
    school?: string;
    image: {
      image: string;
      altText: string;
    }[];
  };
}[];

export interface IPsudoTagPageContent {
  primaryTag: ITag & { name: string };
  relatedTags: string[];
  component: "TagPageV2";
  articles: {
    stories: IBlogStory[];
    total: number;
    pageSize: number;
  };
  trendingArticles: IBlogStory[];
  learningCollections: IPopulatedLearningCollectionStory[];
}

export interface IPsudoTagPage {
  content: IPsudoTagPageContent;
}

export interface ITag {
  tag: string;
  description: string;
  component?: string;
}

export interface IArticle {
  title: string;
  image: string;
  published_at: string;
}

export type blogTags = ITag[];

export type ButtonColour = "red" | "white" | "navy" | "maroon" | "darkGrey";

export enum DeviceType {
  Mobile = "mobile",
  Tablet = "tablet",
  Desktop = "desktop",
}

export type ButtonTheme = "primary" | "secondary" | "icon" | "link1" | "link2";
export interface ISbDataSourceResult extends ISbResult {
  data: {
    datasource_entries?: ISbDataSourceEntry[];
  };
}

export interface ISbDataSourceEntry {
  dimension_value?: string;
  id: number;
  name: string;
  value: string;
}

export interface IProfileStoryContent {
  Name: string;
  Type: string[];
  Awards: string;
  Byline: string;
  CrimsonRole: string;
  component: string;
  Specialties: string;
  Universities: string;
  SmallBiography: string;
  ProfilePicture: ProfilePicture;
  LinkedinProfile: LinkedinProfile;
  LargeBiography: string;
}

export interface IProfileStory extends IStory<IProfileStoryContent> {}

export type ProfilePicture = {
  id: number;
  alt: string;
  name: string;
  title: string;
  filename: string;
  is_external_url?: boolean;
  meta_data?: {
    title?: string;
    alt?: string;
  };
};

export type LinkedinProfile = {
  id: string;
  url: string;
  linktype: string;
  fieldtype: string;
  cached_url: string;
};
export interface ILearningCollectionContent {
  tags: ITag[];
  learningCollectionEditor?: string;
  items: string[];
  title: string;
  cover_image: {
    id: number;
    filename: string;
  };
  Description?: string;
  component: string;
}

export interface IPopulatedLearningCollectionContent
  extends Omit<ILearningCollectionContent, "items"> {
  items: IBlogStory[];
}

export interface ILearningCollectionInfoResponse {
  blogs: IBlogStory[];
  learningCollection: ILearningCollectionStory[];
}

export interface ITagHomePageResponse {
  includingTags: ITag[];
  relatedTagGroups: IRelatedTagGroup[];
  foundTrending?: IBlogTagTrending;
  relatedBlogs: IBlogStory[];
}

export interface IStoryVersion<T> {
  id: number;
  created_at: string;
  user_id: number;
  user: string;
  story_id: number;
  meta_data: Record<string, any>;
  status: "published" | "unpublished" | "draft";
  content: T;
}
