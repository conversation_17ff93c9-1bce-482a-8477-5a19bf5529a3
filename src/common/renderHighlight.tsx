export const highlightText = (text: string, className: string) => {
  if (typeof text !== 'string' || !text) return null;
  const parts = text?.split(/(`[^`]+`)/g); // Split by backtick groups

  return parts.map((part, i) => {
    if (part.startsWith("`") && part.endsWith("`")) {
      const content = part.slice(1, -1); // Remove backticks
      return (
        <span key={i} className={className}>
          {content}
        </span>
      );
    }

    return part;
  });
};
