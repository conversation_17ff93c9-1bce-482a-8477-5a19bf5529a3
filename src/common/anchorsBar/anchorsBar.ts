import { SbBlokData } from "@storyblok/react/rsc";

export interface AnchorData {
  id: string;
  title: string;
}

export function extractValidAnchors(sections: SbBlokData[]): AnchorData[] {
  const anchors: AnchorData[] = [];

  for (const section of sections) {
    const { anchorId, anchorTitle } = section;

    if (
      typeof anchorTitle === "string" &&
      anchorTitle.trim() !== "" &&
      typeof anchorId === "string" &&
      anchorId.trim() !== ""
    ) {
      anchors.push({
        id: anchorId.trim(),
        title: anchorTitle.trim(),
      });
    }
  }

  return anchors;
}

export function getDuplicateAnchors(anchors: AnchorData[]): AnchorData[] {
  const countMap = new Map<string, number>();

  for (const { id } of anchors) {
    countMap.set(id, (countMap.get(id) ?? 0) + 1);
  }

  return anchors.filter((a) => countMap.get(a.id)! > 1);
}
