import { useWindowScroll } from "@uidotdev/usehooks";
import { useEffect, useRef, useState } from "react";

export const useAnchorsBarSticky = () => {
  const [direction, setDirection] = useState<"up" | "down">("up");
  const [stickyTop, setStickyTop] = useState(75);
  const lastScrollY = useRef(0);
  const [{ y: scrollY }] = useWindowScroll();

  useEffect(() => {
    if (scrollY == null) return;

    const delta = scrollY - lastScrollY.current;
    if (Math.abs(delta) < 10) return;

    const newDirection = delta > 0 ? "down" : "up";
    setDirection(newDirection);
    setStickyTop(newDirection === "down" ? 0 : 75);

    lastScrollY.current = scrollY;
  }, [scrollY]);

  return { direction, stickyTop };
};
