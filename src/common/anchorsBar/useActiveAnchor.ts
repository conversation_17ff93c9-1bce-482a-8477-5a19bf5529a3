"use client";

import { useEffect, useState } from "react";
import { AnchorData } from "@/common/anchorsBar/anchorsBar";

export function useActiveAnchor(anchors: AnchorData[]) {
  const [activeId, setActiveId] = useState<string | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        for (const entry of entries) {
          if (entry.isIntersecting) {
            const id = entry.target.getAttribute("id");
            if (id) setActiveId(id);
            break;
          }
        }
      },
      {
        rootMargin: "-50% 0px -49% 0px",
        threshold: 0,
      },
    );

    anchors.forEach(({ id }) => {
      const el = document.getElementById(id);
      if (el) observer.observe(el);
    });

    return () => observer.disconnect();
  }, [anchors]);

  return activeId;
}
