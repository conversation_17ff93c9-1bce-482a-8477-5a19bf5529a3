import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";
import { fetchStoryblokStories } from "../storyblok";
import { StoryData } from "@/common/types";

export async function getArticles(
  currentStory: StoryData["story"],
  locale: string,
): Promise<IBlogStory[]> {
  const profileUuid = currentStory.uuid;
  const perPage = 50;
  const maxArticles = 9;
  const maxPage = 10;
  let page = 1;

  const matched: IBlogStory[] = [];

  while (matched.length < maxArticles && page <= maxPage) {
    const res = await fetchStoryblokStories({
      draftMode: false,
      // TODO（dan）:  need to change this to the correct path for blogs
      // starts_with: `${locale}/blog/`,
      starts_with: `${locale}/test-pages/`,
      filter_query: {
        component: { in: "blogPageV2" },
      },
      sort_by: "published_at",
      per_page: perPage,
      page,
    });

    const stories: IBlogStory[] =
      (res.data as { stories?: IBlogStory[] })?.stories ?? [];

    if (stories.length === 0) break;

    for (const story of stories) {
      if (story.uuid === profileUuid) continue;

      const content = story.content as {
        Authors?: { AuthorPage?: string }[];
        author?: string;
        [key: string]: any;
      };

      let authorUuids: string[] = [];

      if (Array.isArray(content.Authors) && content.Authors.length > 0) {
        authorUuids = content.Authors.map((a) => a?.AuthorPage).filter(
          (uuid): uuid is string => typeof uuid === "string",
        );
      }
      if (authorUuids.length === 0 && content.author) {
        authorUuids = [content.author];
      }

      if (authorUuids.includes(profileUuid)) {
        matched.push(story);
        if (matched.length >= maxArticles) break;
      }
    }

    page++;
  }

  return matched.slice(0, maxArticles);
}
