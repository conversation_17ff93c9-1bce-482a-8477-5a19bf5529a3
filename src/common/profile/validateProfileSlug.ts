import { StoryData } from "@/common/types";

export const TYPE_TO_PATH_PREFIX: Record<string, string> = {
  Consultant: "about-us/consultants/",
  FAO: "about-us/consultants/",
  Team: "about-us/team/",
  Editor: "editors/",
};

export const SUPPORTED_BASE_PATHS = ["about-us", "editors"];

export const PREFIX_TO_TYPES_MAP = Object.entries(TYPE_TO_PATH_PREFIX).reduce(
  (acc, [type, path]) => {
    acc[path] ??= [];
    acc[path].push(type);
    return acc;
  },
  {} as Record<string, string[]>,
);

export function normalizeType(type: string): string {
  return type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();
}

export function getEffectivePath(fullSlug: string): string {
  const segments = fullSlug.split("/");
  const matchIndex = segments.findIndex((s) =>
    SUPPORTED_BASE_PATHS.includes(s),
  );
  return matchIndex !== -1 ? segments.slice(matchIndex).join("/") : "";
}

export function getProfilePathValidationResult(story: StoryData["story"]):
  | {
      valid: true;
    }
  | {
      valid: false;
      reason: "not-in-supported-folder" | "type-folder-mismatch";
      typeList: string[];
      slug: string;
    } {
  const fullSlug = story.full_slug ?? "";
  const pathOnly = getEffectivePath(fullSlug);
  const rawTypes = story?.content?.Type ?? [];
  const typeList = Array.isArray(rawTypes) ? rawTypes : [];

  const normalizedTypes = typeList.map(normalizeType);

  const matchedPrefix = Object.entries(TYPE_TO_PATH_PREFIX).find(
    ([type, prefix]) =>
      normalizedTypes.includes(type) && pathOnly.startsWith(prefix),
  );

  const isInValidFolder = Object.values(TYPE_TO_PATH_PREFIX).some((prefix) =>
    pathOnly.startsWith(prefix),
  );

  if (!isInValidFolder) {
    return {
      valid: false,
      reason: "not-in-supported-folder",
      typeList: normalizedTypes,
      slug: fullSlug,
    };
  }

  if (!matchedPrefix) {
    return {
      valid: false,
      reason: "type-folder-mismatch",
      typeList: normalizedTypes,
      slug: fullSlug,
    };
  }

  return {
    valid: true,
  };
}
