export const allowedColours = ["red", "white", "maroon", "navy"] as const;
export type ButtonColour = (typeof allowedColours)[number];

export const allowedThemes = ["primary", "secondary"] as const;
export type ButtonTheme = (typeof allowedThemes)[number];

export function getSafeButtonColour(input: string): ButtonColour {
  return allowedColours.includes(input as ButtonColour)
    ? (input as ButtonColour)
    : "red";
}

export function getSafeButtonTheme(input: string): ButtonTheme {
  if (input === "solid") return "primary";
  return allowedThemes.includes(input as ButtonTheme)
    ? (input as ButtonTheme)
    : "primary";
}
