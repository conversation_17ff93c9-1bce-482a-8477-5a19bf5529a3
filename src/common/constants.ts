export const siteUrl =
  process.env.NEXT_PUBLIC_SITE_URL ?? "https://www.crimsoneducation.org";

export const fieldNames = {
  // Storyblok and Marketo field names
  ATTENDANCE_TYPE: "Attendance_Type__c",
  BIRTH_DATE: "birth_date__c",
  EMAIL: "email",
  FIRST_NAME: "firstName",
  LAST_NAME: "lastName",
  CITY: "city",
  STATE: "state",
  SCHOOL: "school__c",
  YEAR_OF_GRADUATION: "year_of_graduation__c",
  HIGHSCHOOL_YEAR_OF_GRADUATION: "High_School_Graduation_Year__c",
  ENQUIRY: "enquiry__c",
  PHONE: "phone",
  PERSON_TYPE: "person_type__c",
  PARTNERSHIP: "partnership__c",
  PARTNERSHIP_SCHOOL: "Partnership_School__c",
  FINANCIAL_AID: "do_you_need_financial_aid_to_study_abroa__c",
  HOW_DID_YOU_HEAR: "how_did_you_hear_about_crimson__c",
  CURRICULUM: "curriculum__c",
  BEST_TIME_TO_CALL: "best_time_to_call__c",
  PARTNERSHIP_NAME: "partnership_name__c",
  BUDGET: "budget__c",
  SCHOOL_YEAR_GRADE_LEVEL: "school_year_grade_level__c",
  PRIMARY_INTEREST: "primary_interest__c",
  COUNTRY: "country",
  PRIMARY_LANGUAGE: "primary_language__c",
  CITIZENSHIP: "citizenship__c",
  DUAL_CITIZENSHIP: "dual_citizenship__c",
  PRIVACY_POLICY: "privacyPolicy",
  STUDENT_AGE: "Age__c",
  CGA_SUBJECT_INTEREST: "CGA_Subjects_Interest__c",
  SCHOOL_PRIORITISATION: "schoolPrioritisation",
  COMPANY: "company",
  WECHAT_ID: "weChatId",

  // secondary contact fields
  SECONDARY_LEAD_FIRST_NAME: "secondary_lead_first_name__c",
  SECONDARY_LEAD_LAST_NAME: "secondary_lead_last_name__c",
  SECONDARY_LEAD_EMAIL: "secondary_lead_email__c",
  SECONDARY_LEAD_PHONE: "secondary_lead_phone__c",

  // Storyblok only field names
  COMMS_OPT_IN: "commsOptIn",
  CONDITIONAL_FIRST_NAME: "conditionalFirstName",
  CONDITIONAL_LAST_NAME: "conditionalLastName",
  CONDITIONAL_EMAIL: "conditionalEmail",
  CONDITIONAL_PHONE_NUMBER: "conditionalPhoneNumber",
  REMEMBER_ME_OPT_IN: "rememberMeOptIn",
  LOCATION: "location",
  DIVISION: "division",

  // Marketo only field names
  STUDENT_FIRST_NAME: "student_first_name__c",
  PARENT_FIRST_NAME: "parent_first_name__c",
  STUDENT_PHONE: "student_phone__c",
  PARENT_PHONE: "parent_phone__c",
  UNSUBSCRIBED: "unsubscribed",
  MKT_UTM_CAMPAIGN: "mkt_utm_campaign__c",
  MKT_UTM_CONTENT: "mkt_utm_content__c",
  MKT_UTM_MEDIUM: "mkt_utm_medium__c",
  MKT_UTM_SOURCE: "mkt_utm_source__c",
  MKT_UTM_TERM: "mkt_utm_term__c",
  FBCLID: "fbclid__c",
  GCLID: "gclid__c",
  REFERRAL_CODE: "referralCode",
};

export const fieldTypes = {
  CONDITIONAL_EMAIL: "conditionalEmail",
  CONDITIONAL_TEXT_FIELD: "ConditionalTextField",
  CONDITIONAL_PHONE_FIELD: "ConditionalPhoneField",
  DATE_OF_BIRTH_FIELD: "DateOfBirthField",
  TEXT_FIELD: "TextField",
  TEXT_AREA_FIELD: "TextAreaField",
  PHONE_FIELD: "PhoneField",
  DROPDOWN_FIELD: "DropdownField",
  TOGGLE_FIELD: "ToggleField",
  PARTNERSHIP_SCHOOL_FIELD: "Partnership_School_Field",
  COUNTRY_FIELD: "CountryField",
  LANGUAGE_FIELD: "LanguageField",
  CITIZENSHIP_FIELD: "CitizenshipField",
  CHECKBOX_FIELD: "CheckboxField",
  COMMS_OPT_IN_FIELD: "CommsOptInField",
  AGE_FIELD: "AgeField",
  CGA_SUBJECT_INTEREST_FIELD: "CGASubjectInterestField",
  SCHOOL_PRIORITISATION: "SchoolPrioritisationField",
  GOOGLE_LOCATION_FIELD: "GoogleMapsLocationField",
  DIVISION_FIELD: "DivisionField", // Maps to 'primary_interest__c' in Marketo
  PRIMARY_INTEREST_FIELD: "PrimaryInterestField",
  WECHAT_ID: "WechatIdField",
  REFERRAL_CODE: "ReferralCodeField",
};

export const showCommsOptInLocales: string[] = [
  "de",
  "uk",
  "ie",
  "ca",
  "ca-zh",
  "us",
  "us-zh",
  "sg",
  "ae-en",
  "ae",
  "hk-en",
  "hk-zh",
  "za",
  "ru",
  "ru-en",
];

export const showCommsOptInCountries: string[] = [
  "Austria",
  "Belgium",
  "Bulgaria",
  "Canada",
  "Croatia",
  "Cyprus",
  "Czech Republic",
  "Denmark",
  "Estonia",
  "Finland",
  "France",
  "Germany",
  "Greece",
  "Hungary",
  "Hong Kong - SAR",
  "Ireland",
  "Italy",
  "Latvia",
  "Lithuania",
  "Luxembourg",
  "Malta",
  "Netherlands",
  "Poland",
  "Portugal",
  "Romania",
  "Singapore",
  "Slovakia",
  "Slovenia",
  "South Africa",
  "Spain",
  "Sweden",
  "United Arab Emirates",
  "United Kingdom",
  "United States of America",
  "England",
  "Scotland",
  "Wales",
  "Northern Ireland",
  "Anguilla",
  "Bermuda",
  "Virgin Islands",
  "Cayman Islands",
  "Gibraltar",
  "Montserrat",
  "Russia",
];

export const MarketoFeatureTypes = {
  webinarPreview: "webinarPreview",
  blogSidePanelCalc: "blogSidePanelCalc",
  blogSidePanelWebinar: "blogSidePanelWebinar",
  blogSidePanelCta: "blogSidePanelCta",
  blogDynamicBanner: "blogDynamicBanner",
};
