import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import advancedFormat from "dayjs/plugin/advancedFormat";
import localizedFormat from "dayjs/plugin/localizedFormat";
import abbrTimezone from "dayjs-abbr-timezone";
import type { PluginFunc } from "dayjs";

// Add type declaration for the plugin
declare module "dayjs" {
  interface Dayjs {
    format(template?: string): string;
  }
}

const initDayjs = () => {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  dayjs.extend(advancedFormat);
  dayjs.extend(localizedFormat);
  dayjs.extend(abbrTimezone as PluginFunc);
};

let initialised = false;

// These don't exist in the dayjs plugin so I need to account for them manually
const EASTERN_EUROPEAN_CITIES = [
  "Sofia", // Bulgaria
  "Athens", // Greece
  "Bucharest", // Romania
  "Helsinki", // Finland
  "Tallinn", // Estonia
  "Riga", // Latvia
  "Vilnius", // Lithuania
  "Nicosia", // Cyprus
  "Chisinau", // Moldova
  "Kiev", // Ukraine
];

function getDateLocale(locale: string) {
  return (
    {
      tw: "zh-tw",
      us: "en",
      nz: "en-nz",
      au: "en-au",
      br: "pt-br",
      la: "es-mx",
      "kz-ru": "ru",
      hk: "zh-hk",
      mx: "es-mx",
      kr: "ko",
      cn: "zh-cn",
      "sg-zh": "zh",
    }[locale] ?? locale
  );
}

async function loadLocale(locale: string, fallback = "") {
  try {
    await import(`dayjs/locale/${locale?.split("-")?.[0]}.js`);
    return locale;
  } catch {
    if (fallback) await loadLocale(fallback, "");
    // load via user language settings
    console.error(`Date format error loading locale ${locale}`);
  }
}

/**
 * When using this function to format date time, exclude the timezone `z` value
 * in the `timeFormat` parameter, because we do some other timezone manipulation
 * that the dayjs library doesn't do.
 */

type IFormatParams = {
  dateTime: string;
  dateFormat?: string;
  timeFormat?: string;
  locale?: string;
  getDateFormat?: (locale: string) => string;
};

export const formatDateTime = async ({
  dateTime,
  dateFormat = "dddd, MMMM D, YYYY",
  timeFormat = "h:mm A",
  locale = "en",
  getDateFormat = (locale: string) => {
    const isSpaceless = /^(ja|zh).*/.test(locale ?? "");
    return isSpaceless ? "MMMDo" : "dddd, MMMM D";
  },
}: IFormatParams) => {
  const localeToLoad = getDateLocale(window?.navigator?.language);
  const fallbackLocale = getDateLocale(locale);
  const selectedLocale = await loadLocale(localeToLoad, fallbackLocale);

  if (!initialised) {
    initDayjs();
    initialised = true;
  }

  // Use UTC as the base timezone and then convert to local time
  const date = dayjs
    .utc(dateTime)
    .tz(dayjs.tz.guess(), true)
    .locale(selectedLocale ?? fallbackLocale);
  const targetDateFormat = getDateFormat
    ? getDateFormat(selectedLocale ?? locale)
    : dateFormat;
  const dateFormatted = date.format(targetDateFormat);
  const timeFormatted = date.format(timeFormat);
  const timezoneAbbreviation = getTimezoneAbbreviation(date);
  const timeFormattedWithTimezone = `${timeFormatted} ${timezoneAbbreviation}`;

  return [dateFormatted, timeFormattedWithTimezone];
};

function getTimezoneAbbreviation(date: dayjs.Dayjs): string {
  const timezoneName = dayjs.tz.guess();
  const offset = date.format("Z");
  const pluginAbbr = date.format("t");

  // We want to map CEST and CEDT to CET
  if (!pluginAbbr.startsWith("GMT") && pluginAbbr === "CEDT") {
    return "CET";
  }

  if (timezoneName.startsWith("Europe/")) {
    switch (offset) {
      case "+01:00":
      case "+02:00":
        if (
          EASTERN_EUROPEAN_CITIES.some((city) => timezoneName.includes(city))
        ) {
          return "EET";
        }

        // Accounting for daylight saving time in Europe (not done by dayjs plugin)
        return "CET";
      case "+03:00":
        return "EET";
      default:
        return pluginAbbr;
    }
  }

  return pluginAbbr;
}

export const getDayjsDate = (dateTime: string) => {
  if (!initialised) {
    initDayjs();
    initialised = true;
  }

  return dayjs.utc(dateTime).tz(dayjs.tz.guess());
};

/**
 * Checks if a date is expired based on the current time in
 * the user's timezone
 */
export const isDateExpired = (dateTime: string): boolean => {
  if (!initialised) {
    initDayjs();
    initialised = true;
  }

  return dayjs.utc(dateTime).tz(dayjs.tz.guess()).isBefore(dayjs());
};
