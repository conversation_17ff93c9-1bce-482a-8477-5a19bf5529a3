// Local replacement for @crimson-education/algorithms-school-selection-common
// This module provides the same interface as the private package

export const IBSubjects: string[] = [
  "Biology",
  "Chemistry", 
  "Physics",
  "Mathematics",
  "Further Mathematics",
  "Computer Science",
  "Design Technology",
  "Environmental Systems and Societies",
  "Sports, Exercise and Health Science",
  "English A: Language and Literature",
  "English A: Literature", 
  "English B",
  "Chinese A: Language and Literature",
  "Chinese A: Literature",
  "Chinese B",
  "French A: Language and Literature",
  "French A: Literature",
  "French B",
  "German A: Language and Literature", 
  "German A: Literature",
  "German B",
  "Spanish A: Language and Literature",
  "Spanish A: Literature", 
  "Spanish B",
  "Japanese A: Language and Literature",
  "Japanese A: Literature",
  "Japanese B",
  "Korean A: Language and Literature",
  "Korean A: Literature", 
  "Korean B",
  "History",
  "Geography",
  "Economics",
  "Business Management",
  "Psychology",
  "Philosophy",
  "Global Politics",
  "Information Technology in a Global Society",
  "World Religions",
  "Anthropology",
  "Visual Arts",
  "Music",
  "Theatre",
  "Film",
  "Dance",
  "Theory of Knowledge",
  "Extended Essay",
  "Creativity, Activity, Service"
];

export const ALevelsSubjects: string[] = [
  "Accounting",
  "Ancient History", 
  "Anthropology",
  "Arabic",
  "Archaeology",
  "Art and Design",
  "Bengali",
  "Biology",
  "Business Studies",
  "Chemistry",
  "Chinese",
  "Classical Civilisation",
  "Computer Science",
  "Dance",
  "Design and Technology",
  "Drama and Theatre Studies",
  "Economics",
  "Electronics",
  "English Language",
  "English Language and Literature",
  "English Literature",
  "Environmental Science",
  "Film Studies",
  "French",
  "Further Mathematics",
  "Geography",
  "Geology",
  "German",
  "Government and Politics",
  "Greek",
  "Gujarati",
  "Hindi",
  "History",
  "History of Art",
  "Information and Communication Technology",
  "Italian",
  "Japanese",
  "Latin",
  "Law",
  "Mathematics",
  "Media Studies",
  "Modern Hebrew",
  "Music",
  "Music Technology",
  "Panjabi",
  "Persian",
  "Philosophy",
  "Photography",
  "Physical Education",
  "Physics",
  "Polish",
  "Portuguese",
  "Psychology",
  "Religious Studies",
  "Russian",
  "Sociology",
  "Spanish",
  "Statistics",
  "Tamil",
  "Telugu",
  "Turkish",
  "Urdu"
];

// Default export matching the original package structure
const ssaCommon = {
  IBSubjects,
  ALevelsSubjects,
};

export default ssaCommon;
