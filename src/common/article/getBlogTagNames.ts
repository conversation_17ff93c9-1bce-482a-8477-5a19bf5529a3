import { blogTagsList } from "@/components/articlePage/types";
import { fetchBlogTags } from "@/common/storyblok";
import { blogTags } from "../types";

export async function getBlogTagNames(
  blogTags: blogTags,
): Promise<{ name: string; value: string }[]> {
  if (!blogTags.length) return [];
  const tagComponentToDatasourceMap: Record<string, string> = {
    blogGlobalTag: "blog-tags-global",
    blogLocalisedTag: "blog-tags-localised",
  };

  const uniqueDatasourceNames = Array.from(
    new Set(
      blogTags
        .map((tag) => tagComponentToDatasourceMap[tag.component ?? ""])
        .filter(Boolean),
    ),
  );

  const tagResponses = await Promise.all(
    uniqueDatasourceNames
      .filter((ds): ds is string => !!ds)
      .map((ds) => fetchBlogTags(ds)),
  );

  const allFetchedTags: blogTagsList = tagResponses.flatMap((res) => {
    const typedRes = res as {
      data: { datasource_entries: blogTagsList };
    };
    return typedRes.data.datasource_entries || [];
  });

  return blogTags.map((tag) => {
    const matched = allFetchedTags.find((entry) => entry.value === tag.tag);
    return {
      name: matched?.name ?? tag.tag,
      value: tag.tag,
    };
  });
}
