import {
  fetchStoryblokStories,
  fetchStoryWithRelations,
} from "@/common/storyblok";
import {
  ILearningCollectionStory,
  IPopulatedLearningCollectionStory,
} from "@/components/articlePage/types";
import { IPopulatedLearningCollectionContent, IStory } from "@/common/types";

type ResolvedLCResponse = {
  story: IStory<IPopulatedLearningCollectionContent>;
};

export async function getLearningCollectionForBlogPage(
  blogUuid: string,
  locale: string,
): Promise<IPopulatedLearningCollectionStory | null> {
  if (!blogUuid) return null;

  const lcRes = await fetchStoryblokStories<ILearningCollectionStory>({
    draftMode: false,
    starts_with: `${locale}/blog/learning-collections/`,
    per_page: 100,
    sort_by: "published_at",
  });

  const allLCs = lcRes.data?.stories ?? [];

  let latestMatchedLC: ILearningCollectionStory | null = null;

  for (const lc of allLCs) {
    const items = lc.content?.items;

    if (!Array.isArray(items)) continue;

    const matched = items.some((item) => {
      if (typeof item === "string") return item === blogUuid;
      if (typeof item === "object" && item !== null && "uuid" in item) {
        return (item as { uuid: string }).uuid === blogUuid;
      }
      return false;
    });

    if (matched) {
      latestMatchedLC = lc;
      break;
    }
  }

  if (!latestMatchedLC) return null;

  const res: { data?: ResolvedLCResponse } = await fetchStoryWithRelations(
    latestMatchedLC.full_slug,
    "learningCollection.items",
  );

  if (!res?.data?.story) return null;

  const resolved = res.data.story;
  const resolvedItems = resolved.content?.items ?? [];
  if (!Array.isArray(resolvedItems) || resolvedItems.length === 0) return null;

  return {
    ...resolved,
    content: {
      ...resolved.content,
      items: resolvedItems,
    },
  };
}
