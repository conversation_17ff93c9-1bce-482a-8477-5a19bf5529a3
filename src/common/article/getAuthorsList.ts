import { Authors, IStory, StoryData } from "@/common/types";
import { fetchStoryByUuid, fetchStoryWithRelations } from "@/common/storyblok";
import {
  IBlogStory,
  IBlogStoryContent,
} from "@/components/bloks/NewsAndArticles/utils";

export async function getAuthorsList(story: IBlogStory, slug: string) {
  const authorsRaw = story?.content?.Authors as Authors[];

  if (!authorsRaw?.length) {
    const { data: resolvedData } = await fetchStoryWithRelations(
      slug,
      "blogPageV2.author",
      true,
    );
    const resolvedStory = (resolvedData as StoryData<IBlogStoryContent>).story;
    const author = resolvedStory?.content?.author;
    const persona = resolvedStory?.content?.persona;

    if (author) {
      return [{ ...author, mappedPersona: persona }];
    }

    return [];
  }

  const orderedUuids: string[] = [];
  const personaMap = new Map<string, string>();

  for (const item of authorsRaw) {
    const uuid = item?.AuthorPage;
    if (uuid) {
      orderedUuids.push(uuid);
      personaMap.set(uuid, item.Persona);
    }
  }

  if (!orderedUuids.length) return [];

  const { data } = await fetchStoryByUuid(orderedUuids.join(","), true);
  const stories = data?.stories || [];

  const storyMap = new Map<string, IStory>();
  for (const story of stories) {
    storyMap.set(story.uuid, story);
  }

  return orderedUuids
    .map((uuid) => {
      const author = storyMap.get(uuid);
      if (!author) return null;
      return {
        ...author,
        mappedPersona: personaMap.get(uuid),
      };
    })
    .filter(Boolean);
}
