import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";
import { fetchStoryblokStories } from "../storyblok";
import { StoryData } from "@/common/types";

export async function getRelatedArticles(
  currentStory: StoryData["story"],
  locale: string,
): Promise<IBlogStory[]> {
  const currentTags = currentStory.content.blogTags?.map((t) => t.tag) ?? [];
  const currentUuid = currentStory.uuid;

  if (currentTags.length === 0) {
    const res = await fetchStoryblokStories({
      draftMode: false,
      starts_with: `${locale}/blog/`,
      filter_query: {
        component: { in: "blogPageV2" },
      },
      per_page: 4,
      sort_by: "published_at",
    });

    const stories: IBlogStory[] =
      (res.data as { stories?: IBlogStory[] })?.stories ?? [];
    return stories.filter((s) => s.uuid !== currentUuid).slice(0, 3);
  }

  const perPage = 30;
  let page = 1;
  const maxPage = 10;

  const related: (IBlogStory & { matchingCount: number })[] = [];
  const fallback: IBlogStory[] = [];

  while ((related.length < 3 || fallback.length < 3) && page <= maxPage) {
    const res = await fetchStoryblokStories({
      draftMode: false,
      starts_with: `${locale}/blog/`,
      filter_query: {
        component: { in: "blogPageV2" },
        ...(currentTags.length > 0
          ? { "blogTags.tag_in": currentTags.join(",") }
          : {}),
      },
      per_page: perPage,
      page,
      sort_by: "published_at",
    });

    const stories = (res.data as { stories?: IBlogStory[] })?.stories ?? [];

    if (stories.length === 0) break;

    for (const story of stories) {
      if (story.uuid === currentUuid) continue;
      if (
        related.some((s) => s.uuid === story.uuid) ||
        fallback.some((s) => s.uuid === story.uuid)
      ) {
        continue;
      }

      const storyTags = story.content.blogTags?.map((t) => t.tag) ?? [];
      const matchingCount = storyTags.filter((tag) =>
        currentTags.includes(tag),
      ).length;

      if (matchingCount > 0) {
        related.push({ ...story, matchingCount });
      } else {
        fallback.push(story);
      }

      if (related.length >= 3 && fallback.length >= 3) break;
    }

    page++;
  }

  const topRelated = related
    .sort((a, b) => {
      if (b.matchingCount !== a.matchingCount)
        return b.matchingCount - a.matchingCount;
      const dateDiff =
        new Date(b.published_at).getTime() - new Date(a.published_at).getTime();
      if (dateDiff !== 0) return dateDiff;
      return a.name.localeCompare(b.name);
    })
    .slice(0, 3);

  if (topRelated.length < 3) {
    const filler = fallback
      .filter((s) => !topRelated.some((r) => r.uuid === s.uuid))
      .slice(0, 3 - topRelated.length);
    return [...topRelated, ...filler];
  }

  return topRelated;
}
