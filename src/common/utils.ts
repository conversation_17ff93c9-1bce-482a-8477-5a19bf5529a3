import { clsx, type ClassValue } from "clsx";
import toast from "react-hot-toast";
import { twMerge } from "tailwind-merge";
import {
  showCommsOptInCountries,
  showCommsOptInLocales,
  siteUrl,
} from "./constants";
import { createHmac } from "crypto";
import { IStoryblokAssetProps } from "./types";

export function cn(...inputs: ClassValue[]) {
  const classes = clsx(inputs);
  return twMerge(classes);
}

export const showCommsOptIn = (
  currentLocale: string,
  country: string,
): boolean => {
  return (
    showCommsOptInCountries.includes(country) ||
    showCommsOptInLocales.includes(currentLocale)
  );
};

export const getSlugWithoutLocale = (fullSlug: string) => {
  if (!fullSlug) return null;
  return fullSlug.replace(
    /^(\/?[a-z]{2}|\/?[a-z]{2}-[a-z]{2}|global-content)(\/.*)$/i,
    (match, matchLocale, matchRemainingSlug) => `${matchRemainingSlug}`,
  );
};

export const httpsPath = (path: string | undefined) =>
  path && path.length > 0
    ? path.includes("https://")
      ? path
      : `https:${path}`
    : "";

export const formatLocalTime = (
  updated_at: string | null,
  fullSlug: string,
): string => {
  if (!updated_at) return "unknown time";
  const date = new Date(updated_at);
  const localePrefix = fullSlug.replace(/^\//, "").split("/")[0];
  const isEnglishLocale =
    (localePrefix ?? "") === "en" || (localePrefix ?? "").includes("-en");
  if (isEnglishLocale) {
    return date.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
    });
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}/${month}/${day}`;
};

export function replaceEnWithUs(path: string | undefined) {
  if (!path) return "";
  const updatedPath = path.replace(/^en(?=\/)/, "us");
  return updatedPath;
}

export const copyToClipboard = async (targetString: string) => {
  if (typeof window === "undefined") return;
  try {
    await navigator.clipboard.writeText(targetString);
    toast.success("Copied to Clipboard", {
      duration: 1000,
    });
  } catch (e) {
    console.error(e);
  }
};

export const getShareLink = (fullSlug: string) => {
  return `${siteUrl}/${fullSlug}`;
};

export const flattenFilterQuery = (
  filterQuery: Record<string, Record<string, string>>,
) => {
  const result: Record<string, string> = {};
  for (const key in filterQuery) {
    const sub = filterQuery[key];
    for (const operation in sub) {
      result[`filter_query[${key}][${operation}]`] = sub[operation] ?? "";
    }
  }
  return result;
};
export const generateImageAltFromFilename = (filename?: string) => {
  if (typeof filename !== "string" || !filename) return "";
  const parts = filename?.split("/");
  const lastPart = parts[parts.length - 1] ?? "";
  const alt = lastPart.replace(/\.[^/.]+$/, "");
  return alt;
};

/**
 * This is for the specific use case of images that are selected via a data
 * source from storyblok. Currently used by the InfoPage and WebinarEventPageV2,
 * where its passed in as a comma separated string from storyblok that we need
 * to parse
 */
export const getDataSourceBackgroundImages = (images: string) => {
  if (!images) return { desktopImage: "", mobileImage: "" };

  const backgroundImages = images?.split(",") ?? [];
  const desktopImage = backgroundImages[0] ?? "";
  const mobileImage = backgroundImages[1] ?? desktopImage;

  return {
    desktopImage,
    mobileImage,
  };
};

export const validateSignature = (
  secret: string,
  body: string,
  signature: string,
) => createHmac("sha1", secret).update(body).digest("hex") === signature;

export const tryAsyncCalls = async <T>(
  ...callers: (() => Promise<T>)[]
): Promise<T | null> => {
  for (const caller of callers) {
    try {
      return await caller();
    } catch (e) {
      console.error(e);
    }
  }
  return null;
};
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay = 300,
): (...args: Parameters<T>) => any {
  let timeoutId: ReturnType<typeof setTimeout>;

  return function (...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
}

/**
 * This is for the specific use of shareImage when sometimes it may be the legacy Image(old)
 * type in storyblok, or the new Image Asset type
 * @param shareImage
 * @returns
 */
export const getShareImage = (
  shareImage: string | IStoryblokAssetProps | null,
): string => {
  if (!shareImage) return "";

  if (typeof shareImage === "string") {
    return httpsPath(shareImage) ?? "";
  }

  return shareImage.filename;
};

export const hexToRgba: (hex: string, alpha?: number) => string = (
  hex,
  alpha = 1,
) => {
  hex = hex.replace("#", "");
  if (!/^([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})$/.test(hex)) {
    throw new Error("Invalid hex color format");
  }
  if (alpha < 0 || alpha > 1) {
    throw new Error("Alpha value must be between 0 and 1");
  }

  const fullHex =
    hex.length === 3
      ? hex
          .split("")
          .map((char) => char + char)
          .join("")
      : hex;

  const r = parseInt(fullHex.slice(0, 2), 16);
  const g = parseInt(fullHex.slice(2, 4), 16);
  const b = parseInt(fullHex.slice(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};
