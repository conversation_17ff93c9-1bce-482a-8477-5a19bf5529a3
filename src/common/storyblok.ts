import {
  getStoryblokApi,
  ISbStories,
  type ISbStoriesParams,
  type StoryblokClient,
} from "@storyblok/react/rsc";
import { flattenFilterQuery } from "./utils";
import {
  IBlogHomePageStory,
  IRelatedTagGroup,
  IQueryOptions,
  IPsudoTagPage,
  IStory,
  ITagHomePageResponse,
  IStoryVersion,
} from "./types";
import {
  ILearningCollectionStory,
  IPopulatedLearningCollectionStory,
} from "@/components/articlePage/types";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";
import { tryAsyncCalls } from "./utils";
import { ISbSiteShellStoryContent } from "@/components/ui/SiteShell/types";

export interface ITagRelationshipResult {
  coExistCounts: Record<string, number>;
  existCounts: Record<string, number>;
}

const getApiUrl = (version = "2") => {
  const versionCode = `v${version}`;
  const region = process.env.REGION ?? process.env.NEXT_PUBLIC_REGION ?? "eu";
  return region === "ap"
    ? `https://api-ap.storyblok.com/${versionCode}`
    : `https://api.storyblok.com/${versionCode}`;
};

const getToken = () => {
  const token = process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN ?? "";
  if (!token) {
    throw new Error("invalid preview token");
  }
  return token;
};

const getManagementToken = () => {
  const token = process.env.STORYBLOK_MANAGEMENT_TOKEN;
  if (!token) {
    throw new Error("invalid management token");
  }
  return token;
};

export const isTagPagePath = (path: string) => {
  const segs = path.split("/");
  if (segs.length === 3 && segs[1] === "category") {
    return {
      locale: String(segs[0]),
      tag: String(segs[2]),
    };
  }
  return null;
};

export const fetchTagInfoFromBlogHomePage = async (locale: string) => {
  const token = getToken();
  const searchParams = new URLSearchParams({
    token,
  });
  const headers = {
    Accept: "application/json",
    "Content-Type": "application/json",
  };
  const theSlug = `${locale}/blog-home-page`;
  const blogHomePage = await fetch(
    `${getApiUrl()}/cdn/stories/${theSlug}?${searchParams.toString()}`,
    {
      headers,
      method: "GET",
      cache: "no-cache",
      next: {
        tags: [cacheTagBuilder("blog-homepage", { locale })],
      },
    },
  );
  const data = await blogHomePage.json();
  return data as {
    story: IBlogHomePageStory;
  };
};
export const fetchTagInfoFromBlogHomePageByTag = async (
  locale: string,
  tag: string,
): Promise<ITagHomePageResponse> => {
  const { story: blogHomePageStory } =
    await fetchTagInfoFromBlogHomePage(locale);
  const {
    includingTags = [],
    relatedTagGroups = [],
    trending: trendings = [],
  } = blogHomePageStory.content;
  const foundTrending = trendings.find(
    (t) => t.Tag.some((t1) => t1.tag === tag) && t.blogs.length > 0,
  );
  if (!foundTrending) {
    return {
      includingTags,
      relatedTagGroups,
      relatedBlogs: [],
    };
  }
  const uuids = [...foundTrending.blogs];
  const { data: relatedBlogs } = await fetchStoriesByUuids<IBlogStory>(
    uuids,
    false,
    {
      tags: [cacheTagBuilder("blog-homepage", { locale })],
    },
  );
  return {
    includingTags,
    relatedTagGroups,
    foundTrending,
    relatedBlogs: relatedBlogs.stories,
  };
};

export const fetchTagRelationShips = async (
  locale: string,
): Promise<ITagRelationshipResult> => {
  let pageNo = 1;
  const pageSize = 100;
  const coExistCounts: Record<string, number> = {};
  const existCounts: Record<string, number> = {};
  const processStory = (s: IBlogStory[]) => {
    s.forEach((story) => {
      const { blogTags } = story.content;
      if (!Array.isArray(blogTags)) {
        return;
      }
      const rawTags = Array.from(new Set(blogTags.map(({ tag }) => tag)));
      rawTags.forEach((tag1, index1) => {
        existCounts[tag1] = (existCounts[tag1] ?? 0) + 1;
        rawTags.slice(index1 + 1).forEach((tag2, index2) => {
          if (index1 === index2) {
            return;
          }
          const orderedName = [tag1, tag2].sort().join("__AND__");
          if (coExistCounts[orderedName]) {
            coExistCounts[orderedName]++;
          } else {
            coExistCounts[orderedName] = 1;
          }
        });
      });
    });
  };
  const { data: firstPage, total } = await fetchStoryblokStories<IBlogStory>({
    starts_with: `${locale}/blog`,
    page: pageNo,
    per_page: pageSize,
  });
  processStory(firstPage.stories);
  let currentTotal = firstPage.stories.length;
  while (currentTotal < total) {
    pageNo++;
    const { data: nextPage } = await fetchStoryblokStories<IBlogStory>({
      starts_with: `${locale}/blog`,
      page: pageNo,
      per_page: pageSize,
    });
    processStory(nextPage.stories);
    currentTotal += nextPage.stories.length;
  }
  return {
    coExistCounts,
    existCounts,
  };
};

export const fetchLearningCollectionByTag = async (
  locale: string,
  tag: string,
  count = 3,
): Promise<IPopulatedLearningCollectionStory[]> => {
  const { data: collections } =
    await fetchStoryblokStories<ILearningCollectionStory>(
      {
        starts_with: `${locale}/blog/learning-collections`,
        filter_query: {
          "tags.tag": {
            in: tag,
          },
        },
        per_page: count,
        sort_by: "recent",
      },
      [cacheTagBuilder("learning-collection", { locale, tag })],
    );
  const { stories = [] } = collections;
  const uuids = stories.reduce((acc, story) => {
    acc.push(...(story?.content?.items ?? []));
    return acc;
  }, [] as string[]);
  const { data: blogs } = await fetchStoriesByUuids<IBlogStory>(uuids);
  const blogMap = new Map(blogs.stories.map((b) => [b.uuid, b]));
  const populated = collections.stories
    .map((collection) => {
      return {
        ...collection,
        content: {
          ...collection.content,
          items: (collection.content?.items ?? []).reduce((acc, item) => {
            const blog = blogMap.get(item);
            if (blog) {
              acc.push(blog);
            }
            return acc;
          }, [] as IBlogStory[]),
        },
      };
    })
    .filter((c) => c.content.items.length > 0);
  return populated;
};

export const fetchTagPageData = async (
  locale: string,
  tag: string,
  pageNo: number,
  pageSize: number,
): Promise<IPsudoTagPage> => {
  const { stories, total } = await getStoryListByTag(
    locale,
    tag,
    pageNo,
    pageSize,
  );
  const homepageInfo = await fetchTagInfoFromBlogHomePageByTag(locale, tag);
  const learningCollections = await fetchLearningCollectionByTag(locale, tag);
  const tagRelationshipInfo = await fetchTagRelationShips(locale);
  const { relatedTagGroups, relatedBlogs } = homepageInfo;
  const relatedTags = findRelatedTags(
    tag,
    relatedTagGroups,
    tagRelationshipInfo,
  );
  const tgtTagIncluded = (homepageInfo.includingTags ?? [])
    .map((t) => t.tag)
    .includes(tag);
  if (!tgtTagIncluded) {
    //TODO: UI for no related tags?
    const errMsg = `Tag ${tag} is not included in the homepage info`;
    console.error(errMsg);
    throw new Error(errMsg);
  }
  const { data: fullDataSourceEntries } = await getAllDataSourceEntries(
    "blog-tags-v2",
    "description",
  );
  const tgtTagInfo = fullDataSourceEntries.find((entry) => entry.value === tag);
  if (!tgtTagInfo) {
    const errMsg = `Tag ${tag} is not found in the datasource entries`;
    console.error(errMsg);
    throw new Error(errMsg);
  }
  if (!tgtTagInfo.dimension_value) {
    const errMsg = `Tag ${tag} has no description`;
    console.error(errMsg);
    throw new Error(errMsg);
  }
  return {
    content: {
      component: "TagPageV2",
      primaryTag: {
        tag: tgtTagInfo.value,
        description: tgtTagInfo.dimension_value,
        name: tgtTagInfo.name,
      },
      relatedTags,
      articles: {
        stories,
        total,
        pageSize,
      },
      trendingArticles: relatedBlogs,
      learningCollections: learningCollections,
    },
  };
};

// TODO - add dynamic regin for api

export async function fetchStoryblokStories<T = IStory>(
  options: IQueryOptions,
  tags: string[] = [],
): Promise<{ data: { stories: T[] }; total: number }> {
  const version = options.draftMode ? "draft" : "published";
  const cv = Math.floor(Date.now() / 1000);
  const token = process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN!;

  const searchParams: Record<string, string> = {
    version,
    token,
    cv: String(cv),
    resolve_links: "url",
  };

  if (options?.uuids) {
    searchParams.by_uuids = options.uuids.join(",");
  }

  if (options?.slug) {
    searchParams.by_slugs = options.slug;
  }

  if (options?.starts_with) {
    searchParams.starts_with = options.starts_with;
  }

  if (options?.filter_query) {
    Object.assign(searchParams, flattenFilterQuery(options.filter_query));
  }

  if (options?.sort_by === "recent") {
    searchParams.sort_by = "created_at:desc";
  } else if (options?.sort_by === "published_at") {
    searchParams.sort_by = "published_at:desc";
  }

  if (options?.per_page) {
    searchParams.per_page = String(options.per_page);
  }

  if (options?.page) {
    searchParams.page = String(options.page);
  }

  const query = new URLSearchParams(searchParams).toString();

  const finalUrl = `${getApiUrl()}/cdn/stories?${query}`;
  const res = await fetch(finalUrl, {
    next: {
      revalidate: 3600,
      tags: [...tags],
    },
  });
  if (!res.ok) {
    throw new Error(`HTTP error! status: ${res.status}`);
  }

  const data = await res.json();
  return { data, total: parseInt(res.headers.get("total") ?? "0") };
}

export const betterFetchStoryBySlug = async <T = Record<string, unknown>>(
  slug: string,
  draftMode = false,
) => {
  const version = draftMode ? "draft" : "published";

  // https://www.storyblok.com/docs/api/content-delivery/v2/getting-started/cache-invalidation
  const cv = new Date().getTime() / 1000;
  const params = new URLSearchParams({
    token: getToken(),
    cv: String(cv),
    version,
  });
  const url = `${getApiUrl()}/cdn/stories/${slug}?${params.toString()}`;
  const res = await fetch(url, {
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    next: {
      tags: ["page"],
    },
  });
  if (!res.ok) {
    throw new Error(
      `fetchStoryBySlug error! status: ${res.status} ${res.statusText} ${slug}`,
    );
  }
  const data = (await res.json()) as {
    story: IStory<T>;
  };
  return data;
};

export async function fetchStoryBySlug(
  slug: string,
  draftMode = false,
  isUUID = false,
) {
  const version = draftMode ? "draft" : "published";

  // https://www.storyblok.com/docs/api/content-delivery/v2/getting-started/cache-invalidation
  const cv = new Date().getTime() / 1000;
  const searchParamsData: ISbStoriesParams = {
    token: getToken(),
    cv,
    version,
    resolve_links: "url",
  };

  const searchParams = new URLSearchParams(
    searchParamsData as Record<string, string>,
  );

  if (isUUID) {
    searchParams.set("find_by", "uuid");
  }

  const data = await fetch(
    `${getApiUrl()}/cdn/stories/${slug}?${searchParams.toString()}`,
    {
      next: {
        tags: ["page"],
      },
    },
  ).then((res) => res.json());

  return { data };
}

export async function fetchData(slug: string, draftMode = false) {
  const version = draftMode ? "draft" : "published";
  const sbParams: ISbStoriesParams = { version, resolve_links: "url" };
  const storyblokApi: StoryblokClient = getStoryblokApi();

  return storyblokApi.get(`cdn/stories/${slug}`, sbParams);
}

export async function fetchDataSource(
  datasourceName: string,
  draftMode = false,
  dimension?: string,
  per_page?: number,
) {
  const version = draftMode ? "draft" : "published";
  const cv = new Date().getTime() / 1000;

  const sbParams: ISbStoriesParams = {
    token: process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN,
    version,
    datasource: datasourceName,
    dimension: dimension ?? "",
    cv,
    per_page,
  };

  const region = process.env.REGION;
  const baseUrl =
    region === "ap"
      ? "https://api-ap.storyblok.com/v2/"
      : "https://api.storyblok.com/v2/";

  const searchParams = new URLSearchParams(sbParams as Record<string, string>);
  const data = await fetch(
    `${baseUrl}cdn/datasource_entries?${searchParams.toString()}`,
    {
      next: {
        tags: ["page"],
      },
    },
  ).then((res) => res.json());

  return { data } as any;
}

export async function fetchStoryWithRelations(
  slug: string,
  resolveRelations: string,
  draftMode = false,
) {
  const version = draftMode ? "draft" : "published";
  const cv = new Date().getTime() / 1000;
  const token = getToken();

  const searchParamsData: ISbStoriesParams = {
    slug,
    version,
    resolve_relations: resolveRelations, // separate multiple items with commas, e.g. "blogPageV2.author,blogHomePage.featuredAuthors"
    cv,
    token,
    from_release: "0",
  };

  const searchParams = new URLSearchParams(
    searchParamsData as Record<string, string>,
  );

  const apiUrl = getApiUrl("1");
  const data = await fetch(
    `${apiUrl}/cdn/stories/${slug}?${searchParams.toString()}`,
    {
      next: {
        tags: ["resolve-story"],
      },
    },
  ).then((res) => res.json());

  return { data };
}

export async function fetchStoryByUuid(uuid: string, draftMode = false) {
  const version = draftMode ? "draft" : "published";
  const cv = new Date().getTime() / 1000;

  const token = getToken();
  const res = await fetch(
    `${getApiUrl()}/cdn/stories?by_uuids=${uuid}&version=${version}&token=${token}&cv=${cv}`,
    {
      next: {
        tags: ["uuid-story"],
      },
    },
  );
  const data = (await res.json()) as { stories: IStory[] };

  return { data };
}

export async function fetchStoriesByUuids<T = IStory>(
  uuids: string[],
  draftMode = false,
  cacheConfig: NextFetchRequestConfig = {},
): Promise<{ data: { stories: T[] } }> {
  if (uuids.length === 0) {
    return { data: { stories: [] } };
  }
  const version = draftMode ? "draft" : "published";
  const cv = new Date().getTime() / 1000;
  const token = getToken();

  const searchParamsData: ISbStoriesParams = {
    version,
    by_uuids: uuids.join(","),
    token,
    cv,
  };

  const searchParams = new URLSearchParams(
    searchParamsData as Record<string, string>,
  );

  const data = (await fetch(
    `${getApiUrl()}/cdn/stories?${searchParams.toString()}`,
    {
      next: {
        ...cacheConfig,
      },
    },
  ).then((res) => res.json())) as { stories: T[] };

  return { data };
}

export async function fetchBlogTags(BlogTagsType: string, per_page = 1000) {
  const token = getToken();

  const url = `${getApiUrl()}/cdn/datasource_entries?datasource=${BlogTagsType}&token=${token}&per_page=${per_page}`;

  const data = await fetch(url, {
    next: {
      tags: ["blog-tags"],
    },
  }).then((res) => res.json());

  return { data };
}

export async function fetchMultipleStories(
  // @dir
  slug: string,
  draftMode = false,
  component = "",
) {
  const version = draftMode ? "draft" : "published";

  // Maybe we don't need the cache value for this to be so short. Revisit
  const cv = new Date().getTime() / 1000;
  const storyblokApi: StoryblokClient = getStoryblokApi();

  const sbParams: ISbStoriesParams = {
    version,
    resolve_links: "url",
    cv,
    starts_with: `${slug}`, // This allows you to fetch all stories in a specific folder
    ...(component && {
      filter_query: {
        component: {
          in: component,
        },
      },
    }),
  };

  return storyblokApi.get(`cdn/stories`, sbParams);
}
export const getStoryListByTag = async (
  locale: string,
  tag: string,
  page: number,
  per_page: number,
) => {
  const { data, total } = await fetchStoryblokStories<IBlogStory>(
    {
      starts_with: `${locale}/blog`,
      page,
      per_page,
      filter_query: {
        "blogTags.tag": {
          in: tag,
        },
      },
    },
    [cacheTagBuilder("tag-story", { tag, locale })],
  );
  return {
    stories: data.stories,
    total,
  };
};

export const findRelatedTags = (
  targetTag: string,
  relatedTagGroups: IRelatedTagGroup[],
  tagRelationshipResult: ITagRelationshipResult,
) => {
  const { coExistCounts, existCounts } = tagRelationshipResult;
  const foundRelatedTagGroups = relatedTagGroups.find(
    ({ primary_tag, related_tags }) => {
      return (
        primary_tag.some((t) => t.tag === targetTag) ||
        related_tags.some((t) => t.tag === targetTag)
      );
    },
  );
  if (foundRelatedTagGroups) {
    const rawTagsInGroup = Array.from(
      new Set([
        ...foundRelatedTagGroups.primary_tag.map((t) => t.tag),
        ...foundRelatedTagGroups.related_tags.map((t) => t.tag),
      ]),
    ).filter((tag) => tag !== targetTag);
    const sortedTags = rawTagsInGroup
      .sort((a, b) => {
        const aCount = existCounts[a] ?? 0;
        const bCount = existCounts[b] ?? 0;
        return bCount - aCount;
      })
      .slice(0, 10);
    return sortedTags;
  }
  const relatedTagsByCoExist = Object.entries(coExistCounts)
    .filter(([tagPair]) => {
      return tagPair.includes(targetTag);
    })
    .map(([tagPair, count]) => {
      const [tag1, tag2] = tagPair.split("__AND__");
      return {
        tag: tag1 === targetTag ? tag2 : tag1,
        count,
      };
    })
    .sort((a, b) => b.count - a.count)
    .filter((t) => !!t.tag)
    .map((t) => t.tag)
    .slice(0, 10);
  return relatedTagsByCoExist as string[];
};

export const getDataSourceEntries = async (
  datasource: string,
  dimension: string,
  per_page: number,
  page = 1,
) => {
  const token = getToken();
  const query = new URLSearchParams({
    token,
    page: String(page),
    datasource,
    dimension,
    per_page: String(per_page),
  });
  const url = `${getApiUrl()}/cdn/datasource_entries/?${query.toString()}`;
  const res = await fetch(url, {
    next: {
      revalidate: 3600,
      tags: [`datasource-entries-${datasource}`],
    },
  });
  const data = (await res.json()) as {
    datasource_entries: {
      id: number;
      name: string;
      value: string;
      dimension_value?: string;
    }[];
  };
  const total = parseInt(res.headers.get("total") ?? "0");
  return {
    data: data.datasource_entries,
    total,
  };
};

export const getAllDataSourceEntries = async (
  datasource: string,
  dimension: string,
) => {
  const per_page = 100;
  let page = 1;
  const { data, total } = await getDataSourceEntries(
    datasource,
    dimension,
    per_page,
    page,
  );
  const allData = [...data];
  while (allData.length < total) {
    page++;
    const { data: nextData } = await getDataSourceEntries(
      datasource,
      dimension,
      per_page,
      page,
    );
    allData.push(...nextData);
  }
  return {
    data: allData,
    total,
  };
};

export const cacheTagBuilder = (
  entityName: "blog-homepage" | "learning-collection" | "tag-story",
  attributes: {
    tag?: string;
    locale?: string;
  },
) => {
  let theTag = `tag-${entityName}`;
  if (attributes.tag) {
    theTag += `-${attributes.tag}`;
  }
  if (attributes.locale) {
    theTag += `-${attributes.locale}`;
  }
  return theTag;
};

export const getStoryVersions = async <T>(
  spaceId: number,
  storyId: number,
  pageNo = 1,
  pageSize = 20,
): Promise<IStoryVersion<T>[]> => {
  const managementToken = getManagementToken();
  const baseUrl =
    process.env.REGION === "ap"
      ? "https://api-ap.storyblok.com/v1"
      : "https://api.storyblok.com/v1";
  const params = new URLSearchParams({
    by_story_id: storyId.toString(),
    page: pageNo.toString(),
    per_page: pageSize.toString(),
  });
  const res = await fetch(
    `${baseUrl}/spaces/${spaceId}/story_versions?${params.toString()}`,
    {
      headers: {
        Authorization: managementToken,
        "Content-Type": "application/json",
      },
    },
  );
  if (!res.ok) {
    const errMsg = `Failed to fetch story versions: ${res.statusText}\n ${JSON.stringify({ spaceId, storyId })}`;
    throw new Error(errMsg);
  }
  const data = (await res.json()) as {
    story_versions: IStoryVersion<T>[];
  };
  return data.story_versions;
};

export const fetchSiteShellWithFallback = async (
  locale: string,
  draftMode = false,
  globalLocale = "en",
) => {
  const SiteShell_slug = `${locale}/site-shell`;
  const globalShellSlug = `${globalLocale}/site-shell`;
  const siteShellData = await tryAsyncCalls(
    () =>
      betterFetchStoryBySlug<ISbSiteShellStoryContent>(
        SiteShell_slug,
        draftMode,
      ),
    () =>
      betterFetchStoryBySlug<ISbSiteShellStoryContent>(
        globalShellSlug,
        draftMode,
      ),
  );
  return siteShellData?.story?.content ?? null;
};

export const betterFetchMultipleStories = async <T = IStory>(
  options: ISbStoriesParams,
) => {
  const storyblokApi: StoryblokClient = getStoryblokApi();
  options.token = getToken();
  options.cv = new Date().getTime() / 1000;
  const res = await storyblokApi.get(`cdn/stories`, options);
  return res.data as { stories: T[] };
};
