type WindowWithDataLayer = Window & {
  dataLayer: Record<string, any>[];
};

declare const window: WindowWithDataLayer;

// Constants for Facebook Pixel Events
const fbEvents = {
  LEAD: "Lead",
};

// Constants for Google Tag Manager.
// Apply these classes to any html element and GTM will trigger the corresponding
// GTM tag when the element is clicked. If the element also has an attribute
// called data-gtm-label, it will pass the identity of the element.
//Note: these events should match what's in GTM.
const gtmEvents = {
  INTEREST: "gtm-interest",
  PURCHASE: "gtm-purchase",
  INTERACTION: "gtm-interaction",
  CONTACT_SUBMISSION: "ContactFormSubmit",
  UK_SSA_SUBMISSION: "SSAFormSubmitUK",
  US_SSA_SUBMISSION: "SSAFormSubmitUS",
  CONTACT_SUBMISSION_COMPLETE: "completedSubmission",
  SSA_SUBMISSION: "gtm-ssa-submission",
  FORM_BUILDER_SUMMISSION: "formBuilderSubmission",
  QUIZ_SUBSSMISSION: "quizSubmission",
  NCEA_TO_GPA_FORM_SUBMISSION: "gtm-ncea-gpa-submissions",
  NCEA_TO_GPA_NEXT_BUTTON_CLICKED: "gtm-ncea-to-gpa-next-button-clicked",
  IB_ALEVEL_TO_GPA_FORM_SUBMISSION: "gtm-ib-aLevel-gpa-submissions",
  IB_ALEVEL_TO_GPA_NEXT_BUTTON_CLICKED:
    "gtm-ib-aLevel-to-gpa-next-button-clicked",
  USH_GPA_FORM_SUBMISSION: "gtm-uhs-gpa-submissions",
  USH_GPA_NEXT_BUTTON_CLICKED: "gtm-uhs-to-gpa-next-button-clicked",
  HSC_TO_GPA_FORM_SUBMISSION: "gtm-hsc-gpa-submissions",
  HSC_TO_GPA_NEXT_BUTTON_CLICKED: "gtm-hsc-to-gpa-next-button-clicked",
  VCE_TO_GPA_FORM_SUBMISSION: "gtm-vce-gpa-submissions",
  VCE_TO_GPA_NEXT_BUTTON_CLICKED: "gtm-vce-to-gpa-next-button-clicked",
  USH_TO_GPA_FORM_SUBMISSION: "gtm-ush-gpa-submissions",
  USH_TO_GPA_NEXT_BUTTON_CLICKED: "gtm-ush-to-gpa-next-button-clicked",
  MV_FORM_FIELD: "form-field-interaction-mv",
  MV_FORM_FIELD_ERROR: "form-field-error-mv",
  MV_MEDVIEW_AB_TEST: "Medview AB",
  FB_FORM_TRIGGER: "fb-form-trigger",
  GEC_LEAD_TRIGGER: "gec-lead-trigger",
  GEC_CONTACT_TRIGGER: "gec-contact-trigger",
  TRUCONVERSION_HEATMAP_TRIGGER: "truconversion-heatmap-trigger",
};

const getFacebookEventData = (fbEvent: string, fbEventIdPrefix?: string) => {
  const facebookEventType = fbEvent === "Lead" ? fbEvent : "Contact";
  const facebookEventId = `${fbEventIdPrefix ?? facebookEventType}_${
    new Date().getTime() + Math.random()
  }`;
  let facebookEventSourceUrl = "";
  let clientUserAgent = "";

  if (typeof window !== "undefined") {
    facebookEventSourceUrl = window.location.href;
    clientUserAgent = window.navigator.userAgent;
  }

  return {
    facebookEventType,
    facebookEventId,
    facebookEventSourceUrl,
    clientUserAgent,
  };
};

// TODO: quick impl for now, see if next has built in functionality for this
const getCookie = (name: string) => {
  if (typeof window !== "undefined" && window) {
    const value = `; ${document.cookie}`.match(`;\\s*${name}=([^;]+)`);
    return value ? value[1] : "";
  }
};

const trackFacebookEvent = (event: string, options = {}) => {
  if (window.fbq) {
    window.fbq("track", event, options);
  }
};

const triggerFacebookConversionEvent = (
  event: string,
  eventId: string,
  eventType: string,
  options = {},
) => {
  if (window.dataLayer) {
    const eventObject = Object.assign({}, options, {
      event,
      "x-fb-event_id": eventId,
      "x-fb-event_trigger_type": eventType,
    });
    window.dataLayer.push(eventObject);
  }
};

const triggerEnhancedConversionEvent = (
  event: string,
  email: string,
  phoneNumber: string,
  options = {},
) => {
  if (window.dataLayer) {
    const eventObject = Object.assign({}, options, {
      event,
      email: email,
      phone_number: phoneNumber,
    });
    window.dataLayer.push(eventObject);
  }
};

const triggerCustomEvent = (event: string, gtmLabel: string, options = {}) => {
  if (window.dataLayer) {
    const eventObject = Object.assign({}, options, {
      event,
      "gtm.element.dataset.gtmLabel": gtmLabel,
    });
    window.dataLayer.push(eventObject);
  }
};

export {
  fbEvents,
  getCookie,
  gtmEvents,
  trackFacebookEvent,
  triggerCustomEvent,
  triggerFacebookConversionEvent,
  triggerEnhancedConversionEvent,
  getFacebookEventData,
};
