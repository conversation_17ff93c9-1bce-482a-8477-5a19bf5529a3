const defaultFormComponentNames = [
  "formBuilder",
  "sideImageFormSection",
  "sideTextImageFormSection",
  "sideTextImageGridFormSection",
];

export function getFormCount(
  sections: Array<{ component?: string }> = [],
  formComponentNames: string[] = defaultFormComponentNames,
): number {
  return (
    sections?.filter(
      (section) =>
        section.component && formComponentNames.includes(section.component),
    ).length || 0
  );
}
