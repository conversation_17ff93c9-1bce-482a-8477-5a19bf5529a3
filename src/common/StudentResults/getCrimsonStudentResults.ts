import { fetchDataSource } from "../storyblok";

export interface DatasourceEntry {
  id: number;
  name: string;
  value: string;
  dimension_value: string | null;
}

export async function getCrimsonStudentResults(
  datasourceName: string,
  dimension: string,
): Promise<DatasourceEntry[]> {
  const { data }: { data: { datasource_entries: DatasourceEntry[] } } =
    await fetchDataSource(
      datasourceName,
      true,
      dimension,
      1000,
    );

  return data.datasource_entries;
}
