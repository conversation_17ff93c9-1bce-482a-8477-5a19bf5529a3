import { DatasourceEntry } from "@/common/StudentResults/getCrimsonStudentResults";

export function mergeCrimsonStudentData(entries: DatasourceEntry[]): any[] {
  return entries
    .filter((entry) => entry.dimension_value)
    .map((entry) => {
      try {
        const parsed = JSON.parse(entry.dimension_value!);
        const { dimension_value, ...rest } = entry;
        return {
          ...rest,
          ...parsed,
        };
      } catch (err) {
        console.error("❌ Failed to parse entry:", entry, err);
        return null;
      }
    })
    .filter(<PERSON><PERSON>an);
}
