import { getCrimsonStudentResults } from "@/common/StudentResults/getCrimsonStudentResults";
import { mergeCrimsonStudentData } from "@/common/StudentResults/mergeCrimsonStudentData";
import { CrimsonStudentData } from "@/components/bloks/AllTimeAdmissionsResultsTable/type";

// Harcode to 18 schools only
const HARDCODED_SCHOOL_NAMES = [
  "Princeton University",
  "MIT",
  "Harvard University",
  "Stanford University",
  "Yale University",
  "California Institute of Technology",
  "Duke University",
  "Johns Hopkins University",
  "Northwestern University",
  "University of Pennsylvania",
  "Cornell University",
  "The University of Chicago",
  "Brown University",
  "Columbia University ",
  "Dartmouth College",
  "University of California, LA",
  "University of California, Berkeley",
  "Oxbridge",
];

const datasetMap: Record<string, string> = {
  en: "acceptance-calc",
  es: "es",
  ru: "ru",
  "zh-cn": "zh-cn",
  "zh-tw": "zh-tw",
  th: "th",
  ko: "ko",
  ja: "ja",
  vi: "vi",
  pt: "pt",
  id: "id",
};

const getDatasetKeyByLanguage = (lang?: string) =>
  datasetMap[lang?.toLowerCase?.() ?? "en"] ?? "acceptance-calc";

export const getCrimsonDataset = async (
  datasetId: string,
  lang?: string,
): Promise<{
  dataset: CrimsonStudentData[];
  translationMap: Map<string, string>;
}> => {
  const mainKey = "acceptance-calc";
  const langKey = getDatasetKeyByLanguage(lang);

  const isDefaultLanguage = !lang || lang.trim().toLowerCase() === "en";

  const [rawEntries, translationEntries] = await Promise.all([
    getCrimsonStudentResults(datasetId, mainKey),
    isDefaultLanguage
      ? getCrimsonStudentResults(datasetId, "short-name")
      : langKey !== mainKey
        ? getCrimsonStudentResults(datasetId, langKey)
        : Promise.resolve([]),
  ]);

  const merged: CrimsonStudentData[] =
    mergeCrimsonStudentData(rawEntries) ?? [];

  const dataset: CrimsonStudentData[] = HARDCODED_SCHOOL_NAMES.flatMap(
    (fullName) => {
      const match = merged.find(
        (entry) => entry.name.trim() === fullName.trim(),
      );
      return match ? [match] : [];
    },
  );

  const translationMap = new Map(
    translationEntries
      .filter((entry) => entry.dimension_value !== null)
      .map((entry) => [entry.name, entry.dimension_value!]),
  );

  return { dataset, translationMap };
};
