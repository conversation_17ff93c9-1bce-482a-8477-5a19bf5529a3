import Link from "next/link";
import Text from "@/components/ui/Text";

const BlogTag: React.FC<{
  tagLink: string;
  tagName: string;
}> = ({ tagLink, tagName }) => {
  return (
    <Link
      href={`/blog/tags/${tagLink}`}
      className="inline-flex h-[27px] items-center justify-center rounded-[30.5px] bg-primary01-75 px-[0.9375rem] py-[0.4375rem]"
    >
      <Text tag="span" style="t2" className="text-white">
        {tagName}
      </Text>
    </Link>
  );
};

export default BlogTag;
