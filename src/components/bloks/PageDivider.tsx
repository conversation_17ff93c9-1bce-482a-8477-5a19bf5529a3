import React from "react";
import Container from "@/components/ui/Container";
import { cn } from "@/common/utils";

interface PageDividerProps {
  overrideClass?: string;
  containerClass?: string;
  dividerClass?: string;
}

const PageDivider: React.FC<PageDividerProps> = ({
  overrideClass,
  containerClass,
  dividerClass,
}) => {
  return (
    <div className={cn("bg-transparent", overrideClass)}>
      <Container className={cn("!md:py-0 !lg:py-0 !py-0 px-6", containerClass)}>
        <div className={cn("h-px w-full bg-grays-G7", dividerClass)} />
      </Container>
    </div>
  );
};

export default PageDivider;
