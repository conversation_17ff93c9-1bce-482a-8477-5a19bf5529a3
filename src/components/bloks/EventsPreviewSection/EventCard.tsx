import Button from "@/components/ui/Button";
import Text from "@/components/ui/Text";
import React from "react";
import { IEventHosts } from ".";
import { fetchStoryblokStories } from "@/common/storyblok";
import Link from "next/link";
import EventCardDateLabel from "./EventCardDateLabel";
import { IWebinarEventHeader } from "../WebinarEventHeader";
import { getDataSourceBackgroundImages } from "@/common/utils";
import { useLabelTranslation } from "@/common/hooks/useTranslation";
import EventTypeLabel from "./EventTypeLabel";

type Props = {
  hostedByLabel: string;
  signUpLabel: string;
  name: string;
  eventHeader: IWebinarEventHeader[];
  eventHosts?: IEventHosts[];
  dateTime: string;
  component: string;
  type: [
    { _uid: string; eventType: "In Person" | "Online"; textOverride?: string },
  ];
  eventSlug: string;
  locale: string;
};

interface IHostStory {
  content: {
    Name: string;
    Byline: string;
  };
}

const EventCard = async ({
  hostedByLabel,
  signUpLabel,
  dateTime,
  eventHeader,
  type,
  name,
  eventHosts,
  eventSlug,
  locale,
}: Props) => {
  const typeLabel = type[0]?.eventType ?? "WEBINAR";

  const { desktopImage, mobileImage } = getDataSourceBackgroundImages(
    eventHeader[0]?.backgroundImagePreset ?? "",
  );

  const thumbnailImage =
    mobileImage || eventHeader[0]?.thumbnailImage[0]?.image || desktopImage;

  const eventHostIds = eventHosts
    ?.map((host) => {
      if (host.createHostWithoutProfile && host.hostName && host.hostByline) {
        return null;
      }
      if (host.pickFromProfile) {
        return host.pickFromProfile;
      }
      return null;
    })
    .filter(Boolean) as string[];

  const directHosts = eventHosts
    ?.filter(
      (host) =>
        host.createHostWithoutProfile && host.hostName && host.hostByline,
    )
    .map((host) => `${host.hostName}, ${host.hostByline}`);

  const data: { data: { stories: IHostStory[] } } = eventHostIds?.length
    ? await fetchStoryblokStories<IHostStory>({
        uuids: eventHostIds,
      })
    : { data: { stories: [] } };

  const eventHostData = data?.data?.stories;

  const profileHosts = eventHostData?.map((host) => {
    return `${host.content.Name}, ${host.content.Byline}`;
  });

  const eventHostNamesAndBylines = [
    ...(directHosts ?? []),
    ...(profileHosts ?? []),
  ];

  return (
    <Link
      href={`/${eventSlug}`}
      className="group flex size-full flex-col rounded bg-neutral01-0 transition-shadow hover:shadow-[0px_5px_25px_0px_rgba(0,0,0,0.25)]"
    >
      <div
        className="relative size-full h-[19.4rem] max-h-[19.4rem] overflow-hidden rounded-t"
        style={{
          backgroundImage: `url(${thumbnailImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center right",
        }}
      />

      <div className="flex flex-1 flex-col gap-y-lg p-xl">
        <div>
          <EventCardDateLabel dateTime={dateTime} locale={locale} />

          {typeLabel && <EventTypeLabel typeLabel={typeLabel} />}
        </div>
        <div>
          <Text className="text-primary01-100" tag="p" style="h4">
            {name}
          </Text>
        </div>
        {eventHostNamesAndBylines && eventHostNamesAndBylines?.length > 0 && (
          <div>
            <Text className="text-neutral01-75" tag="p" style="t2">
              {hostedByLabel}
            </Text>
            <Text className="text-neutral01-75" tag="p" style="t2">
              {eventHostNamesAndBylines.join("\n")}
            </Text>
          </div>
        )}

        <div className="mt-auto flex">
          <Button
            theme="link1"
            colour="maroon"
            className="mt-auto grow group-hover:text-primary01-50"
          >
            {signUpLabel} →
          </Button>
        </div>
      </div>
    </Link>
  );
};

export default EventCard;
