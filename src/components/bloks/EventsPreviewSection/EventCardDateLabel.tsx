"use client";

import { formatDateTime } from "@/common/date";
import Text from "@/components/ui/Text";
import { useEffect, useState } from "react";

type Props = { dateTime: string; locale: string };

const EventCardDateLabel = ({ dateTime, locale }: Props) => {
  const [formattedDate, setFormattedDate] = useState("");

  useEffect(() => {
    const dateLocale = locale?.split("-")?.[0] ?? "en";
    const standardDateFormat = "dddd, MMMM D";

    const getFormattedDate = async () => {
      const [dateFormatted, timeFormatted] = await formatDateTime({
        dateTime,
        dateFormat: standardDateFormat,
        timeFormat: "h:mm A",
        locale: dateLocale,
        getDateFormat: (locale: string) => {
          const isSpaceless = /^(ja|zh).*/.test(locale ?? "");
          return isSpaceless ? "MMMDo" : standardDateFormat;
        },
      });

      return `${dateFormatted} · ${timeFormatted}`;
    };

    getFormattedDate()
      .then((formattedDate) => {
        setFormattedDate(formattedDate);
      })
      .catch((error) => {
        console.error(error);
      });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <Text className="mb-[0.37rem] text-primary01-100" tag="p" style="b2">
      {formattedDate}
    </Text>
  );
};

export default EventCardDateLabel;
