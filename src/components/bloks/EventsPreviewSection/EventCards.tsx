import EventCard from "./EventCard";
import SlideCarousel from "@/components/ui/SlideCarousel";
import { StoryblokStory } from ".";
import { isDateExpired } from "@/common/date";

interface EventCardsProps {
  hostedByLabel: string;
  signUpLabel: string;
  eventData: StoryblokStory[];
  locale: string;
}

const normalizeEventData = (eventData: StoryblokStory[]) => {
  return eventData
    .map((event) => {
      const { name, content, slug } = event;
      const {
        eventHeader,
        dateTime,
        component,
        type,
        eventHosts = [],
        hideFromPreview,
      } = content;

      if (!dateTime) return null;

      const isExpired = dateTime && isDateExpired(dateTime);

      if (
        !eventHeader ||
        isExpired ||
        !eventHeader?.[0]?.thumbnailImage?.length ||
        !component ||
        !type ||
        !slug ||
        component !== "WebinarEventPageV2" ||
        hideFromPreview
      )
        return null;

      return {
        name: eventHeader[0]?.title ?? name,
        dateTime,
        component,
        type,
        eventHeader,
        eventHosts,
        slug,
      };
    })
    .filter((event): event is NonNullable<typeof event> => event !== null);
};

const EventCards = ({
  hostedByLabel,
  signUpLabel,
  eventData,
  locale,
}: EventCardsProps) => {
  const normalizedEventData = normalizeEventData(eventData);

  return (
    <SlideCarousel transparent={true} wrapperClassName="!pt-0">
      {normalizedEventData.map((event) => {
        return (
          <EventCard
            eventSlug={`${locale === "en" ? "" : locale + "/"}events/${event.slug}`}
            key={event.name}
            hostedByLabel={hostedByLabel}
            signUpLabel={signUpLabel}
            locale={locale}
            {...event}
          />
        );
      })}
    </SlideCarousel>
  );
};

export default EventCards;
