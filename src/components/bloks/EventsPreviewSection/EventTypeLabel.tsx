"use client";

import { useLabelTranslation } from "@/common/hooks/useTranslation";
import Text from "@/components/ui/Text";
import React from "react";

type Props = {
  typeLabel: string;
};

function EventTypeLabel({ typeLabel }: Props) {
  const { t } = useLabelTranslation();
  return (
    <Text
      className="rounded-[0.19rem] bg-primary01-25 px-[5px] py-[4px] text-primary01-100"
      tag="span"
      style="b3"
    >
      {t(typeLabel)}
    </Text>
  );
}

export default EventTypeLabel;
