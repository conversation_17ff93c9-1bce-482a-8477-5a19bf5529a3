import Button from "@/components/ui/Button";
import Container from "@/components/ui/Container";
import StoryblokLink from "@/components/ui/StoryblokLink";
import Text from "@/components/ui/Text";
import EventCards from "./EventCards";
import { fetchMultipleStories } from "@/common/storyblok";
import { IWebinarEventHeader } from "../WebinarEventHeader";

export interface IEventHosts {
  component: string;
  createHostWithoutProfile: boolean;
  hostByline: string;
  hostName: string;
  pickFromProfile: string;
  _uid: string;
}

export interface IEventsPreviewSectionProps {
  buttonLabel: string;
  component: string;
  heading: string;
  hostedByLabel: string;
  preHeading: string;
  signUpLabel: string;
  _uid: string;
  locale: string;
}

export interface StoryblokStory {
  uuid: string;
  name: string;
  slug: string;
  full_slug: string;
  content: {
    eventHeader: IWebinarEventHeader[];
    eventHosts: IEventHosts[];
    title: string;
    dateTime: string;
    component: string;
    type: [
      {
        _uid: string;
        eventType: "In Person" | "Online";
        textOverride?: string;
      },
    ];
    hideFromPreview: boolean;
  };
}

interface IResult {
  data: {
    stories: StoryblokStory[];
  };
}

export default async function EventsPreviewSection(props: {
  blok: IEventsPreviewSectionProps;
  locale: string;
}) {
  const { blok, locale } = props;
  const { buttonLabel, heading, hostedByLabel, preHeading, signUpLabel } = blok;

  const eventsSlug = `${locale}/events`;
  const data: IResult = await fetchMultipleStories(eventsSlug);

  const eventData = data?.data?.stories;

  if (!eventData || eventData.length === 0) {
    return null;
  }

  return (
    <section>
      <Container className="!pb-3xl lg:!pb-4xl">
        <div className="mb-[6px] flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className="mb-3xl lg:mb-0 lg:max-w-[50%]">
            <Text
              tag="h3"
              style="q4"
              mdStyle="ph1"
              className="italic text-primary01-50"
            >
              {preHeading}
            </Text>
            <Text
              tag="h3"
              style="h3"
              mdStyle="h2"
              className="mt-2 text-primary01-75"
            >
              {heading}
            </Text>
          </div>
          <StoryblokLink
            link={{
              url: `/${locale}/resources/webinars-workshops`,
              cached_url: "/${locale}/resources/webinars-workshops",
              target: "_self",
              linktype: "url",
            }}
          >
            <Button theme="secondary" colour="maroon">
              {buttonLabel}
            </Button>
          </StoryblokLink>
        </div>
      </Container>
      <div>
        <EventCards
          hostedByLabel={hostedByLabel}
          signUpLabel={signUpLabel}
          eventData={eventData}
          locale={locale}
        />
      </div>
    </section>
  );
}
