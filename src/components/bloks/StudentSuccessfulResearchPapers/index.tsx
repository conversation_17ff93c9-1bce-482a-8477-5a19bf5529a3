"use client";

import React from "react";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import { IStudentSuccessResearchSectionProps } from "./types";
import ResearchCard from "./ResearchCardItem";
import { InfiniteCarousel } from "./InfiniteCarousel";
import { getDotBackgroundStyle } from "@/common/getDotBackgroundStyle";

export default function StudentSuccessCapstones(
  props: IStudentSuccessResearchSectionProps,
) {
  const { heading, studentLabel, mentorLabel, outcomeLabel, researchPapers } =
    props.blok;

  return (
    <section className="w-full bg-grays-G6">
      <Container size="full">
        <div className="relative z-0 w-full pt-[2.87rem] md:pt-[3.88rem] lg:pt-[2.41rem] xl:pt-16">
          <div className="mx-auto grid gap-6 px-[25px] md:px-[30px] lg:max-w-[50.0625rem] xl:max-w-[44.5rem] 2xl:max-w-[53.375rem]">
            <Text tag="h4" style="h4" className="text-center">
              {heading}
            </Text>
          </div>
          <div className="mt-3xl lg:mt-12">
            <InfiniteCarousel>
              {researchPapers.map((student, i) => (
                <ResearchCard
                  key={i}
                  {...student}
                  studentLabel={studentLabel}
                  mentorLabel={mentorLabel}
                  outcomeLabel={outcomeLabel}
                />
              ))}
            </InfiniteCarousel>
          </div>
          <Background />
        </div>
      </Container>
    </section>
  );
}

function Background() {
  return (
    <div
      className="absolute inset-x-8 bottom-24 top-0 -z-10 md:bottom-28 lg:inset-x-0 lg:-bottom-12"
      style={getDotBackgroundStyle()}
    />
  );
}
