"use client";

import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/common/utils";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/16/solid";
import IconButton from "@/components/ui/IconButton";
import { useWindowSize } from "@uidotdev/usehooks";
import { debounce } from "@/common/utils";

const LARGE_SCREEN = 1024;

export const InfiniteCarousel = ({
  children,
}: {
  children: React.ReactNode[];
}) => {
  // Duplicate the list to simulate infinite scrolling when reset scroll position
  const mappedChildren = React.Children.map(
    children,
    (child) => child,
  ) as typeof children;
  const extendedImages = [
    ...mappedChildren,
    ...mappedChildren,
    ...mappedChildren,
  ];

  const START_INDEX = mappedChildren.length;
  const END_INDEX = mappedChildren.length * 2;

  const { width: screenWidth } = useWindowSize();
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setIndex] = useState<number>(START_INDEX);
  const [previousIndex, setPreviousIndex] = useState<number>(START_INDEX);

  const selectNext = () =>
    setIndex((index) => {
      setPreviousIndex(index);
      return Math.min(index + 1, END_INDEX);
    });
  const selectPrev = () =>
    setIndex((index) => {
      setPreviousIndex(index);
      return Math.max(index - 1, START_INDEX - 1);
    });

  useEffect(() => {
    if (containerRef.current) {
      const cardElement = containerRef.current.firstElementChild as HTMLElement;
      if (cardElement) {
        const width = cardElement.offsetWidth;
        const cardStyle = window.getComputedStyle(cardElement);
        const gap = parseFloat(cardStyle.marginRight);

        const visibleWidth = screenWidth ?? window.innerWidth;
        const offsetScrollAmount =
          visibleWidth > LARGE_SCREEN ? (visibleWidth - width) / 2 : 0;

        // scroll the carousel
        containerRef.current.style.transition = "left 0.7s ease-in-out";
        containerRef.current.style.left = `${-(currentIndex * (width + gap) - offsetScrollAmount)}px`;

        if (currentIndex === END_INDEX || currentIndex === START_INDEX - 1) {
          // Reset the scroll position to simulate infinity
          const timer = setTimeout(() => {
            if (containerRef.current) {
              setPreviousIndex(currentIndex);
              // offset to the position of the parent immediately
              const wrapIndex =
                currentIndex === END_INDEX ? START_INDEX : END_INDEX - 1;

              // set to position immidiately
              containerRef.current.style.transition = "none";
              containerRef.current.style.left = `${-(
                wrapIndex * (width + gap) -
                offsetScrollAmount
              )}px`;
              setIndex(wrapIndex);
            }
          }, 800);

          return () => {
            clearTimeout(timer);
          };
        }
      }
    }
  }, [currentIndex, screenWidth, mappedChildren.length]);

  useEffect(() => {
    const onResize: () => void = debounce(() => {
      if (containerRef.current) {
        // snap(containerRef.current);
        const container = containerRef.current;
        const containerWidth = container.offsetWidth;

        const targetScrollLeft = currentIndex * containerWidth;

        container.scrollTo({
          left: targetScrollLeft,
        });
      }
    });

    window.addEventListener("resize", onResize);

    return () => {
      window.removeEventListener("resize", onResize);
    };
  }, [currentIndex]);

  const disableAnimate =
    (currentIndex === START_INDEX && previousIndex === END_INDEX) ||
    (currentIndex === END_INDEX - 1 && previousIndex === START_INDEX - 1);

  return (
    <div className="relative overflow-hidden px-[25px]">
      <div className="relative flex" ref={containerRef} style={{}}>
        {extendedImages.map((item, index) => {
          const offsetIndex = currentIndex;
          const isSelected = offsetIndex === index;

          return (
            <div
              className={cn(
                "relative mr-4 min-h-[28.6rem] w-full flex-none lg:my-auto lg:-mr-[40px] lg:w-[70vw] lg:max-w-[48.875rem]",
                !disableAnimate && "transition-transform duration-500",
                !isSelected ? "lg:scale-75 lg:blur-[3px]" : "lg:scale-100",
              )}
              key={index}
            >
              {item}
              {!isSelected && (
                <div className="absolute top-0 z-[1] size-full rounded-[0.97688rem] lg:bg-black/10" />
              )}
            </div>
          );
        })}
      </div>
      <div
        className={cn("mx-auto mt-4xl flex max-w-[800px] justify-end gap-md")}
      >
        <IconButton
          colour={"maroon"}
          onClick={selectPrev}
          Icon={ChevronLeftIcon}
        />
        <IconButton
          colour={"maroon"}
          onClick={selectNext}
          Icon={ChevronRightIcon}
        />
      </div>
    </div>
  );
};
