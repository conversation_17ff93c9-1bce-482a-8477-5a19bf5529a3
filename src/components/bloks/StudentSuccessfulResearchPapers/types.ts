export interface IStudentSuccessResearchSectionProps {
  blok: {
    heading: string;
    studentLabel: string;
    mentorLabel: string;
    outcomeLabel: string;
    researchPapers: IStudentResearchPaper[];
  };
}

export interface IStudentResearchPaperProps extends IStudentResearchPaper {
  studentLabel: string;
  mentorLabel: string;
  outcomeLabel: string;
}

export interface IStudentResearchPaper {
  heading: string;
  publication: string;
  studentName: string;
  studentByline: string;
  mentorName: string;
  mentorByline: string;
  outcome: string;
  collegeLogo: string;
}
