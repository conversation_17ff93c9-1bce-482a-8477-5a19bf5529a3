import React from "react";
import { IStudentResearchPaperProps } from "./types";
import Text from "@/components/ui/Text";
import Image from "next/image";
import { generateImageAltFromFilename } from "@/common/utils";

export default function ResearchCard(props: IStudentResearchPaperProps) {
  const {
    heading,
    publication,
    student<PERSON><PERSON>,
    student<PERSON><PERSON><PERSON>,
    student<PERSON><PERSON><PERSON>,
    mentor<PERSON><PERSON><PERSON>,
    mentor<PERSON><PERSON>,
    mentor<PERSON><PERSON><PERSON>,
    outcome<PERSON>abel,
    outcome,
    collegeLogo,
  } = props;

  return (
    <div className="relative my-auto flex size-full flex-col overflow-hidden rounded-[0.97688rem] bg-white p-6 lg:h-auto">
      <div className="mb-2xl grid gap-3 border-b border-[#F2F2F2] pb-5 md:mb-[1.17rem] lg:mb-12">
        <Text tag="h3" style="t2" mdStyle="h4" className="text-primary01-75">
          {heading}
        </Text>
        <Text
          tag="p"
          style="b4"
          mdStyle="b1"
          className="whitespace-pre text-neutral01-75"
        >
          {publication}
        </Text>
      </div>
      <div className="grid flex-1 gap-6 lg:grid-cols-2">
        <div className="grid gap-6">
          <div className="flex justify-between">
            <div>
              <Label>{studentLabel}</Label>
              <Text tag="p" style="hw5" mdStyle="hw2" className="text-grays-G2">
                {studentName}
              </Text>
              <Text tag="p" style="b5" mdStyle="b4" className="text-grays-G3">
                {studentByline}
              </Text>
            </div>
            {collegeLogo && (
              <Image
                src={collegeLogo}
                alt={generateImageAltFromFilename(collegeLogo)}
                height={200}
                width={200}
                className="size-[5.25rem] object-contain md:hidden"
              />
            )}
          </div>
          <div>
            <Label>{mentorLabel}</Label>
            <Text tag="p" style="b1" className="text-neutral01-75">
              {mentorName}
            </Text>
            <Text tag="p" style="b1" className="text-neutral01-75">
              {mentorByline}
            </Text>
          </div>
        </div>
        <div className="row-span-2 flex flex-col">
          <Label>{outcomeLabel}</Label>
          <Text tag="p" style="b4" mdStyle="b1" className="text-neutral01-75">
            {outcome}
          </Text>
          {collegeLogo && (
            <Image
              src={collegeLogo}
              alt={generateImageAltFromFilename(collegeLogo)}
              height={200}
              width={200}
              className="ml-auto mt-auto hidden size-[5.12869rem] object-contain p-4 md:block lg:size-[6.5625rem]"
            />
          )}
        </div>
      </div>
    </div>
  );
}

const Label = ({ children }: { children: React.ReactNode }) => (
  <Text
    tag="p"
    style="b4"
    mdStyle="b1"
    className="mb-3 inline-block border-b border-b-[#F2F2F2] pb-2 pr-4 text-primary01-75"
  >
    {children}
  </Text>
);
