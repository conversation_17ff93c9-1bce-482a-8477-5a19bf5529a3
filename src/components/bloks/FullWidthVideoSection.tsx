"use client";

import React, { useState } from "react";
import Image from "next/image";
import { PlayIcon } from "@heroicons/react/24/solid";

interface Props {
  blok: {
    videoLabel?: string;
    youtubeUrl: string;
    imageOverride?: string;
  };
}

const extractYouTubeId = (url: string) => {
  const match = /(?:\?v=|\/embed\/|\.be\/)([\w\-]+)/.exec(url);
  return match ? match[1] : null;
};

const FullWidthVideoSection = ({ blok }: Props) => {
  const { videoLabel, youtubeUrl, imageOverride } = blok;
  const [isPlaying, setIsPlaying] = useState(false);
  const [isIframeLoaded, setIsIframeLoaded] = useState(false);

  const videoId = extractYouTubeId(youtubeUrl);
  if (!videoId) return null;

  const thumbnailImage = imageOverride
    ? imageOverride.startsWith("//")
      ? `https:${imageOverride}`
      : imageOverride
    : `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handleIframeLoad = () => {
    setIsIframeLoaded(true);
  };

  return (
    <div className="my-8 flex w-full flex-col items-center">
      <div className="relative aspect-video w-full max-w-6xl overflow-hidden rounded bg-black">
        {isPlaying ? (
          <div className="relative size-full">
            <iframe
              className="size-full rounded"
              src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`}
              title={videoLabel ?? "YouTube video"}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              onLoad={handleIframeLoad}
            />
            {!isIframeLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-black">
                <div className="size-16 animate-spin rounded-full border-4 border-white/80 border-t-transparent" />
              </div>
            )}
          </div>
        ) : (
          <button
            onClick={handlePlay}
            className="relative block size-full cursor-pointer"
          >
            <Image
              src={thumbnailImage}
              alt={videoLabel ?? "video thumbnail"}
              fill
              className="object-cover"
              sizes="(max-width: 1536px) 100vw, 1536px"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="flex size-14 items-center justify-center rounded-full border-4 border-white transition-transform hover:scale-110">
                <PlayIcon className="size-6 text-white" />
              </div>
            </div>
          </button>
        )}
      </div>
      {videoLabel && (
        <h2 className="mt-3 font-body-single text-sm font-normal leading-none text-grays-G4">
          {videoLabel}
        </h2>
      )}
    </div>
  );
};

export default FullWidthVideoSection;
