"use client";

import { useEffect, useState } from "react";
import { isValidElement } from "react";
import { DocumentDuplicateIcon } from "@heroicons/react/24/outline";
import { cn } from "@/common/utils";

interface CodeBlockProps {
  children?: React.ReactNode;
}

const CodeBlock = ({ children }: CodeBlockProps) => {
  const [copied, setCopied] = useState(false);
  const [text, setText] = useState("");

  useEffect(() => {
    if (isValidElement(children)) {
      const codeChildren = (
        children as React.ReactElement<{ children: string | string[] }>
      ).props.children;

      if (typeof codeChildren === "string") {
        setText(codeChildren.trim());
      } else if (Array.isArray(codeChildren)) {
        setText(codeChildren.join("").trim());
      }
    } else if (typeof children === "string") {
      setText(children.trim());
    }
  }, [children]);

  const handleCopy = () => {
    if (!text) return;

    navigator.clipboard.writeText(text).catch((err) => {
      console.error("Failed to copy:", err);
    });

    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  return (
    <div className="relative h-auto w-full rounded bg-[#F9F9F9] px-6 pt-12">
      <pre className="m-0 size-full bg-transparent p-0 pb-6 font-body-single text-base font-normal text-primary01-100">
        {children}
      </pre>
      <button
        aria-label={copied ? "Copied to clipboard" : "Copy to clipboard"}
        onClick={handleCopy}
        className={cn(
          "absolute right-2 top-2 flex items-center gap-1 px-2 py-1 text-xs text-grays-G3 transition-all duration-200 hover:text-primary01-100",
          copied && "text-green-600",
        )}
      >
        {!copied && <DocumentDuplicateIcon className="size-6" />}
        {copied ? "Copied" : ""}
      </button>
    </div>
  );
};

export default CodeBlock;
