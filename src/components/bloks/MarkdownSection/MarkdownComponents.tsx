import React from "react";
import CodeBlock from "./CodeBlock";
import type { Components } from "react-markdown";
import { cn } from "@/common/utils";

export const createMarkdownComponents: (
  overrideClassName?: string,
  listStyle?: "dot" | "line",
) => Partial<Components> = (overrideClassName, listStyle = "line") => {
  return {
    h1: (props) => (
      <h1
        className="mb-4 mt-14 font-display-serif text-4xl font-semibold leading-10 text-black"
        {...props}
      />
    ),
    h2: (props) => (
      <h2
        className="mb-4 mt-14 font-display-serif text-4xl font-semibold leading-10 text-black [&_a]:font-display-serif [&_a]:text-4xl [&_a]:font-semibold [&_a]:leading-10 [&_a]:text-black [&_a]:underline [&_a]:transition-colors [&_a]:duration-300 [&_a]:hover:text-primary01-50"
        {...props}
      />
    ),
    h3: (props) => (
      <h3
        className="mb-2 mt-10 font-display-serif text-3xl font-semibold leading-[1.22] text-black"
        {...props}
      />
    ),
    h4: (props) => (
      <h4
        className="mb-2 font-display-serif text-xl font-semibold leading-[1.22] text-black"
        {...props}
      />
    ),
    h5: (props) => (
      <h5
        className="mb-2 font-display-serif text-lg font-semibold leading-[1.22] text-black"
        {...props}
      />
    ),
    blockquote: (props) => (
      <blockquote
        className="my-4 border-l-2 border-primary01-75 pl-4 font-display-serif text-xl font-semibold italic text-black"
        {...props}
      />
    ),
    p: (props) => {
      return (
        <p
          className={cn(
            "my-3 font-body-single text-base font-normal leading-normal text-black",
            overrideClassName,
          )}
          {...props}
        />
      );
    },
    ul: ({ children, ...props }) => {
      const ulClassName =
        listStyle === "dot"
          ? cn(
              "mb-7 mt-4 pl-8 font-body-single text-base leading-normal text-black list-disc marker:text-primary01-75",
              overrideClassName,
            )
          : cn(
              "mb-7 mt-4 pl-8 font-body-single text-base leading-normal text-black [&>li]:relative [&>li]:before:absolute [&>li]:before:-left-4 [&>li]:before:text-primary01-75 [&>li]:before:content-['-']",
              overrideClassName,
            );

      return (
        <ul className={ulClassName} {...props}>
          {children}
        </ul>
      );
    },
    ol: ({ children, ...props }) => (
      <ol
        className="list-decimal pl-8 font-body-single text-base leading-normal text-black marker:text-primary01-75"
        {...props}
      >
        {children}
      </ol>
    ),
    li: (props) => <li {...props} />,
    a: (props) => (
      <a
        className="font-body-single text-base font-normal leading-tight text-black underline transition-colors duration-300 hover:text-primary01-50"
        {...props}
      />
    ),
    hr: (props) => <hr className="border-grays-G5" {...props} />,
    pre: (props) => <CodeBlock {...props} />,
  };
};
