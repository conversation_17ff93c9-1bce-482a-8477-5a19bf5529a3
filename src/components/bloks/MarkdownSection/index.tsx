import { Theme } from "@/common/types";
import ReactMarkdown from "react-markdown";
import { createMarkdownComponents } from "./MarkdownComponents";
import { cn } from "@/common/utils";

interface Props {
  blok: {
    bodyContent?: string;
    theme?: Theme;
  };
  className?: string;
  components?: Record<string, React.ComponentType<any>>;
  listStyle?: "dot" | "line";
}

const MarkdownSection = ({
  blok,
  className,
  components,
  listStyle = "line",
}: Props) => {
  const { bodyContent } = blok;

  if (!bodyContent) return null;

  const customComponents = createMarkdownComponents(className, listStyle);

  const mergedComponents = components
    ? { ...customComponents, ...components }
    : customComponents;

  return (
    <ReactMarkdown className={cn(className)} components={mergedComponents}>
      {bodyContent}
    </ReactMarkdown>
  );
};

export default MarkdownSection;
