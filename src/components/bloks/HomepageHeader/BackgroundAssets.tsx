import { getResponsiveImageUrl } from "@/common/images";
import { IStoryblokAssetProps } from "@/common/types";
import ImageWrapper from "@/components/ui/ImageWrapper";

const BackgroundAssets = ({
  image,
  mobileImage,
  backgroundVideo,
}: {
  image: IStoryblokAssetProps;
  mobileImage: IStoryblokAssetProps;
  backgroundVideo: IStoryblokAssetProps;
}) => {
  return (
    <div className="absolute inset-0 z-[-1] flex overflow-hidden">
      <ImageWrapper
        src={getResponsiveImageUrl(image.filename, 400, 200)}
        alt={"Desktop Background"}
        width={400}
        height={200}
        sizes="(max-width: 600px) 100vw, (max-width: 1200px) 50vw, 400px"
        className="hidden size-full object-cover lg:block"
        fetchPriority="high"
        priority
      />
      <ImageWrapper
        src={mobileImage.filename}
        alt="Mobile Background"
        width={400}
        height={200}
        fit="cover"
        className="block size-full object-cover lg:hidden"
        sizes="(max-width: 600px) 100vw, (max-width: 1200px) 50vw, 400px"
        quality={60}
      />
      {backgroundVideo?.filename && (
        <video
          className="absolute inset-0 mx-auto hidden size-full object-cover lg:block"
          muted
          playsInline
          preload="auto"
          autoPlay
        >
          <source src={backgroundVideo.filename} type="video/mp4" />
        </video>
      )}
      <div className="absolute inset-0 bg-gradient-to-r from-black to-transparent to-85%" />
    </div>
  );
};

export default BackgroundAssets;
