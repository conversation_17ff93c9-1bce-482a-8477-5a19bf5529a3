import { IStoryblokAssetProps } from "@/common/types";
import Text from "@/components/ui/Text";

export interface IStudent {
  _uid: string;
  name: string;
  image: IStoryblokAssetProps;
  classYear: string;
  college: string;
}

const StudentDetails = ({ student }: { student: IStudent | null }) => {
  if (!student) return null;

  return (
    <div className="absolute bottom-[2.1875rem] flex flex-col justify-center text-primary01-25 md:bottom-48 lg:bottom-[19rem] lg:left-1/2 lg:right-0 lg:hidden lg:max-w-[230px] lg:translate-x-[380px]">
      <div className="animate-[fade-in-up_1s_ease-out_0.5s_forwards] opacity-0">
        <Text tag="p" style="sh5">
          {student?.name}
        </Text>

        <Text tag="p" style="b3" className="flex flex-col">
          <span>{student?.college}</span>
          <span>{student?.classYear},</span>
        </Text>
      </div>
    </div>
  );
};

export default StudentDetails;
