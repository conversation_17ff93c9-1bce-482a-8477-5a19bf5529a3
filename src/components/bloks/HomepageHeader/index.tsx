import { IStoryblokAssetProps } from "@/common/types";
import Container from "@/components/ui/Container";
import { storyblokEditable } from "@storyblok/react/rsc";
import Image from "next/image";
import BackgroundAssets from "./BackgroundAssets";
import StudentDetails, { IStudent } from "./StudentDetails";
import PublisherMarquee from "./PublisherMarquee";
import Text from "@/components/ui/Text";
export interface IHomepageHeaderProps {
  _uid: string;
  label: string;
  heading: string;
  student: IStudent[];
  component: string;
  subheading: string;
  bodyContent: string;
  publisherLogos: string[];
  mobileBackgroundImage: IStoryblokAssetProps;
  desktopBackgroundImage: IStoryblokAssetProps;
  desktopBackgroundVideo: IStoryblokAssetProps;
}

const HomepageHeader = (props: IHomepageHeaderProps) => {
  const student = props.student[0] ?? null;
  return (
    <div
      {...storyblokEditable({ ...props })}
      className="relative overflow-hidden pt-24 lg:pt-36 xl:pt-40 2xl:pt-64"
      id="homepage-header"
    >
      <BackgroundAssets
        image={props.desktopBackgroundImage}
        mobileImage={props.mobileBackgroundImage}
        backgroundVideo={props.desktopBackgroundVideo}
      />

      <Container className="pt-0 sm:pt-0 md:pb-[2.81rem] md:pt-0 lg:pb-12 lg:pt-0">
        {/* Text Content */}
        <div className="mb-72 whitespace-pre-wrap text-center text-white md:mb-[24.5rem] lg:mb-[3.13rem] lg:max-w-[50%] lg:text-left 2xl:relative 2xl:bottom-24">
          <Text
            tag="h1"
            style="q4"
            mdStyle="sh3"
            lgStyle="ph1"
            className="mx-auto mb-2 max-w-[31.25rem] italic lg:mx-0 lg:mt-12 lg:max-w-full"
          >
            {props.heading}
          </Text>
          <Text
            className="mx-auto mb-6 max-w-[31.25rem] lg:mx-0 lg:max-w-full"
            tag="h2"
            style="h3"
            mdStyle="h1"
          >
            {props.subheading}
          </Text>

          <Text
            tag="p"
            style="b2"
            mdStyle="b1"
            className="mx-auto max-w-[31.25rem] lg:mx-0 lg:max-w-full"
          >
            {props.bodyContent}
          </Text>
        </div>

        {/* Animated Student Image */}
        {student?.image?.filename && (
          <div className="absolute bottom-0 left-1/2 w-[15.6875rem] -translate-x-1/2 md:w-[24.25rem] lg:right-0 lg:hidden lg:translate-x-[50px] xl:w-[25.125rem]">
            <Image
              src={student?.image?.filename}
              alt={student?.college ?? "Student profile"}
              className="w-full animate-[fade-in-up_1s_ease-out_0.5s_forwards] opacity-0"
              width={251}
              height={331}
            />
          </div>
        )}

        {/* Gradient Overlay */}
        <div className="absolute inset-x-0 bottom-0 h-[168px] bg-gradient-to-t from-neutral01-100 to-transparent to-[77.53%]" />

        <StudentDetails student={student} />

        <PublisherMarquee
          label={props.label}
          publisherLogos={props.publisherLogos}
        />
      </Container>
    </div>
  );
};

export default HomepageHeader;
