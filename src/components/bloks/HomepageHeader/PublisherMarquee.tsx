import Image from "next/image";
import Marquee from "react-fast-marquee";

const PublisherMarquee = ({
  label = "As seen in",
  publisherLogos,
}: {
  label: string;
  publisherLogos: string[];
}) => {
  return (
    <div className="hidden md:block lg:mt-[3.13rem]">
      <p className="pb-2.5 font-body-single text-body-single-sm text-white">
        {label}
      </p>
      <div className="relative">
        <Marquee speed={38}>
          {publisherLogos.map((logo, index) => (
            <div
              key={logo}
              className="relative mr-[3.91rem] w-full max-w-[250px]"
            >
              <Image
                src={logo}
                alt={`publisher logo - ${index + 1}`}
                unoptimized={true}
                className="w-auto"
                width={0}
                height={0}
              />
            </div>
          ))}
        </Marquee>
      </div>
    </div>
  );
};

export default PublisherMarquee;
