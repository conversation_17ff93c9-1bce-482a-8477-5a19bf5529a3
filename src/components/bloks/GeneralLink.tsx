import { IStoryblokLinkProps } from "@/common/types";
import Link from "next/link";

interface Props {
  blok: IStoryblokLinkProps;
  className?: string;
  children?: React.ReactNode;
}

const GeneralLink = ({ blok, className, children }: Props) => {
  const { targetAnchorId, link, newTab } = blok;

  let href = targetAnchorId ? `#${targetAnchorId}` : "#";

  if (link.url || link.cached_url) {
    const { linktype = "", url = "", cached_url = "" } = link;
    // TODO: revisit to make this more robust + match what the actual component does in storyblok
    href =
      linktype === "url"
        ? url.startsWith("http")
          ? url
          : `https://${url}`
        : `/${cached_url}`;
  }

  return (
    <Link
      href={href}
      target={newTab ? "_blank" : undefined}
      className={className}
    >
      {children}
    </Link>
  );
};

export default GeneralLink;
