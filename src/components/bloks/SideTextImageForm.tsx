import { StoryblokComponent, storyblokEditable } from "@storyblok/react/rsc";
import SideForm from "@/components/ui/SideForm";
import Markdown from "@/components/ui/Markdown";
import { cn } from "@/common/utils";
import type { TBackgroundColour, Theme } from "@/common/types";
import Container from "../ui/Container";
import Text from "../ui/Text";

interface Props {
  blok: {
    backgroundColour: TBackgroundColour;
    bodyImage: [{ _uid: string }];
    bodyHeader?: string;
    bodyContent?: string;
    bodyLayout: string;
    flipLayout: boolean;
    flipMobileLayout: boolean;
    dataTestId: string;
    sectionIndex: number;
    anchorId: string;
    _uid: string;
    formComponent: any[];
    heading: string;
    subheading: string;
    theme: Theme;
  };
  sectionIndex: number;
}

const headerOrder = (sortOrder: string) =>
  sortOrder === "imageHeaderContent" ? "order-2" : "order-1";

const contentOrder = (sortOrder: string) =>
  sortOrder === "headerContentImage" ? "order-2" : "order-3";

const imageOrder = (sortOrder: string) => {
  if (sortOrder === "imageHeaderContent") return "order-1";
  if (sortOrder === "headerImageContent") return "order-2";
  return "order-3";
};

const SideTextImageForm = ({ blok }: Props) => {
  const {
    bodyImage,
    flipLayout,
    flipMobileLayout,
    anchorId,
    _uid,
    formComponent = [],
    heading = "",
    subheading = "",
    theme = "light",
    bodyHeader,
    bodyContent,
    bodyLayout,
  } = blok;

  return (
    <section id={anchorId} {...storyblokEditable(blok)} className="bg-grays-G6">
      <Container>
        <div
          id="standard-web-lead-form"
          className={cn(
            "flex items-center gap-y-[46px] xl:flex-row xl:items-start xl:gap-x-20 xl:pt-[60px]",
            flipLayout ? "xl:flex-row-reverse" : "xl:flex-row",
            flipMobileLayout ? "flex-col-reverse" : "flex-col",
          )}
        >
          <div className="flex max-w-[740px] flex-col px-5 py-8 xl:w-1/2 xl:p-0">
            <Text
              tag="h2"
              style="mh1.5"
              mdStyle="h3"
              className={cn("mb-10 text-primary01-75", headerOrder(bodyLayout))}
            >
              {bodyHeader}
            </Text>

            {bodyContent && (
              <Markdown
                body={bodyContent}
                theme={theme}
                style="body2"
                className={cn(
                  "font-normal md:text-body1",
                  contentOrder(bodyLayout),
                )}
              />
            )}

            {bodyImage?.length > 0 && (
              <div
                className={cn(
                  "mx-auto mt-8 flex w-full xl:max-h-[500px]",
                  imageOrder(bodyLayout),
                )}
              >
                <StoryblokComponent
                  blok={bodyImage[0]}
                  key={bodyImage[0]._uid}
                  fit="contain"
                  sizes="(min-width: 768px) 740px, 100vw"
                />
              </div>
            )}
          </div>

          <div className="w-full xl:w-1/2 xl:rounded-2xl">
            {formComponent?.length > 0 && (
              <SideForm
                formId={_uid}
                {...{ heading, subheading, formComponent }}
              />
            )}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default SideTextImageForm;
