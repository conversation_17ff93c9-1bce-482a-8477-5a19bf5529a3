import { storyblokEditable } from '@storyblok/react/rsc';
import {
  FAQItem,
  IFAQItem,
} from './FAQItem';
import { SbBlokData } from '@storyblok/react';
import Text from '@/components/ui/Text';
import Container from '@/components/ui/Container';

export interface IFaqBlok extends SbBlokData {
  subHeading: string;
  heading: string;
  faqs: IFAQItem[];
}

const FAQ = ({ blok }: { blok: IFaqBlok }) => {
  return (
    <section
      {...storyblokEditable(blok)}
      className="bg-grays-G6"
      data-testid="case-study-faq-section"
    >
      <Container className="!py-10 md:!py-[4.6875rem]">
        <div className="mb-[3.75rem] text-left">
          {blok.subHeading && (
            <Text
              tag="h5"
              style="h5"
              className="text-primary01-50"
            >
              {blok.subHeading}
            </Text>
          )}
          {blok.heading && (
            <Text
              tag="h1"
              style="sh3"
              className="mt-[2.75rem] md:mt-[2.75rem] text-neutral01-100"
            >
              {blok.heading}
            </Text>
          )}
        </div>

        <div>
          {blok.faqs?.map((faqItem) => (
            <FAQItem key={faqItem._uid} blok={faqItem} />
          ))}
        </div>
      </Container>
    </section>
  );
};

export default FAQ;