"use client";

import { useState } from 'react';
import { storyblokEditable, type SbBlokData } from '@storyblok/react';
import { RichEditor } from '@crimson-education/helios-editor-renderer';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { cn } from '@/common/utils';
import Text from '@/components/ui/Text';

export interface IFAQItem extends SbBlokData {
  question: string;
  answer: any[];
}

export const FAQItem = ({ blok }: { blok: IFAQItem }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div
      {...storyblokEditable(blok)}
      className="border-b-[0.6px] border-grays-G5"
      data-testid="faq-item"
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex w-full items-start justify-between gap-[1.875rem] py-[1.875rem] text-left"
        aria-expanded={isOpen}
      >
        <Text
          tag="span"
          style="sh6"
          className="text-neutral01-100"
        >
          {blok.question}
        </Text>
        <div className="flex-shrink-0">
          <ChevronDownIcon
            className={cn(
              'h-6 w-6 transition-transform duration-300 stroke-2',
              isOpen ? 'rotate-180 text-primary01-75' : 'text-grays-G5',
            )}
          />
        </div>
      </button>
      {isOpen && (
        <div
          className="prose prose-light max-w-none pb-[1.875rem] pr-[3rem] md:pr-[4.5rem] prose-p:text-[1.125rem] prose-p:leading-[1.5] prose-p:font-body-p prose-p:mt-0 prose-p:mb-4"
          style={{ '--tw-prose-body': '#56412E' } as React.CSSProperties}
          data-testid="faq-answer"
        >
          <RichEditor value={blok.answer ?? []} />
        </div>
      )}
    </div>
  );
};