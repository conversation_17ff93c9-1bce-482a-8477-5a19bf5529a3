import { storyblokEditable } from "@storyblok/react/rsc";
import type {
  Theme,
  TBackgroundColour,
  ISbDataSourceResult,
} from "@/common/types";
import SectionWrapper from "@/components/ui/SectionWrapper";
import Container from "@/components/ui/Container";
import StatsGridItem, { StatsGridItemProps } from "./StatsGridItem";
import { cn } from "@/common/utils";
import { fetchDataSource } from "@/common/storyblok";

interface Props {
  blok: {
    backgroundColour: TBackgroundColour;
    backgroundImage: string;
    theme: Theme;
    anchorId: string;
    alignment: string;
    sectionTheme: Theme;
    desktopAlignment: string;
    enableAnimations: boolean;
    items: StatsGridItemProps[];
    stylePreset: string;
    usePresets?: boolean;
  };
  sectionIndex: number;
}

/**
 * @deprecated - This component is no longer used in the new design.
 */
const StatsGrid = async ({ blok }: Props) => {
  const { usePresets } = blok;

  const data: ISbDataSourceResult = await fetchDataSource("stats-items");
  const globalStatsItems = data?.data?.datasource_entries;

  return (
    <div {...storyblokEditable(blok)}>
      <SectionWrapper
        backgroundImage={blok.backgroundImage}
        backgroundColour={blok.backgroundColour}
        theme={blok.sectionTheme}
        anchorId={blok.anchorId}
      >
        <Container
          className={cn(
            blok.stylePreset === "stylePreset2" &&
              blok.items.length < 2 &&
              "md:max-w-[80%]",
            blok.stylePreset !== "stylePreset2" && "!py-8",
          )}
        >
          <ul
            className={cn(
              "grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4",
              blok.alignment === "center" ? "text-center" : "text-left",
              blok.desktopAlignment === "center"
                ? "md:text-center"
                : "md:text-left",
            )}
          >
            {usePresets
              ? globalStatsItems?.map((item) => (
                  <StatsGridItem
                    header={item.value}
                    _uid={`${item.id}`}
                    body={item.name}
                    useCTA={false}
                    key={item.id}
                    enableAnimations={blok.enableAnimations}
                  />
                ))
              : blok.items.map((item) => (
                  <StatsGridItem
                    {...item}
                    key={item._uid}
                    enableAnimations={blok.enableAnimations}
                  />
                ))}
          </ul>
        </Container>
      </SectionWrapper>
    </div>
  );
};

export default StatsGrid;
