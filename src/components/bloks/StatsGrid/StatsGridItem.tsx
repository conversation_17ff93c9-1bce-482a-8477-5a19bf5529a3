"use client"

import { StoryblokComponent } from "@storyblok/react/rsc";
import Typography from "@/components/ui/Typography";
import AnimatedIncrementor from "@/components/ui/AnimatedIncrementor";

export interface StatsGridItemProps {
  _uid: string;
  body: string;
  header: string;
  useCTA?: boolean;
  textLink?: [
    {
      _uid: string;
      link: string;
      text: string;
    },
  ];
  largeHeader?: string;
  callToAction?: [{ _uid: string }];
  enableAnimations: boolean;
}

const StatsGridItem = ({
  body,
  header,
  useCTA,
  textLink,
  largeHeader,
  callToAction,
  enableAnimations,
}: StatsGridItemProps) => (
  <li className="flex flex-col">
    <Typography
      tag="h3"
      style={largeHeader ? "h2" : "h3"}
      className="min-h-[44px] font-bold" // workaround for AnimatedIncrementor
    >
      {enableAnimations ? <AnimatedIncrementor target={header} /> : header}
    </Typography>

    <p className="text-body2">{body}</p>

    {!useCTA && textLink && textLink.length > 0 && (
      <StoryblokComponent
        blok={textLink[0]}
        key={textLink[0]._uid}
        className="text-xs"
      />
    )}

    {useCTA && callToAction && callToAction.length > 0 && (
      <StoryblokComponent blok={callToAction[0]} key={callToAction[0]._uid} />
    )}
  </li>
);

export default StatsGridItem;
