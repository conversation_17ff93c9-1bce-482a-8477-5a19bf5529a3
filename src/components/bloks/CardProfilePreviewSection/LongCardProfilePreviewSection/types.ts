export interface LongCardProfilePreviewSectionProps {
  blok: {
    preHeading?: string;
    heading?: string;
    bodyContent?: string;
    profiles: TLongCardProfile[];
  };
}

export interface IPickedLongCardProfile {
  _uid: string;
  createPersonWithoutProfile: false;
  pickFromProfile: string;
}

export interface IPopulatedPickedLongCardProfile {
  _uid: string;
  createPersonWithoutProfile: false;
  pickFromProfile: {
    full_slug: string;
    content: {
      LargeBiography: string;
      Byline: string;
      Name: string;
      ProfilePicture: {
        filename: string;
      };
    };
  };
}

export interface ICustomizedLongCardProfile {
  _uid: string;
  createPersonWithoutProfile: true;
  byline: string;
  name: string;
  largeBiography: string;
  profilePicture: {
    filename: string;
  };
}

export type TLongCardProfile =
  | IPickedLongCardProfile
  | ICustomizedLongCardProfile;

export type TPopulatedLongCardProfile =
  | IPopulatedPickedLongCardProfile
  | ICustomizedLongCardProfile;

export interface ILongProfileCardProps {
  name: string;
  image: string;
  byLine: string;
  largeBiography: string;
  profileSlug?: string;
  uuid: string;
}
