import Container from "@/components/ui/Container";
import { LongProfileCard } from "./LongProfileCard";
import HeadingSection from "@/components/ui/CardProfilePreview/HeadingSection";
import {
  ICustomizedLongCardProfile,
  ILongProfileCardProps,
  LongCardProfilePreviewSectionProps,
  TPopulatedLongCardProfile,
} from "./types";
import ShowMoreLongCard from "./ShowMoreLongCard";
import SlideCarousel from "@/components/ui/SlideCarousel";
import { betterFetchMultipleStories } from "@/common/storyblok";
import { IProfileStory } from "@/common/types";

const checkIfCustomizedLongCardProfile = (
  longCardProfile: TPopulatedLongCardProfile,
): longCardProfile is ICustomizedLongCardProfile => {
  return longCardProfile.createPersonWithoutProfile === true;
};

export const prepareLongProfileCardProps = (
  profile: TPopulatedLongCardProfile,
): ILongProfileCardProps => {
  if (checkIfCustomizedLongCardProfile(profile)) {
    return {
      uuid: profile._uid,
      name: profile.name,
      image: profile.profilePicture.filename,
      byLine: profile.byline,
      largeBiography: profile.largeBiography,
      profileSlug: "",
    };
  }
  return {
    name: profile.pickFromProfile.content.Name,
    image: profile.pickFromProfile.content.ProfilePicture.filename,
    byLine: profile.pickFromProfile.content.Byline,
    largeBiography: profile.pickFromProfile.content.LargeBiography,
    profileSlug: profile.pickFromProfile.full_slug,
    uuid: profile._uid,
  };
};

const INITIAL_BATCH_SIZE = 6;
const BATCH_SIZE = 6;

const LongCardProfilePreviewSection = async ({
  blok,
}: LongCardProfilePreviewSectionProps) => {
  try {
    const { preHeading = "", heading, bodyContent, profiles } = blok;

    const profileUUIDs = profiles
      .filter((profile) => {
        return !profile.createPersonWithoutProfile;
      })
      .map((p) => p.pickFromProfile);
    const { stories } = await betterFetchMultipleStories<IProfileStory>({
      by_uuids: profileUUIDs.join(","),
    });
    const populatedProfiles = profiles.reduce((acc, profile) => {
      if (profile.createPersonWithoutProfile) {
        acc.push(profile);
        return acc;
      }
      const story = stories.find((s) => s.uuid === profile.pickFromProfile);
      if (!story) {
        return acc;
      }
      acc.push({
        ...profile,
        pickFromProfile: story,
      });
      return acc;
    }, [] as TPopulatedLongCardProfile[]);
    const initialProfiles = populatedProfiles.slice(0, INITIAL_BATCH_SIZE);
    const restProfiles = populatedProfiles.slice(INITIAL_BATCH_SIZE);
    return (
      <Container className="flex flex-col py-10 md:pt-[4.6rem]">
        <HeadingSection
          preHeading={preHeading}
          heading={heading}
          bodyContent={bodyContent}
        />
        <div className="hidden lg:block">
          <div className="mx-auto mt-16 flex flex-wrap gap-x-[38px] gap-y-[100px]">
            {initialProfiles.map((profile, idx) => {
              return (
                <div
                  key={idx}
                  className="w-[calc((100%-76px)/3)] rounded-[10px]"
                >
                  <LongProfileCard {...prepareLongProfileCardProps(profile)} />
                </div>
              );
            })}
          </div>
          {restProfiles.length > 0 && (
            <ShowMoreLongCard
              profileList={restProfiles}
              batchSize={BATCH_SIZE}
            />
          )}
        </div>
        <div className="mx-auto mt-16 flex w-full flex-wrap lg:hidden">
          <SlideCarousel
            header={null}
            transparent={true}
            autoCardWrapper={false}
            innerWrapperClassName="!px-[8px]"
          >
            {populatedProfiles.map((profile, idx) => {
              return (
                <div
                  key={idx}
                  className="mr-[16px] w-full flex-none md:w-[calc((100%-16px)/2)] lg:mr-2xl lg:w-[calc((100%-48px)/3)]"
                >
                  <LongProfileCard {...prepareLongProfileCardProps(profile)} />
                </div>
              );
            })}
          </SlideCarousel>
        </div>
      </Container>
    );
  } catch (error) {
    console.error(error);
    return null;
  }
};

export default LongCardProfilePreviewSection;
