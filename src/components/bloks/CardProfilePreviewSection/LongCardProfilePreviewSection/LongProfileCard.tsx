"use client";
import { ILongProfileCardProps } from "./types";
import Text from "@/components/ui/Text";
import CardProfileImage from "@/components/ui/CardProfilePreview/CardProfileImage";
import ProfileBgc from "@/images/Profile-background.webp";
import { useLabelTranslation } from "@/common/hooks/useTranslation";
import StoryblokLink from "@/components/ui/StoryblokLink";

const truncateText = (text: string, maxLength: number) => {
  return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
};

export const LongProfileCard = ({
  name,
  image,
  byLine,
  largeBiography,
  profileSlug,
}: ILongProfileCardProps) => {
  const { t } = useLabelTranslation();
  const inner = (
    <>
      <CardProfileImage
        ProfileBgc={ProfileBgc}
        profileImage={image}
        name={name}
        byline={byLine}
      />
      <div className="flex grow flex-col rounded-b-[4px] p-[20px] shadow-[0px_0px_17.5px_0px_rgba(86,65,46,0.10)]">
        <div className="flex h-full flex-col">
          <div className="line-clamp-[20] grow">
            <Text style="b3" tag="p" lgStyle="b2" className="text-[#88807B]">
              {truncateText(largeBiography, 500)}
            </Text>
          </div>
          {profileSlug && (
            <div className="mt-[20px] flex items-center">
              <span className="cursor-pointer font-body-single text-body-p-md font-bold text-primary01-75 hover:text-primary01-50">
                {`${t("Read More")} →`}
              </span>
            </div>
          )}
        </div>
      </div>
    </>
  );
  return (
    <div className="flex size-full flex-col">
      {profileSlug ? (
        <StoryblokLink
          className="flex h-full flex-col"
          link={{
            newTab: true,
            url: `/${profileSlug}`,
            linktype: "story",
            cached_url: `/${profileSlug}`,
          }}
        >
          {inner}
        </StoryblokLink>
      ) : (
        inner
      )}
    </div>
  );
};
