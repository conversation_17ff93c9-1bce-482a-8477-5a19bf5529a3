"use client";
import ShowMoreProfile from "@/components/ui/CardProfilePreview/ShowMoreProfile";
import { TPopulatedLongCardProfile } from "./types";
import { prepareLongProfileCardProps } from ".";
import { LongProfileCard } from "./LongProfileCard";

const ShowMoreLongCard = ({
  profileList,
  batchSize,
}: {
  profileList: TPopulatedLongCardProfile[];
  batchSize: number;
}) => {
  return (
    <ShowMoreProfile
      items={profileList}
      batchSize={batchSize}
      renderItem={(profile) => {
        const props = prepareLongProfileCardProps(profile);
        return (
          <div
            key={props.uuid}
            className="w-[calc((100%-76px)/3)] rounded-[10px]"
          >
            <LongProfileCard {...props} />
          </div>
        );
      }}
    />
  );
};

export default ShowMoreLongCard;