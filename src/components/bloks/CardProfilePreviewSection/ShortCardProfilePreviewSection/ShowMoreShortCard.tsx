"use client";

import ShortProfileCard from "./ShortProfileCard";
import ShowMoreProfile from "@/components/ui/CardProfilePreview/ShowMoreProfile";
import { IProfileStory } from "@/common/types";

const ShowMoreShortCard = ({ profileList }: { profileList: IProfileStory[] }) => {
    return (
        <ShowMoreProfile
            items={profileList}
            batchSize={6}
            renderItem={(profile, index, ref) => (
                <div
                    key={`${profile.uuid}-${index}`}
                    ref={ref}
                    className="w-full sm:w-[calc((100%-38px)/2)] lg:w-[calc((100%-38px*2)/3)] flex"
                >
                    <ShortProfileCard profileInfo={profile} />
                </div>
            )}
        />
    );
};

export default ShowMoreShortCard;