"use client";

import { IProfileStory } from "@/common/types";
import ProfileBgc from "@/images/Profile-background.webp";
import Text from "@/components/ui/Text";
import CardProfileImage from "@/components/ui/CardProfilePreview/CardProfileImage";
import ReadMore from "@/components/ui/CardProfilePreview/labelSection/ReadMore";

interface ShortProfileCardProps {
  profileInfo: IProfileStory;
}

const ShortProfileCard = ({ profileInfo }: ShortProfileCardProps) => {
  const name = profileInfo.content?.Name ?? profileInfo.name;
  const byline = profileInfo.content?.Byline;
  const profileImage = profileInfo.content?.ProfilePicture?.filename;
  const SmallBiography = profileInfo.content?.SmallBiography;
  const slug = profileInfo.full_slug;

  return (
    <a className="flex size-full cursor-pointer flex-col" href={`/${slug}`}>
      <CardProfileImage
        name={name}
        byline={byline}
        profileImage={profileImage}
        ProfileBgc={ProfileBgc}
      />
      <div className="flex grow flex-col justify-between rounded-b p-6 shadow-[0px_0px_17.5px_0px_rgba(86,65,46,0.10)]">
        {SmallBiography && (
          <Text tag="p" style="b2" className="text-[#88807B]">
            {SmallBiography}
          </Text>
        )}
        <ReadMore />
      </div>
    </a>
  );
};

export default ShortProfileCard;
