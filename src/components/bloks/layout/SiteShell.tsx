import Footer from "@/components/ui/SiteShell/Footer";
import Navbar from "@/components/ui/SiteShell/Navbar";
import BottomCtaBanner from "@/components/ui/SiteShell/Navbar/BottomCtaBanner";
import { ISbSiteShellStoryContent } from "@/components/ui/SiteShell/types";
interface Props {
  children: React.ReactNode;
  locale: string;
  pageType: string;
  siteShellContent: ISbSiteShellStoryContent | null;
}

const SiteShell = async ({
  children,
  locale,
  pageType,
  siteShellContent,
}: Props) => {
  if (!siteShellContent) {
    return <>{children}</>;
  }
  try {
    const footerProps = {
      ourServicesFooter: siteShellContent.ourServicesFooter,
      aboutCrimsonFooter: siteShellContent.aboutCrimsonFooter,
      eventsLabel: siteShellContent.eventsLabel,
      ourServicesHeader: siteShellContent.ourServicesHeader,
      aboutCrimsonHeader: siteShellContent.aboutCrimsonHeader,
      admissionsResourcesHeader: siteShellContent.admissionsResourcesHeader,
      upcomingEventsLabel: siteShellContent.upcomingEventsLabel,
      followUsLabel: siteShellContent.followUsLabel,
      socialMediaIcons: siteShellContent.socialMediaIcons,
      sitemapLabel: siteShellContent.sitemapLabel,
      termsOfUseLabel: siteShellContent.termsOfUseLabel,
      privacyPolicyLabel: siteShellContent.privacyPolicyLabel,
      cookiePreferencesLabel: siteShellContent.cookiePreferencesLabel,
      declarationLabel: siteShellContent.declarationLabel,
    };

    const navbarProps = {
      eventsLabel: siteShellContent.eventsLabel,
      ourServicesHeader: siteShellContent.ourServicesHeader,
      aboutCrimsonHeader: siteShellContent.aboutCrimsonHeader,
      admissionsResourcesHeader: siteShellContent.admissionsResourcesHeader,
      stickyNavigationMessage: siteShellContent.stickyNavigationMessage,
      stickyCtaLabel: siteShellContent.stickyCtaLabel,
      stickyPageUrl: siteShellContent.stickyPageUrl,
      pageType: pageType,
      ctaButtonLabel: siteShellContent.ctaButtonLabel,
    };

    return (
      <section>
        <Navbar {...navbarProps} />
        {children}
        <BottomCtaBanner
          title={siteShellContent.stickyNavigationMessage}
          buttonText={siteShellContent.ctaButtonLabel}
          isInline={false}
          pageType={pageType}
        />
        <Footer locale={locale} {...footerProps} />
      </section>
    );
  } catch (err: any) {
    console.error("SiteShell Error ~ 100", err);
    return <>{children}</>;
  }
};

export default SiteShell;
