"use client";

export type OneTrustLinkProps = {
  children: React.ReactNode;
};

const OneTrustLink = ({ children }: OneTrustLinkProps) => (
  <button
    onClick={() => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      window?.OneTrust?.ToggleInfoDisplay();
    }}
    className="underline hover:text-primary01-50"
  >
    {children}
  </button>
);

export default OneTrustLink;
