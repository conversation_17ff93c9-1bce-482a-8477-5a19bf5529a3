"use client";

import React, { useState, useEffect } from "react";
import { storyblokEditable } from "@storyblok/react/rsc";
import { formatDateTime } from "@/common/date";
import type { Theme, TBackgroundColour } from "@/common/types";
import Text from "../ui/Text";
import TimeIcon from "../icons/Time";
import CalendarIcon from "../icons/Calendar";
import LocationIcon from "../icons/Location";
import Container from "../ui/Container";

interface BannerRowProps {
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  children: React.ReactNode;
}

const BannerRow = ({ icon: Icon, children }: BannerRowProps) => {
  return (
    <li className="flex items-center justify-center gap-3">
      <span className="inline-flex text-white">
        <Icon className="size-6" />
      </span>
      <Text tag="span" style="mb1" mdStyle="b1">
        {children}
      </Text>
    </li>
  );
};

interface Props {
  blok: {
    cost: string;
    theme: Theme;
    location: string;
    backgroundColour: TBackgroundColour;
    dateOverrideText: string;
    timeOverrideText: string;
  };
  dateTime: string;
}

const WebinarEventDetailsBanner = ({ blok, dateTime }: Props) => {
  const [date, setDate] = useState<string[]>([]);
  const [dateFormatted, timeFormatted] = date;

  useEffect(() => {
    formatDateTime({ dateTime })
      .then((formattedDate) => setDate(formattedDate))
      .catch(() => setDate([]));
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div {...storyblokEditable(blok)} className="bg-primary01-75 text-white">
      <Container className="!py-6 md:!py-8 xl:mx-auto xl:max-w-[848px]">
        <ul className="flex w-full flex-col gap-3 p-0 md:flex-row md:justify-between">
          <BannerRow icon={CalendarIcon}>
            {blok.dateOverrideText || dateFormatted}
          </BannerRow>
          <LineSeparator />
          <BannerRow icon={TimeIcon}>
            {blok.timeOverrideText || timeFormatted}
          </BannerRow>
          <LineSeparator />

          <BannerRow icon={LocationIcon}>{blok.location}</BannerRow>
        </ul>
      </Container>
    </div>
  );
};

const LineSeparator = () => {
  return (
    <span className="hidden md:block">
      <svg
        width="2"
        height="21"
        viewBox="0 0 2 21"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <line x1="1" y1="2.18557e-08" x2="0.999999" y2="21" stroke="#DFDFDF" />
      </svg>
    </span>
  );
};

export default WebinarEventDetailsBanner;
