import React from "react";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import { StarIcon } from "@heroicons/react/24/solid";

import { cn } from "@/common/utils";

interface IProps {
  blok: {
    header: string;
    bodyCopy: string;
    activityList: {
      typeHeader: string;
      header: string;
      body: string;
    }[];
  };
}

const CaseStudyExtracurricular: React.FC<IProps> = ({
  blok: { header, bodyCopy, activityList = [] },
}) => {
  return (
    <Container size="caseStudy" className="bg-white">
      <div className="flex w-full flex-col items-center justify-between py-[40px] md:flex-col md:py-[75px] lg:flex-row lg:py-[75px]">
        <div className="w-full md:w-full lg:w-[36%] xl:w-[41%] 2xl:w-[44%]">
          <div className="flex items-center">
            <StarIcon className="size-4 text-primary01-50" />
            <Text tag="h5" style="sh5" className="ml-[6px]">
              {header}
            </Text>
          </div>
          <Text tag="p" style="b2" className="mt-[14px]">
            {bodyCopy}
          </Text>
        </div>
        <div className="scrollbar-thumb-gray-400/50 scrollbar-thumb-rounded-lg mt-[60px] max-h-[528px] w-full overflow-y-auto scrollbar-thin scrollbar-track-transparent md:mt-[60px] md:w-full lg:mt-0 lg:w-fit">
          {activityList.map(({ typeHeader, header, body }, idx) => (
            <div
              key={idx}
              className={cn(
                "flex min-h-[50px] items-center justify-between p-[15px]",
                idx % 2 === 0 ? "bg-white" : "bg-neutral01-0",
              )}
            >
              <Text
                style="sh7"
                tag="h6"
                className="w-[100px] hyphens-auto break-words text-primary01-75"
              >
                {typeHeader}
              </Text>
              <div className="ml-[20px] flex-1 md:ml-[20px] md:flex-1 lg:ml-[20px] lg:w-[332px]">
                <Text
                  tag="h5"
                  style="b2"
                  className="font-semibold text-neutral01-75"
                >
                  {header}
                </Text>
                <Text tag="p" style="b2" className="text-neutral01-75">
                  {body}
                </Text>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Container>
  );
};

export default CaseStudyExtracurricular;
