"use client";

import { IForm, Theme } from "@/common/types";
import { SbBlokData, storyblokEditable } from "@storyblok/react/rsc";
import Container from "@/components/ui/Container";
import Form from "@/components/ui/Forms/Form";
import { cva } from "class-variance-authority";
import FormHeaders from "@/components/ui/Forms/FormHeaders";
import { useState } from "react";
import { getDotBackgroundStyle } from "@/common/getDotBackgroundStyle";

interface IFormProps extends SbBlokData {
  form: IForm[];
  reason?: string;
  fbEventIdPrefix?: string;
  disableRememberMeOptIn?: boolean;
  _uid: string;
  anchorId: string;
  theme: Theme;
  heading: string;
  subheading: string;
}

interface Props {
  blok: IFormProps;
  sectionIndex: number;
}

const formWrapperVariants = cva("", {
  variants: {
    theme: {
      light: "bg-grays-G6 text-neutral01-100",
      dark: "bg-blue-700 text-white",
    },
  },
  defaultVariants: {
    theme: "light",
  },
});

const SingleColumnForm = ({ blok }: Props) => {
  const { anchorId, heading, subheading, form } = blok;
  const [showHeaders, setShowHeaders] = useState(true);

  // Hardcode for now until we are ready to support dark mode
  const theme = "light";
  form[0]!.theme = theme;

  return (
    <section
      id={anchorId}
      className={formWrapperVariants({ theme })}
      {...storyblokEditable(blok)}
    >
      <Container
        style={getDotBackgroundStyle()}
      >
        <div id="standard-web-lead-form">
          <div className="mx-auto grid max-w-[55.8125rem] grid-cols-1 gap-3xl rounded-xl bg-white p-6 shadow-[0px_2px_20px_0px_rgba(86,65,46,0.10)] md:p-10">
            {showHeaders && (
              <div>
                <FormHeaders heading={heading} subheading={subheading} />
              </div>
            )}

            <div className="mx-auto w-full bg-white">
              {form?.length > 0 && (
                <Form
                  form={blok.form}
                  formId={blok._uid}
                  reason="Filled single column form"
                  setShowHeaders={setShowHeaders}
                />
              )}
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default SingleColumnForm;
