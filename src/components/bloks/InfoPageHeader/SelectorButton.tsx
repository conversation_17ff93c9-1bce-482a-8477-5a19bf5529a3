"use client";

import Button from "@/components/ui/Button";
import { IStudyInSelector } from "./StudyInSelectorSection";

import { usePathname } from "next/navigation";
import { IStory } from "@/common/types";
import StoryblokLink from "@/components/ui/StoryblokLink";
import { usePageContext } from "@/components/context/PageContext";
import { cn, getSlugWithoutLocale } from "@/common/utils";

interface Props {
  selector: IStudyInSelector;
  index?: number;
  hyperlinksData?: IStory;
}

const SelectorButton = ({ selector, hyperlinksData }: Props) => {
  const currentSlug = usePathname();
  const { locale } = usePageContext();

  const isCurrentPage = `/${hyperlinksData?.full_slug}` === currentSlug;
  const slugWithoutLocale = getSlugWithoutLocale(
    hyperlinksData?.full_slug ?? "",
  );

  // TODO: Re-instate this before we go live (it removes EN from the slug which we don't want in production)
  // But for the sake of the demo, we'll use the slug with the locale
  // const redirectUrl =
  //   process.env.NODE_ENV === "production" && locale === "en"
  //     ? `${slugWithoutLocale}`
  //     : `/${locale}${slugWithoutLocale}`;

  const redirectUrl = `/${locale}${slugWithoutLocale}`;

  return (
    <StoryblokLink
      link={{
        cached_url: redirectUrl,
        linktype: "Story",
        url: redirectUrl,
      }}
    >
      <Button
        key={selector.hyperlink}
        colour="darkGrey"
        theme="primary"
        className={cn(isCurrentPage && "!bg-primary01-50")}
      >
        {selector.heading}
      </Button>
    </StoryblokLink>
  );
};

export default SelectorButton;
