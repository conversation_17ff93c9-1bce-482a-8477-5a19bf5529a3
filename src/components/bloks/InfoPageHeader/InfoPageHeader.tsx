import { SbBlokData, storyblokEditable } from "@storyblok/react/rsc";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import ImageWrapper from "@/components/ui/ImageWrapper";
import { cn, getDataSourceBackgroundImages } from "@/common/utils";
import StudyInSelectorSection, {
  IStudyInSelector,
} from "./StudyInSelectorSection";

export interface IInfoPageHeader extends SbBlokData {
  preHeading: string;
  heading: string;
  bodyContent: string;
  studyInLabel: string;
  studyInSelector: IStudyInSelector[];
  backgroundImage: string;
  gradientOverlay: "black" | "deepMaroon";
}

interface Props {
  blok: IInfoPageHeader;
  pageType: string;
}

const InfoPageHeader = (props: Props) => {
  const { blok, pageType } = props;

  const {
    heading,
    preHeading,
    bodyContent,
    studyInLabel,
    studyInSelector,
    backgroundImage,
    gradientOverlay,
  } = blok;

  const { desktopImage, mobileImage } = getDataSourceBackgroundImages(
    backgroundImage ?? "",
  );

  const hideGradientOverlay = studyInSelector.length < 2;

  return (
    <section className="relative pt-10 text-white" {...storyblokEditable(blok)}>
      <div className="absolute inset-0 z-0 flex overflow-hidden">
        <ImageWrapper
          src={desktopImage}
          alt={heading}
          sizes="100vw"
          className="hidden size-full object-cover object-[top_center] md:block"
          width={2880}
          height={1200}
          fetchPriority="high"
          priority
          quality={80}
        />
        <ImageWrapper
          src={mobileImage}
          alt={heading}
          sizes="(max-width: 600px) 100vw, (max-width: 1200px) 50vw, 400px"
          className="block size-full object-cover object-center md:hidden"
          width={500}
          height={700}
          fetchPriority="high"
          priority
        />
      </div>
      <div
        className={cn(
          "absolute bottom-0 h-[375px] w-full bg-gradient-to-t from-30% md:hidden",
          gradientOverlay === "deepMaroon"
            ? "from-primary01-100"
            : "from-black opacity-70",
          hideGradientOverlay && "hidden",
        )}
      />
      <div
        className={cn(
          "absolute top-0 h-[375px] w-full bg-gradient-to-b from-0% opacity-70 md:hidden",
          gradientOverlay === "deepMaroon"
            ? "from-primary01-100"
            : "from-black",
        )}
      />
      <div
        className={cn(
          "absolute left-0 top-0 hidden h-full w-[70%] bg-gradient-to-r from-50% md:block",
          gradientOverlay === "deepMaroon"
            ? "from-primary01-100"
            : "from-black opacity-70",
        )}
      />
      <Container className="relative z-[2]">
        <div className="mb-[14.06rem] flex flex-col items-center justify-between md:mb-7 md:max-w-[33rem] md:items-start">
          <Text
            tag="h3"
            style="mph1"
            mdStyle="ph1"
            className="pb-2 italic text-white"
          >
            {preHeading}
          </Text>
          <Text
            tag="h2"
            style="mh1.5"
            mdStyle="h1"
            className="pb-7 text-center text-white md:text-left"
          >
            {heading}
          </Text>
          <Text
            tag="p"
            style="mb1"
            mdStyle="b1"
            className="text-center text-grays-G6 md:text-left"
          >
            {bodyContent}
          </Text>
        </div>
        {studyInSelector.length >= 2 && pageType === "infoPage" && (
          <StudyInSelectorSection
            items={studyInSelector}
            studyInLabel={studyInLabel}
          />
        )}
      </Container>
    </section>
  );
};

export default InfoPageHeader;
