import Text from "@/components/ui/Text";
import { fetchStoryblokStories } from "@/common/storyblok";
import SelectorButton from "./SelectorButton";
import { IBlogStory } from "../NewsAndArticles/utils";

export interface IStudyInSelector {
  heading: string;
  hyperlink: string;
}

interface Props {
  items: IStudyInSelector[];
  studyInLabel: string;
}

const StudyInSelectorSection = async ({ items, studyInLabel }: Props) => {
  const hyperLinkUUIDs = items.map((item) => item.hyperlink);

  const isDraftMode = process.env.NODE_ENV !== "production";

  const hyperlinks: { data?: { stories?: IBlogStory[] } } =
    await fetchStoryblokStories({
      draftMode: isDraftMode,
      uuids: hyperLinkUUIDs,
    });

  const hyperlinksData = hyperlinks.data?.stories ?? [];

  if (hyperlinksData.length === 0) {
    return null;
  }

  const hyperlinksMap = hyperlinksData.reduce<Record<string, IBlogStory>>(
    (acc, story) => {
      if (story.uuid) {
        acc[story.uuid] = story;
      }
      return acc;
    },
    {}
  );

  return (
    <div>
      <>
        <Text
          tag="p"
          style="mb1"
          mdStyle="b1"
          className="pb-3 text-center text-neutral01-0 md:text-left"
        >
          {studyInLabel}
        </Text>
        <div className="flex items-center justify-center gap-3 md:justify-start">
          {items.map((selector, index) => (
            <SelectorButton
              key={selector.hyperlink}
              selector={selector}
              index={index}
              hyperlinksData={hyperlinksMap[selector.hyperlink]}
            />
          ))}
        </div>
      </>
    </div>
  );
};

export default StudyInSelectorSection;
