"use client"

import { lazy, Suspense } from "react";
import { Carousel } from "nuka-carousel";
import type { Theme, TBackgroundColour } from "@/common/types";
import { CarouselItemProps } from "./CarouselItem";
import { CustomDots } from "./CustomDots";
import { CustomArrows } from "./CustomArrows";
import CarouselItemSkeleton from "./CarouselItemSkeleton";

const CarouselItem = lazy(() => import("./CarouselItem"));


interface Props {
  blok: {
    heading: string;
    subheading: string;
    backgroundColour: TBackgroundColour;
    backgroundImage: string;
    theme: Theme;
    anchorId: string;
    flipLayout: boolean;
    callToAction: [{ _uid: string }];
    carouselItems: CarouselItemProps[];
    carouselTheme: Theme;
    leftBackgroundImage: [{ _uid: string }];
    rightBackgroundImage: [{ _uid: string }];
    onlyShowOneOnDesktop: boolean;
    carouselBackgroundColour: TBackgroundColour;
  };
  showControls: boolean;
  carouselTheme: Theme;
}

const CarouselWrapper = ({
  showControls, blok, carouselTheme
}: Props) => {
  return (
    <Carousel
      autoplay
      showDots={showControls}
      showArrows={showControls}
      wrapMode="wrap"
      className="relative"
      autoplayInterval={7000}
      dots={<CustomDots />}
      arrows={<CustomArrows />}
    >
      <Suspense fallback={<CarouselItemSkeleton />}>
        {blok.carouselItems.map((item) => (
          <CarouselItem
            {...item}
            key={item._uid}
            theme={carouselTheme}
          />
        ))}
      </Suspense>
    </Carousel> 
  )
}

export default CarouselWrapper