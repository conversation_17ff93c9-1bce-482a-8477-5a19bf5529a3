import { cn } from "@/common/utils";
import { useCarousel } from "nuka-carousel";
import { ChevronLeftIcon } from "@heroicons/react/24/solid";
import { ChevronRightIcon } from "@heroicons/react/24/solid";

export function CustomArrows() {
  const { currentPage, totalPages, wrapMode, goBack, goForward } =
    useCarousel();

  const allowWrap = wrapMode === "wrap";
  const enablePrevNavButton = allowWrap || currentPage > 0;
  const enableNextNavButton = allowWrap || currentPage < totalPages - 1;

  const prevNavClassName = cn(
    "inline-block px-4 py-2 cursor-pointer invisible",
    enablePrevNavButton && "!visible",
  );

  const nextNavClassName = cn(
    "inline-block px-4 py-2 cursor-pointer invisible",
    enableNextNavButton && "!visible",
  );

  return (
    <div className="absolute -bottom-6 right-8 z-10 hidden justify-between md:flex">
      <button className={prevNavClassName} onClick={goBack}>
        <ChevronLeftIcon className="size-6" />
      </button>
      <button className={nextNavClassName} onClick={goForward}>
        <ChevronRightIcon className="size-6" />
      </button>
    </div>
  );
}
