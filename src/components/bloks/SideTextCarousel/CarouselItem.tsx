import { lazy, Suspense } from "react";
import Typography from "@/components/ui/Typography";
import type { IStoryblokLinkProps, Theme } from "@/common/types";
import Skeleton from "@/components/ui/Skeleton";
import GeneralLink from "../GeneralLink";
import ImageWithAltText from "../ImageWithAltText";

const Markdown = lazy(() => import("@/components/ui/Markdown"));

export interface CarouselItemProps {
  _uid: string;
  body: string;
  heading: string;
  subheading: string;
  wrapperLink: IStoryblokLinkProps[];
  profileImage: [{
    image: string;
    altText: string;
    _uid: string;
  }];
  theme: Theme;
}

const CarouselItem = ({
  heading,
  subheading,
  body,
  profileImage,
  wrapperLink,
  theme,
}: CarouselItemProps) => {
  return (
    <div className="relative flex min-w-full flex-col py-8 md:flex-row md:space-x-[50px]">
      {wrapperLink[0] && (
        <GeneralLink blok={wrapperLink[0]} className="absolute inset-0" />
      )}
      {profileImage && (
        <ImageWithAltText
          blok={profileImage[0]}
          key={profileImage[0]._uid}
          width={205}
          height={205}
          fit="cover"
          sizes="(min-width: 768px) 205px, 165px"
          className="mx-auto mb-5 size-[165px] shadow-xl md:mt-1 md:size-[205px]"
        />
      )}

      <div className="grow px-5 md:pl-0 md:pr-8">
        <Typography
          tag="h3"
          style="h3"
          className="mb-4 font-bold md:text-[28px] md:leading-[35px]"
        >
          {heading}
        </Typography>

        {subheading && (
          <Typography tag="p" style="body2" className="mb-5 font-semibold">
            {subheading}
          </Typography>
        )}

        {body && (
          <Suspense fallback={<Skeleton theme={theme} className="h-20" />}>
            <Markdown body={body} theme={theme} style="body2" />
          </Suspense>
        )}
      </div>
    </div>
  );
};

export default CarouselItem;
