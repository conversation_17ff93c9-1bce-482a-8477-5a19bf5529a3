import { Theme } from "@/common/types";
import Skeleton from "@/components/ui/Skeleton";

const CarouselItemSkeleton = ({ theme = "light" }: { theme?: Theme }) => {
  return (
    <div className="flex min-w-full flex-col py-8 md:flex-row md:space-x-[50px]">
      <Skeleton
        className="mx-auto mb-5 size-[165px] shadow-xl md:mt-1 md:size-[205px]"
        theme={theme}
      />

      <div className="grow px-8 md:pl-0">
        <Skeleton theme={theme} className="mb-4 h-8 w-full" />
        <Skeleton theme={theme} className="mb-5 h-8 w-full" />
        <Skeleton theme={theme} className="h-20 w-full" />
      </div>
    </div>
  );
};

export default CarouselItemSkeleton;
