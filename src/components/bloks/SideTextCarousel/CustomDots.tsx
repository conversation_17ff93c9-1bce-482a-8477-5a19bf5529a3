import { cn } from "@/common/utils";
import { useCarousel } from "nuka-carousel";

export const CustomDots = () => {
  const { totalPages, currentPage, goToPage } = useCarousel();
  return (
    <div className="relative inset-x-0 flex items-center justify-center gap-1 pb-8 md:left-[270px] md:justify-start">
      {[...Array(totalPages)].map((_, index) => (
        <button
          key={index}
          onClick={() => goToPage(index)}
          className={cn(
            "size-2 cursor-pointer rounded-full border-none bg-grey-400 p-0",
            currentPage === index && "bg-orange-300",
          )}
        />
      ))}
    </div>
  );
};
