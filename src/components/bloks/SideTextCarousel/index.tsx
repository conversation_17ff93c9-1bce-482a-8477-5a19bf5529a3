import { storyblokEditable, StoryblokComponent } from "@storyblok/react/rsc";
import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";
import SectionWrapper from "@/components/ui/SectionWrapper";
import Typography from "@/components/ui/Typography";
import type { Theme, TBackgroundColour } from "@/common/types";
import Container from "@/components/ui/Container";
import { CarouselItemProps } from "./CarouselItem";
import CarouselWrapper from "./CarouselWrapper";

interface Props {
  blok: {
    heading: string;
    subheading: string;
    backgroundColour: TBackgroundColour;
    backgroundImage: string;
    theme: Theme;
    anchorId: string;
    flipLayout: boolean;
    callToAction: [{ _uid: string }];
    carouselItems: CarouselItemProps[];
    carouselTheme: Theme;
    leftBackgroundImage: [{ _uid: string }];
    rightBackgroundImage: [{ _uid: string }];
    onlyShowOneOnDesktop: boolean;
    carouselBackgroundColour: TBackgroundColour;
  };
  sectionIndex: number;
}

// TODO leftBackgroundImage, rightBackgroundImage, onlyShowOneOnDesktop

const carouselVariants = cva("", {
  variants: {
    carouselBackgroundColour: {
      navy: "bg-blue-700",
      charcoal: "bg-blue-900",
      indigo: "bg-blue-500",
      mulberry: "bg-maroon-700",
      salmon: "bg-red-300",
      white: "bg-white",
      yellow: "bg-yellow-500",
      whisper: "bg-grey-100",
      mist: "bg-grey-50",
      tiffany: "bg-green-900",
      sky: "bg-blue-200",
    },
    carouselTheme: {
      light: "text-blue-700",
      dark: "text-white",
    },
  },
});

/**
 * @deprecated - This component is no longer used in the new design.
 */
const SideTextCarousel = ({ blok }: Props) => {
  const showControls = blok.carouselItems.length > 1;
  const { carouselBackgroundColour, carouselTheme } = blok;

  return (
    <div {...storyblokEditable(blok)}>
      <SectionWrapper
        backgroundImage={blok.backgroundImage}
        backgroundColour={blok.backgroundColour}
        theme={blok.theme}
        anchorId={blok.anchorId}
      >
        <Container className="px-0 pb-0 lg:pb-[60px]">
          <div className="flex flex-col gap-y-4 overflow-hidden md:gap-y-12 xl:flex-row xl:gap-x-[120px] xl:gap-y-14">
            <div
              className={cn(
                "mx-auto w-full px-5 md:px-0 xl:w-2/5 xl:pt-8",
                blok.flipLayout && "order-1",
              )}
            >
              <Typography
                tag="h2"
                style="h2"
                className="mb-4 md:text-[28px] md:leading-[35px] xl:mb-6"
              >
                {blok.heading}
              </Typography>

              {blok.subheading && (
                <Typography
                  tag="p"
                  style="body2"
                  className="font-normal md:text-body1"
                >
                  {blok.subheading}
                </Typography>
              )}

              {blok.callToAction.length > 0 && (
                <div className="mt-8">
                  <StoryblokComponent
                    blok={blok.callToAction[0]}
                    key={blok.callToAction[0]._uid}
                  />
                </div>
              )}
            </div>

            <div className="w-full md:mx-0 xl:w-3/5">
              <div
                className={cn("relative", carouselVariants({ carouselTheme }))}
              >
                <div
                  className={cn(
                    "absolute inset-0 top-[130px] md:left-[100px] md:top-0",
                    carouselVariants({ carouselBackgroundColour }),
                  )}
                ></div>

                <CarouselWrapper
                  showControls={showControls}
                  blok={blok}
                  carouselTheme={carouselTheme}
                />
              </div>
            </div>
          </div>
        </Container>
      </SectionWrapper>
    </div>
  );
};

export default SideTextCarousel;
