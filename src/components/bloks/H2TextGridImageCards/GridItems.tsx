import Text from "@/components/ui/Text";
import React from "react";
import { IH2GridItems } from "./types";

export default function GridItems({
  gridItems,
}: {
  gridItems: IH2GridItems[];
}) {
  return (
    <>
      {gridItems.map((item, index) => (
        <div key={item._uid} className="flex items-start gap-6">
          <div className="h-auto">
            <GridItemSideLineIcon />
          </div>
          <div>
            <Text
              tag="p"
              style="sh2"
              className="text-primary01-50 md:font-light"
            >
              0{index + 1}
            </Text>
            <Text tag="p" style="b2" mdStyle="b1" className="text-neutral01-75">
              {item.bodyContent}
            </Text>
          </div>
        </div>
      ))}
    </>
  );
}

const GridItemSideLineIcon = () => {
  return (
    <svg
      width="1"
      height="47"
      viewBox="0 0 1 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="h-full"
    >
      <line x1="0.5" y1="47" x2="0.500002" y2="-2.18557e-08" stroke="#D9D9D9" />
    </svg>
  );
};
