import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import Image from "next/image";
import { IResearchCard } from "./types";

export default function ResearchCard(researchCard: IResearchCard) {
  const { image, heading, bodyContent, className } = researchCard;

  return (
    <div
      className={cn(
        "flex size-full flex-col gap-8 rounded bg-white px-8 pb-8 pt-[50px] shadow lg:min-w-[19.94rem]",
        className,
      )}
    >
      <Image
        src={image?.filename}
        alt={heading}
        width={200}
        height={250}
        className="max-h-[98px] w-fit object-contain md:max-h-[145px]"
      />
      <Text
        tag="p"
        style="sh6"
        className="border-b border-b-[#F2F2F2] pb-3 text-neutral01-75"
      >
        {heading}
      </Text>
      <Text tag="p" style="b4" mdStyle="b3" className="text-neutral01-75">
        {bodyContent}
      </Text>
    </div>
  );
}
