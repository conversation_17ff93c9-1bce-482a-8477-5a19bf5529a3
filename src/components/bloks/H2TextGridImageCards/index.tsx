"use client";

import { storyblokEditable } from "@storyblok/react/rsc";
import React from "react";
import Container from "../../ui/Container";
import Image from "next/image";
import { cn } from "@/common/utils";
import AnimatedCardSection from "./AnimatedCardSection";
import { IH2TextGridImageCards } from "./types";
import H2TextGridItems from "./H2TextGridItems";

interface Props {
  blok: IH2TextGridImageCards;
  pageType: string;
}

const H2TextGridImageCards = (props: Props) => {
  const { blok } = props;

  const {
    heading,
    quote,
    attribution,
    bodyContent,
    gridItems,
    researchCards,
    rightPanelAnimation,
    images,
  } = blok;

  const isOnlyOneImage = images?.length === 1;
  const isMultipleImages = images?.length > 1;

  const showAnimationCards =
    (rightPanelAnimation === "research-cards" && researchCards?.length > 0) ||
    (rightPanelAnimation === "images" && isMultipleImages);

  if (showAnimationCards) {
    return <AnimatedCardSection blok={blok} />;
  }

  return (
    <section {...storyblokEditable(blok)} className={cn("h-auto bg-grays-G6")}>
      <div>
        <Container className="flex flex-col gap-x-8 lg:min-h-screen lg:flex-row 2xl:mx-auto 2xl:max-w-screen-2xl 2xl:gap-x-[6.62rem]">
          <div className="basis-full !py-0 md:basis-3/4 lg:flex lg:basis-1/2 lg:flex-col xl:basis-1/2 2xl:basis-1/2">
            <H2TextGridItems
              quote={quote}
              attribution={attribution}
              heading={heading}
              bodyContent={bodyContent}
              gridItems={gridItems}
            />
          </div>
          <div className="relative basis-full items-start md:basis-1/4 lg:flex lg:basis-1/2 xl:basis-1/2 2xl:basis-1/2">
            {rightPanelAnimation === "images" && isOnlyOneImage && (
              <div className="relative block w-full lg:sticky lg:top-24">
                <Image
                  src={images[0]?.filename ?? ""}
                  alt="Decorative image for content section"
                  width={800}
                  height={1100}
                  className="size-full"
                />
              </div>
            )}
          </div>
        </Container>
      </div>
    </section>
  );
};

export default H2TextGridImageCards;
