import React, { useState, useMemo, useRef } from "react";
import ResearchCard from "./ResearchCard";
import { IStoryblokAssetProps } from "@/common/types";
import Image from "next/image";
import { cn, generateImageAltFromFilename } from "@/common/utils";
import { motion, useScroll, useMotionValueEvent } from "motion/react";
import { IResearchCard } from "./types";

interface Props {
  cards: IResearchCard[];
  type: "research-cards" | "images";
  images?: IStoryblokAssetProps[];
}

const CARD_WIDTH = 320;
const CARD_GAP = 24;
const SLOT_POSITIONS = [0, CARD_WIDTH + CARD_GAP, 2 * (CARD_WIDTH + CARD_GAP)];

function AnimatedCardElements({ cards, type, images }: Props) {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [activeIndex, setActiveIndex] = useState(-1);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"],
  });

  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    if (latest <= 0.15) {
      setActiveIndex(-1);
    } else if (latest > 0.15 && latest <= 0.4) {
      setActiveIndex(0);
    } else if (latest > 0.4 && latest <= 0.55) {
      setActiveIndex(1);
    } else if (latest > 0.55) {
      setActiveIndex(2);
    }
  });

  const items = type === "research-cards" ? cards : images;

  const visibleCards = useMemo(() => {
    const safeItems = items ?? [];
    return safeItems.slice(0, activeIndex + 3);
  }, [items, activeIndex]);

  if ((type === "research-cards" && !cards) || (type === "images" && !images))
    return null;

  const getSlot = (cardIdx: number) => {
    if (cardIdx < activeIndex) return 0;
    if (cardIdx === activeIndex) return 0;
    if (cardIdx === activeIndex + 1) return 1;
    if (cardIdx === activeIndex + 2) return 2;
    return 2;
  };

  const getZIndex = (cardIdx: number) => {
    return cardIdx + 1;
  };

  const getRotation = (slot: number, stackDepth: number) => {
    if (slot !== 0) return 0;
    if (stackDepth === 0) return 0;
    if (stackDepth === 1) return 2;
    if (stackDepth === 2) return 4;
    return 0;
  };

  const getStackDepth = (cardIdx: number) => {
    if (cardIdx > activeIndex) return null;
    return activeIndex - cardIdx;
  };

  return (
    <div className="relative hidden size-full lg:flex" ref={sectionRef}>
      <div
        className={cn(
          "sticky top-[calc(50vh-258px)] flex h-fit w-full flex-row justify-center",
        )}
        style={{ minHeight: "32.25rem" }}
      >
        {visibleCards.map((item, idx) => {
          if (!item) return null;
          const key =
            type === "research-cards"
              ? (item as IResearchCard)._uid
              : (item as IStoryblokAssetProps).id;
          const slot = getSlot(idx);
          const zIndex = getZIndex(idx);
          const stackDepth = getStackDepth(idx);
          const rotation = getRotation(slot, stackDepth ?? 0);
          return (
            <motion.div
              key={key}
              className="absolute"
              initial={{ x: SLOT_POSITIONS[2], opacity: 1, zIndex }}
              animate={{
                x: SLOT_POSITIONS[slot],
                opacity: 1,
                zIndex,
              }}
              transition={{ duration: 1.4, ease: "easeOut" }}
              style={{ width: CARD_WIDTH, left: 0 }}
            >
              <motion.div
                initial={{ scale: 0.98, rotate: 0 }}
                animate={{ scale: 1, rotate: rotation }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              >
                {type === "research-cards" && (
                  <ResearchCard
                    className="min-h-[32.25rem] max-w-[19.9375rem]"
                    {...(item as IResearchCard)}
                  />
                )}
                {type === "images" && (
                  <div className="relative h-[32.25rem] w-[19.9375rem]">
                    <Image
                      src={(item as IStoryblokAssetProps).filename}
                      alt={generateImageAltFromFilename(
                        (item as IStoryblokAssetProps)?.filename,
                      )}
                      fill={true}
                      className="size-full object-cover"
                    />
                  </div>
                )}
              </motion.div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}

export default AnimatedCardElements;
