"use client";

import { storyblokEditable } from "@storyblok/react/rsc";
import Container from "../../ui/Container";
import Image from "next/image";
import { cn } from "@/common/utils";
import MobileResearchCards from "./MobileResearchCards";
import AnimatedCardElements from "./AnimatedCardElements";
import { IH2TextGridImageCards } from "./types";
import H2TextGridItems from "./H2TextGridItems";

type Props = {
  blok: IH2TextGridImageCards;
};

export default function AnimatedCardSection({ blok }: Props) {
  const {
    heading,
    quote,
    attribution,
    bodyContent,
    gridItems,
    researchCards,
    rightPanelAnimation,
    images,
  } = blok;

  const isOnlyOneImage = images?.length === 1;
  const isMultipleImages = images?.length > 1;

  const multipleImages = images?.slice(0, 3) ?? [];

  const showAnimationCards =
    (rightPanelAnimation === "research-cards" && researchCards?.length > 0) ||
    (rightPanelAnimation === "images" && isMultipleImages);

  return (
    <section {...storyblokEditable(blok)} className={cn("h-auto bg-grays-G6 overflow-hidden")}>
      <Container className="lg:!pr-0">
        <div
          className={cn(
            "relative flex flex-col overflow-clip lg:flex-row lg:gap-[6.62rem] 2xl:gap-[7.81rem] max:overflow-visible lg:items-center",
          )}
        >
          <div className="basis-full !py-0 md:basis-3/4 lg:flex lg:basis-1/2 lg:flex-col xl:basis-1/2 2xl:basis-1/2">
            <H2TextGridItems
              quote={quote}
              attribution={attribution}
              heading={heading}
              bodyContent={bodyContent}
              gridItems={gridItems}
            />
            <div>
              {rightPanelAnimation === "images" && isOnlyOneImage && (
                <div className="relative block w-[135%] md:w-full lg:absolute lg:hidden lg:w-[45rem] 2xl:w-full">
                  <Image
                    src={images[0]?.filename ?? ""}
                    alt="Decorative image for content section"
                    width={800}
                    height={1100}
                    className="size-full"
                  />
                </div>
              )}
            </div>
          </div>
          <div className="relative basis-full items-center md:basis-1/4 lg:flex lg:basis-1/2 lg:pb-1 lg:pt-4 xl:basis-1/2 2xl:basis-1/2">
            {showAnimationCards && (
              <AnimatedCardElements
                cards={researchCards}
                type={rightPanelAnimation}
                images={multipleImages}
              />
            )}
          </div>
        </div>
      </Container>
      {rightPanelAnimation === "research-cards" &&
        researchCards?.length > 0 && (
          <MobileResearchCards cards={researchCards} />
        )}
    </section>
  );
}
