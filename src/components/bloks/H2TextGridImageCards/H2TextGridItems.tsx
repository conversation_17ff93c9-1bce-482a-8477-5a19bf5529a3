"use client";

import Text from "../../ui/Text";
import { CloseQuotation } from "../../ui/OpenCloseQuotation";
import { OpenQuotation } from "../../ui/OpenCloseQuotation";
import { cn } from "@/common/utils";
import type { ClassValue } from "clsx";
import GridItems from "./GridItems";
import { IH2GridItems } from "./types";
import { highlightText } from "@/common/renderHighlight";
import MarkdownSection from "../MarkdownSection";

type Props = {
  quote: string;
  attribution: string;
  heading: string;
  bodyContent: string;
  gridItems: IH2GridItems[];
};

export const HeadingComponent = ({
  as: Component,
  className,
  ...props
}: {
  as: React.ElementType;
  className?: ClassValue;
}) => (
  <Component
    className={cn("font-display-sans text-h4", className)}
    {...props}
  />
);

export const BodyComponent = ({
  as: Component,
  className,
  ...props
}: {
  as: React.ElementType;
  className?: ClassValue;
}) => (
  <Component
    className={cn("font-body-p text-b2 md:text-b1", className)}
    {...props}
  />
);

function H2TextGridItems({
  quote,
  attribution,
  heading,
  bodyContent,
  gridItems,
}: Props) {
  return (
    <>
      <Text tag="h2" style="mh1.5" mdStyle="h2" className="text-primary01-75 mb-0 md:mb-10">
        {heading}
      </Text>
      {quote && (
        <>
          <div className="pt-10 md:pt-10 mb-10">
            <Text
              tag="p"
              style="q4"
              mdStyle="q1"
              className="whitespace-pre-line pb-8 text-grays-G1 md:pb-6"
            >
              <OpenQuotation />
              {highlightText(quote, "italic text-primary01-50")}
              <CloseQuotation />
            </Text>
            <Text
              tag="p"
              style="b3"
              className="whitespace-pre-line text-neutral01-75 lg:whitespace-normal"
            >
              {attribution}
            </Text>
          </div>
          <hr className="border-grays-G5" />
        </>
      )}
      <div className="mt-10">
        <MarkdownSection blok={{ bodyContent }} components={components} />
      </div>
      <div className="grid grid-cols-1 gap-12 pb-20 pt-10 md:grid-cols-2 md:gap-[4.56rem] md:pt-20 lg:pb-0">
        <GridItems gridItems={gridItems} />
      </div>
    </>
  );
}

export const components: Record<string, React.ComponentType<any>> = {
  h1: (props) => (
    <h1 className="mb-6 font-display-sans text-h4 text-black" {...props} />
  ),
  h2: (props) => (
    <h2 className="mb-6 font-display-sans text-h4 text-black" {...props} />
  ),
  h3: (props) => (
    <h3 className="mb-6 font-display-sans text-h4 text-black" {...props} />
  ),
  h4: (props) => (
    <h4 className="mb-6 font-display-sans text-h4 text-black" {...props} />
  ),
  h5: (props) => (
    <h5 className="mb-6 font-display-sans text-h4 text-black" {...props} />
  ),
  p: (props) => (
    <p
      className="my-3 font-body-p text-b2 text-neutral01-75 md:text-b1"
      {...props}
    />
  ),
  ul: ({ children, ...props }) => {
    return (
      <ul
        className="mb-7 mt-4 pl-8 font-body-p text-b2 text-neutral01-75 md:text-b1 [&>li]:relative [&>li]:before:absolute [&>li]:before:-left-4 [&>li]:before:text-primary01-75 [&>li]:before:content-['-']"
        {...props}
      >
        {children}
      </ul>
    );
  },
  ol: ({ children, ...props }) => (
    <ol
      className="list-decimal pl-8 font-body-p text-b2 text-neutral01-75 marker:text-primary01-75 md:text-b1"
      {...props}
    >
      {children}
    </ol>
  ),
  li: (props) => <li {...props} />,
  hr: (props) => <hr className="my-10 border-grays-G5" {...props} />,
};

export default H2TextGridItems;
