import SlideCarousel from "@/components/ui/SlideCarousel";
import ResearchCard from "./ResearchCard";
import { IResearchCard } from "./types";

export default function MobileResearchCards({
  cards,
}: {
  cards: IResearchCard[];
}) {
  if (!cards) return null;

  return (
    <div className="block lg:hidden">
      <SlideCarousel transparent={true} wrapperClassName="!pt-0">
        {cards?.map((card) => <ResearchCard key={card._uid} {...card} />)}
      </SlideCarousel>
    </div>
  );
}
