import { IStoryblokAssetProps } from "@/common/types";
import { SbBlokData } from "@storyblok/react/rsc";
import { ClassValue } from "clsx";

export interface IH2GridItems {
  _uid: string;
  bodyContent?: string;
}

export interface IH2TextGridImageCards extends SbBlokData {
  heading: string;
  quote: string;
  _uid: string;
  sideGif?: IStoryblokAssetProps;
  component: string;
  attribution: string;
  bodyContent: string;
  gridItems: IH2GridItems[];
  researchCards: IResearchCard[];
  rightPanelAnimation: "research-cards" | "images";
  images: IStoryblokAssetProps[];
}

export interface IResearchCard {
  _uid: string;
  image: IStoryblokAssetProps;
  heading: string;
  bodyContent: string;
  className?: ClassValue;
}
