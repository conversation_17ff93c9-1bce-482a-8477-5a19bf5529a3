import Text from "@/components/ui/Text";
import { cn } from "@/common/utils";

interface Props {
    children: React.ReactNode;
    className?: string;
    type?: string;
}

const TableHeaderCell = ({ children, className, type = "" }: Props) => {
    const isTimesBetter = type === "TimesBetter";

    const textAlignClass = isTimesBetter ? "text-center" : "text-left";
    const textClass = cn(
        "text-primary01-75  break-words w-full lg:w-[130px] px-[5px]",
        isTimesBetter && "m-auto"
    );

    return (
        <th className={cn("align-middle py-3 md:py-[23px]", textAlignClass, className)}>
            <Text tag="p" style="t3" mdStyle="h5" className={textClass}>
                {children}
            </Text>
        </th>
    );
};

export default TableHeaderCell;