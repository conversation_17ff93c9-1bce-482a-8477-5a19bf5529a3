import Container from "@/components/ui/Container";
import TableLayout from "./TableLayout";
import { getCrimsonDataset } from "@/common/StudentResults/getCrimsonDataset";
import Header from "../GridSection/Header";

interface Props {
    blok: {
        collegeColumnTitle?: string;
        generalColumnTitle: string;
        acceptanceRatesChartDataset: string;
        crimsonAdmitRateColumnTitle?: string;
        crimsonOffersCountColumnTitle?: string;
        acceptanceRateImprovementColumnTitle?: string;
        headerItem: any[];
        language?: string;
    };
}

const AllTimeAdmissionsResultsTable = async ({ blok }: Props) => {
    const {
        collegeColumnTitle = "College",
        generalColumnTitle = "Crimson All Time Offers",
        crimsonAdmitRateColumnTitle = "General Admit Rate",
        crimsonOffersCountColumnTitle = "US Crimson Admit Rate",
        acceptanceRateImprovementColumnTitle = "Times Better",
        acceptanceRatesChartDataset,
        language = "en",
    } = blok;

    const { dataset, translationMap } = await getCrimsonDataset(acceptanceRatesChartDataset, language);

    if (!dataset || dataset.length === 0) return null;

    return (
        <div className="bg-grays-G6">
            <Container>
                <Header
                    title={blok.headerItem[0]}
                />
                <TableLayout
                    dataset={dataset}
                    translationMap={translationMap}
                    headers={{
                        college: collegeColumnTitle,
                        general: generalColumnTitle,
                        crimsonRate: crimsonAdmitRateColumnTitle,
                        offers: crimsonOffersCountColumnTitle,
                        better: acceptanceRateImprovementColumnTitle,
                    }}
                />
            </Container>
        </div>
    );
};

export default AllTimeAdmissionsResultsTable;