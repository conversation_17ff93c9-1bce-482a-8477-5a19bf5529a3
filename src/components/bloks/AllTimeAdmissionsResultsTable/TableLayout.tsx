import TableHeaderCell from "./TableHeaderCell";
import TableBodyCell from "./TableBodyRow";
import { CrimsonStudentData } from "./type";

interface Props {
    dataset: CrimsonStudentData[];
    translationMap: Map<string, string>;
    headers: {
        college: string;
        general: string;
        crimsonRate: string;
        offers: string;
        better: string;
    };
}

const TableLayout = ({ dataset, translationMap, headers }: Props) => (
    <div className="relative">
        <div className="overflow-[overlay] max-w-full overflow-auto max-h-[386px] md:max-h-[555px] scrollbar-thin scrollbar-thumb-[rgba(192,192,192,0.5)] scrollbar-track-transparent scrollbar-thumb-rounded-md">
            <table className="w-full border-collapse text-left text-sm text-black">
                <thead className="sticky top-0 z-[2] bg-grays-G6 shadow-[inset_0_-1px_0_0_theme(colors.primary01.75)]">
                    <tr>
                        <TableHeaderCell>{headers.college}</TableHeaderCell>
                        <TableHeaderCell>{headers.offers}</TableHeaderCell>
                        <TableHeaderCell>{headers.general}</TableHeaderCell>
                        <TableHeaderCell>{headers.crimsonRate}</TableHeaderCell>
                        <TableHeaderCell type="TimesBetter" className="bg-primary01-25 shadow-[inset_0_-1px_0_0_theme(colors.primary01.75)]">
                            {headers.better}
                        </TableHeaderCell>
                    </tr>
                </thead>
                <tbody>
                    {dataset.map((entry, index) => (
                        <tr key={index} className="border-b border-grays-G5 last:border-0">
                            <TableBodyCell type="College">{translationMap.get(entry.name) ?? entry.name}</TableBodyCell>
                            <TableBodyCell>{entry?.value ?? "--"}</TableBodyCell>
                            <TableBodyCell>
                                {entry?.general_acceptance_rate ? `${entry.general_acceptance_rate}%` : "--"}
                            </TableBodyCell>
                            <TableBodyCell>
                                {entry?.crimson_acceptance_rate_UsStudentOnly ? `${entry.crimson_acceptance_rate_UsStudentOnly}%` : "--"}
                            </TableBodyCell>
                            <TableBodyCell type="TimesBetter" className="font-bold text-red-600 bg-primary01-25">
                                {entry?.crimson_times_better ? `${entry.crimson_times_better}x` : "--"}
                            </TableBodyCell>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
        <div className="hidden md:block pointer-events-none absolute bottom-0 left-0 h-[46px] w-full bg-[linear-gradient(180deg,_rgba(250,249,246,0.00)_0%,_#FAF9F6_100%)]" />
    </div>
);

export default TableLayout;