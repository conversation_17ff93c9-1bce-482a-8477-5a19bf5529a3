import Text, { TStyle } from "@/components/ui/Text";
import { cn } from "@/common/utils";

interface Props {
    children: React.ReactNode;
    className?: string;
    type?: string;
}

const TableBodyCell = ({ children, className, type = "" }: Props) => {
    let textStyle: TStyle = "b2";
    let textMdStyle: TStyle = "b1";
    let textClass = "text-neutral01-75";
    let textAlignClass = "text-left";

    switch (type) {
        case "TimesBetter":
            textStyle = "h5";
            textMdStyle = "h3";
            textClass = "text-primary01-50";
            textAlignClass = "text-center";
            break;
        case "College":
            textStyle = "b3"
            textMdStyle = "sh5";
            textClass = "text-neutral01-75";
            break;
        default:
            break;
    }
    return (
        <td className={cn("align-middle py-4 md:py-[1.25rem]", textAlignClass, className)}>
            <Text tag="p" style={textStyle} mdStyle={textMdStyle} className={textClass}>
                {children}
            </Text>
        </td>
    );
};

export default TableBodyCell;