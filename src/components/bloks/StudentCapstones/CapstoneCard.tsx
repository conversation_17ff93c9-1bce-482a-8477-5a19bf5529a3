import React from "react";
import { ICapstoneCard } from "./types";
import Image from "next/image";
import Text from "@/components/ui/Text";
import { generateImageAltFromFilename } from "@/common/utils";

interface ICapstoneCardProps extends ICapstoneCard {
  nameLabel: string;
  projectLabel: string;
  locationLabel: string;
  admittedToLabel: string;
  cardTitleLabel: string;
  description: string;
}

export default function CapstoneCard(props: ICapstoneCardProps) {
  const {
    image,
    nameLabel,
    name,
    projectLabel,
    project,
    locationLabel,
    location,
    admittedToLabel,
    admittedTo,
    cardTitleLabel,
    description,
  } = props;
  return (
    <div className="flex h-full flex-col bg-white p-lg shadow-[0px_0px_18.352px_0px_rgba(86,_65,_46,_0.10)] lg:w-[21.5625rem] lg:p-[0.69rem] 2xl:w-[25.64525rem] 2xl:pb-[2.18rem]">
      <Image
        src={image.filename}
        width={1024}
        height={1024}
        className="h-[8.4375rem] w-full object-cover lg:h-[17.20475rem] 2xl:h-[20.46169rem]"
        alt={generateImageAltFromFilename(image.filename)}
      />
      <div className="flex flex-1 flex-col gap-4 lg:px-[1.2rem]">
        <Text
          tag="p"
          style="h5"
          className="border-b border-b-[#F2F2F2] py-4 lg:pt-[1.84rem] 2xl:pt-[2.18rem]"
        >
          {cardTitleLabel}
        </Text>
        <StudentDetails label={nameLabel} text={name} />
        <StudentDetails label={locationLabel} text={location} />
        <StudentDetails label={projectLabel} text={project} />
        <StudentDetails label={admittedToLabel} text={admittedTo} />
        <Text tag="p" style="b2" className="text-neutral01-75 lg:hidden">
          {description}
        </Text>
      </div>
    </div>
  );
}

const StudentDetails = ({ text, label }: { text: string; label: string }) => (
  <div className="flex items-center gap-4">
    <Text
      tag="label"
      style="b4"
      lgStyle="b3"
      className="block min-w-[74px] max-w-[120px] text-neutral01-100"
    >
      {label}
    </Text>
    <Text
      tag="p"
      style="hw5"
      mdStyle="hw4"
      className="w-full border-b border-dotted border-b-neutral01-50 text-grays-G2"
    >
      {text}
    </Text>
  </div>
);
