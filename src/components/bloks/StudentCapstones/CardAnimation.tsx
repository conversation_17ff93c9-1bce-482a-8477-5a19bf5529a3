import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import { AnimatePresence, Easing, motion } from "motion/react";

const cardAtFrontVariants = {
  enter: {
    opacity: 0,
    x: 0,
    scale: 1,
  },
  center: {
    opacity: 1,
    x: 0,
    y: 0,
    rotateZ: 0,
    scale: 1,
    transition: {
      delay: 1,
    },
  },
  exit: {
    x: 40,
    z: [0, -20],
    left: [0, "-100%"],
    rotateZ: [0, -1],
    scale: [1, 0.95],
    transition: {
      duration: 0.5,
      ease: [0.42, 0, 0.58, 1] as Easing,
    },
  },
};

const magicAnimate = {
  duraton: 0.6,
  ease: [0.42, 0, 0.58, 1] as Easing,
};

const insertToBackVariant = {
  enter: {
    opacity: 0,
    z: -20,
    left: "-100%",
    rotateZ: 1,
    scale: 0.9,
  },
  center: {
    opacity: [0, 1, 1, 1, 1, 0],
    z: [-20, -40],
    left: ["-100%", 40],
    top: 30,
    rotateZ: [-1, 3],
    scale: [0.95, 0.925],
    transition: {
      ...magicAnimate,
      delay: 0.45,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      delay: 0,
    },
  },
};

const normalAnimations = {
  initial: {
    opacity: 0,
    x: 20,
  },
  center: {
    opacity: 1,
    x: 0,
  },
  exit: {
    opacity: 0,
  },
};

export default function CardAnimation({
  children,
  currentIndex,
  previousIndex,
}: {
  currentIndex: number;
  previousIndex?: number;
  children: React.ReactNode[];
}) {
  const childrenArray = React.Children.toArray(children);
  const wrapper = useRef<HTMLDivElement>(null);
  const [allIndex] = useState<number[]>(childrenArray.map((_, i) => i));
  const [cards, setCards] = useState(allIndex.slice(0, 3));

  useEffect(() => {
    // set cards based on position in overall children
    setCards((cards) => cards.slice(1)); // eveything except first
    const onChange = () =>
      setCards(() => {
        const newArray: number[] = [];
        
        for (
          let i = currentIndex;
          i < currentIndex + Math.min(allIndex.length, 3);
          i++
        ) {
          newArray.push(allIndex[i % allIndex.length] as unknown as number);
        }

        return newArray;
      });
    const timeout = setTimeout(onChange, 600);

    return () => {
      clearTimeout(timeout);
      onChange();
    };
  }, [currentIndex, allIndex]);

  useLayoutEffect(() => {
    // get height
    const parent = wrapper.current;
    if (!parent) return;

    const children = Array.from(parent.children);
    const tallestHeight = Math.max(
      ...children.map((child) => child.getBoundingClientRect().height),
    );

    parent.style.minHeight = `${tallestHeight}px`;
    parent.style.opacity = `1`;
  }, []);

  return (
    <>
      <div
        ref={wrapper}
        className="stack-container sticky left-0 top-3 opacity-0 lg:w-96 2xl:w-[28rem]"
      >
        <AnimatePresence>
          <motion.div
            key={currentIndex}
            variants={cardAtFrontVariants}
            initial="enter"
            animate="center"
            exit="exit"
            layout
            style={{
              position: "absolute",
              scale: 1,
              left: 0,
              top: 0,
              background: "#fff",
              borderRadius: 12,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 5,
              cursor: "pointer",
            }}
          >
            {childrenArray[cards[0] ?? 0]}
          </motion.div>
          {previousIndex !== undefined && (
            <motion.div
              key={`__${previousIndex}`}
              variants={insertToBackVariant}
              initial="enter"
              animate="center"
              exit="exit"
              layout
              style={{
                position: "absolute",
                left: 0,
                background: "#fff",
                borderRadius: 12,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                zIndex: 0,
                cursor: "pointer",
                backgroundColor: "red",
              }}
            >
              {childrenArray[previousIndex]}
            </motion.div>
          )}
        </AnimatePresence>
        {cards.map((card, index) => {
          const isTop = index === 0;
          return (
            <motion.div
              key={card}
              variants={normalAnimations}
              exit="exit"
              layout
              style={{
                position: "absolute",
                borderRadius: 12,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                zIndex: cards.length - index,
                cursor: isTop ? "pointer" : "default",
                scale: 1 - index * 0.025,
                left: `${25 * index}px`,
              }}
              initial={{
                left: 0,
                rotate: 0,
              }}
              animate={{
                left: index * 25,
                rotate: `${index * 4}deg`,
              }}
              transition={{
                duration: 0.2,
                ease: [0, 0, 0.58, 1],
              }}
            >
              {childrenArray[card]}
            </motion.div>
          );
        })}
      </div>
    </>
  );
}
