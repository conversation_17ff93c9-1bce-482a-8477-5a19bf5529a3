import { IStoryblokAssetProps } from "@/common/types";

export interface ICapstoneCard {
  image: IStoryblokAssetProps;
  name: string;
  admittedTo: string;
  location: string;
  project: string;
  description: string;
}

export interface IStudentSuccessCapstonesProps {
  blok: {
    heading: string;
    bodyContent: string;
    capstoneCards: ICapstoneCard[];
    cardTitleLabel: string;
    nameLabel: string;
    projectLabel: string;
    locationLabel: string;
    admittedToLabel: string;
  };
}

export type ICaptstoneGridSectionProps = IStudentSuccessCapstonesProps["blok"];
