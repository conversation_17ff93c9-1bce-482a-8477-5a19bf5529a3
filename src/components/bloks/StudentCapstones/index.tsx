import React from "react";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import SlideCarousel from "@/components/ui/SlideCarousel";
import { IStudentSuccessCapstonesProps } from "./types";
import CapstoneCard from "./CapstoneCard";
import { getDotBackgroundStyle } from "@/common/getDotBackgroundStyle";
import AutoChangingList from "./AutoChangingListComponent";

export default function StudentSuccessCapstones(
  props: IStudentSuccessCapstonesProps,
) {
  const {
    heading,
    bodyContent,
    capstoneCards,
    nameLabel,
    projectLabel,
    admittedToLabel,
    cardTitleLabel,
    locationLabel,
  } = props.blok;

  return (
    <section className="w-full bg-grays-G6">
      {/* eslint-disable-next-line tailwindcss/enforces-shorthand */}
      <Container className="px-0 md:px-0 lg:px-[30px]">
        <div className="relative z-0 w-full pt-[2.87rem] md:pt-[3.88rem] lg:pt-[2.41rem] xl:pt-16">
          <div className="mx-auto grid gap-6 px-[25px] md:px-[30px] lg:max-w-[50.0625rem] xl:max-w-[44.5rem] 2xl:max-w-[53.375rem]">
            <Text tag="h2" style="h4" className="lg:text-center">
              {heading}
            </Text>
            <Text
              tag="p"
              style="b2"
              mdStyle="b1"
              className="text-neutral01-75 lg:text-center"
            >
              {bodyContent}
            </Text>
          </div>

          <div className="hidden pt-4xl md:pt-20 lg:block">
            <AutoChangingList {...props.blok} capstoneCards={capstoneCards} />
          </div>
          <div className="mt-[3.94rem] md:mt-[2.94rem] lg:hidden">
            <SlideCarousel transparent wrapperClassName="!py-0">
              {capstoneCards.map((student, i) => (
                <CapstoneCard
                  key={i}
                  {...student}
                  nameLabel={nameLabel}
                  projectLabel={projectLabel}
                  locationLabel={locationLabel}
                  admittedToLabel={admittedToLabel}
                  cardTitleLabel={cardTitleLabel}
                />
              ))}
            </SlideCarousel>
          </div>
          <Background />
        </div>
      </Container>
    </section>
  );
}

function Background() {
  return (
    <div
      className="absolute inset-x-8 bottom-24 top-0 -z-10 md:bottom-28 lg:inset-x-0 lg:-bottom-12"
      style={getDotBackgroundStyle()}
    />
  );
}
