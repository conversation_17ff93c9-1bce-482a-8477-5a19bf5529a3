"use client";

import React, { useEffect, useRef, useState } from "react";
import { ICaptstoneGridSectionProps } from "./types";
import SlidingLine from "./SlidingLine";
import Text from "@/components/ui/Text";
import CardAnimation from "./CardAnimation";
import CapstoneCard from "./CapstoneCard";
import { cn } from "@/common/utils";
import { useInView } from "motion/react";

const NORMAL_TIMEOUT = 5000;
const SELECTION_TIMEOUT = 11000;

export default function AutoChangingList(props: ICaptstoneGridSectionProps) {
  const elements = useRef<HTMLDivElement[]>([]);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const {
    capstoneCards,
    nameLabel,
    projectLabel,
    admittedToLabel,
    cardTitleLabel,
    locationLabel,
  } = props;

  const [currentCard, setCurrentCard] = useState(0);
  const [previousCard, setPreviousCard] = useState<number>();
  const [isSelectionMode, setIsSelectionMode] = useState<boolean>(false);
  const isInView = useInView(wrapperRef);

  useEffect(() => {
    if (isInView) {
      const timeout = setTimeout(() => {
        if (isSelectionMode) {
          setIsSelectionMode(false);
        } else if (currentCard < capstoneCards.length - 1) {
          setCurrentCard(currentCard + 1);
          setPreviousCard(currentCard);
        } else {
          setCurrentCard(0);
          setPreviousCard(currentCard);
        }
      }, isSelectionMode ? SELECTION_TIMEOUT : NORMAL_TIMEOUT);

      return () => clearTimeout(timeout);
    }
  }, [currentCard, isSelectionMode, capstoneCards, isInView]);

  return (
    <div className="grid grid-cols-2" ref={wrapperRef}>
      <div className="relative flex items-start justify-center">
        <CardAnimation currentIndex={currentCard} previousIndex={previousCard}>
          {capstoneCards.map((student, i) => (
            <CapstoneCard
              key={i}
              {...student}
              nameLabel={nameLabel}
              projectLabel={projectLabel}
              locationLabel={locationLabel}
              admittedToLabel={admittedToLabel}
              cardTitleLabel={cardTitleLabel}
            />
          ))}
        </CardAnimation>
      </div>
      <div>
        {capstoneCards.map((capstone, i) => (
          <div
            ref={(ref: HTMLDivElement) => {
              if (ref) elements.current[i] = ref;
            }}
            key={`${capstone.name}_${i}`}
            className="flex items-stretch gap-[2.62rem]"
            onClick={() => {
              if (i !== currentCard) {
                setCurrentCard(i);
                setPreviousCard(currentCard);
                setIsSelectionMode(true);
              }
            }}
          >
            <SlidingLine
              isCurrent={i === currentCard && isInView}
              isSelectionMode={isSelectionMode}
              timer={isSelectionMode ? SELECTION_TIMEOUT : NORMAL_TIMEOUT}
            />
            <div className="cursor-pointer py-4">
              <Text
                tag="p"
                style="sh4"
                className={cn(
                  "mb-3 transition-colors duration-300 ease-in-out",
                  currentCard === i ? "text-grays-G1" : "text-grays-G5",
                )}
              >
                {capstone.name}
              </Text>
              <Text
                tag="p"
                style="b1"
                className={cn(
                  "transition-colors duration-300 ease-in-out",
                  currentCard === i ? "text-neutral01-75" : "text-grays-G5",
                )}
              >
                {capstone.description}
              </Text>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}