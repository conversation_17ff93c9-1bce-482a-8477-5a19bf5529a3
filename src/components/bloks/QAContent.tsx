import ReactMarkdown from "react-markdown";
import React from "react";

interface FaqItem {
  _uid: string;
  icon: {
    filename: string;
    alt?: string;
  };
  title: string;
  content: string;
}

interface Props {
  blok: {
    header: string;
    content: string;
    faqItems: FaqItem[];
  };
}

const QAContent = ({ blok }: Props) => {
  const { header, content, faqItems } = blok;

  return (
    <div className="mt-12">
      <h3 className="mb-3 font-display-serif text-4xl font-normal leading-10">
        {header}
      </h3>

      <ReactMarkdown className="font-body-single text-base font-normal">
        {content}
      </ReactMarkdown>

      <div className="mt-9">
        {faqItems?.map((item, index) => (
          <div key={item._uid} className="mb-9">
            <div className="flex items-center gap-2">
              <div className="flex-none mb-1 mr-1 flex size-6 items-center justify-center rounded border bg-neutral01-0 font-body-single text-sm leading-none text-primary01-75">
                {index + 1}
              </div>
              <h4 className="m-0 font-display-serif text-3xl font-normal text-black flex-1">
                {item.title}
              </h4>
            </div>
            <ReactMarkdown className="font-body-single text-base font-normal">
              {item.content}
            </ReactMarkdown>
          </div>
        ))}
      </div>
    </div>
  );
};

export default QAContent;
