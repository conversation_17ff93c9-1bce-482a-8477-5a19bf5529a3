import { IStoryblokAssetProps, IStoryblokLinkProps } from "@/common/types";
import { cn } from "@/common/utils";
import Button from "@/components/ui/Button";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import Image from "next/image";
import React from "react";

export interface IBannerProps {
  blok: {
    heading: string;
    bodyContent?: string;
    gradientOverlay: string;
    backgroundImage: IStoryblokAssetProps;
    useCTA: boolean;
    buttonLabel: string;
    link: IStoryblokLinkProps[];
  };
}

export default function FeaturedBannerAndCTA({ blok }: IBannerProps) {
  const {
    heading,
    bodyContent,
    buttonLabel,
    backgroundImage,
    useCTA,
    gradientOverlay,
  } = blok;

  const link = blok.link?.[0];

  const isRedTheme = gradientOverlay === "red";

  return (
    <Container
      size="full"
      className={cn(
        "relative flex min-h-[500px] !p-0",
        isRedTheme ? "bg-primary01-100" : "bg-neutral01-100",
      )}
    >
      <Container className="md:flex md:items-center">
        <div className="relative z-[3] mt-[400px] flex w-full flex-col gap-y-8 text-white md:mt-0 md:w-1/2">
          <Text
            tag="p"
            style="q3"
            lgStyle="q2"
            xlStyle="q1"
            className="whitespace-pre"
          >
            {heading}
          </Text>
          {bodyContent && (
            <Text tag="p" style="b1" className="whitespace-pre">
              {bodyContent}
            </Text>
          )}
          {useCTA && link && (
            <Button
              theme="secondary"
              colour="white"
              className="bg-transparent"
              link={link.link}
              targetAnchorId={link.targetAnchorId}
            >
              {buttonLabel}
            </Button>
          )}
          {/* Gradient Overlay */}
        </div>
      </Container>
      <Image
        alt="background image"
        src={backgroundImage.filename}
        width={4025}
        height={2060}
        className="absolute right-0 top-0 h-[500px] w-full object-cover object-center md:h-full md:w-2/3 md:[mask-image:linear-gradient(to_right,transparent,black_10%)]"
      />
      <div
        className={cn(
          "absolute inset-x-0 bottom-0 z-[2] size-full md:h-full md:w-full",
          isRedTheme
            ? "bg-[linear-gradient(to_top,#3A0407_calc(100%-500px),rgba(255,255,255,0)_calc(100%-250px))] md:bg-[radial-gradient(circle_at_left,_#3A0407,_#74070E_40%,_transparent_68%)]"
            : "bg-[linear-gradient(to_top,#1D150E_calc(100%-500px),rgba(255,255,255,0)_calc(100%-250px))] md:bg-[radial-gradient(circle_at_left,_#1D150E_40%,_#AE8C6F80_60%,_transparent_78%)]",
        )}
      />
    </Container>
  );
}
