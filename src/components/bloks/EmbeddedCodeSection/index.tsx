"use client";
// This needs to be a client component, because it requires the document object to be available.

import React from "react";
import parse from "html-react-parser";
import { storyblokEditable } from "@storyblok/react/rsc";
import { DOMNode, Element } from "html-react-parser";
import styles from "./EmbeddedCodeSection.module.css";

type Props = {
  blok: {
    source: string;
  };
  sectionIndex: number;
};

const EmbeddedCodeSection = ({ blok }: Props) => {
  const { source } = blok;

  return (
    <section {...storyblokEditable(blok)}>
      <div className={`mx-auto w-full ${styles.container}`}>
        {typeof document !== "undefined" &&
          parse(source, {
            replace: function (domNode: DOMNode) {
              if (domNode instanceof Element && domNode.tagName === "script") {
                const script = document.createElement("script");
                script.src = domNode.attribs.src ?? "";
                document.head.appendChild(script);
              }
            },
          })}
      </div>
    </section>
  );
};

export default EmbeddedCodeSection;
