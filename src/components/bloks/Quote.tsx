import React from "react";
import Comma from "../icons/Comma";

interface Props {
  blok: {
    author: string;
    quoteText: string;
  };
}

const Quote = ({ blok }: Props) => {
  const { author, quoteText } = blok;

  return (
    <div className="mb-6 mt-12 flex w-full">
      <Comma className="mr-2 h-4 w-5 shrink-0" />
      <div className="flex-col">
        <p className="font-display-serif text-3xl font-light italic leading-10">
          {quoteText}
        </p>
        <span className="mt-2 block font-body-single text-sm font-normal leading-none text-grays-G4">
          - {author}
        </span>
      </div>
    </div>
  );
};

export default Quote;
