"use client";

import { IStoryblokLinkProps } from "@/common/types";
import { storyblokEditable } from "@storyblok/react/rsc";
import { useLocalStorage } from "@/common/hooks/useLocalStorageValues";
import { MarketoFeatureTypes } from "@/common/constants";
import GeneralLink from "./GeneralLink";
import Image from "next/image";
import { httpsPath } from "@/common/utils";

type Props = {
  blok: {
    desktopImage: string;
    mobileImage: string;
    isBlogTypePage?: boolean;
    link?: IStoryblokLinkProps[];
    enableDynamicBanners?: boolean;
    dynamicBanner?: {
      link: IStoryblokLinkProps[];
      desktopImage: string;
      mobileImage: string;
    };
  };
  sectionIndex: number;
};

const BlogImageSection = ({ blok }: Props) => {
  const { desktopImage, mobileImage, link = [], enableDynamicBanners } = blok;

  const [, setUserFormAttributes] = useLocalStorage("userFormAttributes", {});

  // if dynamic banners are enabled, do not display the image
  if (enableDynamicBanners) return null;

  const hasLink = link.length > 0;

  const image = {
    desktop: desktopImage,
    mobile: mobileImage,
  };

  const handleClick = () => {
    if (hasLink && typeof window !== "undefined") {
      setUserFormAttributes({
        location: location.pathname,
        featureType: MarketoFeatureTypes.blogDynamicBanner,
      });
    }
  };

  return (
    <div {...storyblokEditable(blok)} className="my-8" onClick={handleClick}>
      {hasLink && link[0] ? (
        <GeneralLink blok={link[0]} className="size-full">
          <Images image={image} />
        </GeneralLink>
      ) : (
        <Images image={image} />
      )}
    </div>
  );
};

const Images = ({ image }: { image: { desktop: string; mobile: string } }) => (
  <>
    {image.desktop && (
      <Image
        src={httpsPath(image.desktop)}
        alt="Desktop Image"
        width={500}
        height={200}
        sizes="100vw"
        className="hidden size-full md:block"
      />
    )}
    {image.mobile && (
      <Image
        src={httpsPath(image.mobile)}
        alt="Mobile Image"
        width={500}
        height={200}
        sizes="100vw"
        className="block size-full md:hidden"
      />
    )}
  </>
);

export default BlogImageSection;
