"use client";

import Image from "next/image";
import { useIntersectionObserver } from "@uidotdev/usehooks";
import { useEffect } from "react";
import { IServiceItem } from ".";
import { ISbDataSourceEntry } from "@/common/types";
import Skeleton from "@/components/ui/Skeleton";

interface ServiceImageCardProps {
  card: IServiceItem;
  index: number;
  callWhichImageIsVisible: (imageIndex: number) => void;
  images: ISbDataSourceEntry[] | null;
  isLoadingImages?: boolean;
}

const ServiceImageCard = ({
  card,
  index,
  callWhichImageIsVisible,
  images,
  isLoadingImages,
}: ServiceImageCardProps) => {
  const [ref, entry] = useIntersectionObserver({
    threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],
  });

  useEffect(() => {
    // Call the parent when this image is most visible
    if (entry?.intersectionRatio && entry.intersectionRatio > 0.5) {
      callWhichImageIsVisible(index);
    }
  }, [entry?.intersectionRatio, index, callWhichImageIsVisible]);

  const selectedImage = images?.find(
    (image) =>
      (image?.dimension_value &&
        image?.dimension_value === card?.imageDesktop) ||
      (image?.value && image?.value === card?.imageDesktop),
  );
  const imageToUse =
    selectedImage?.dimension_value ??
    selectedImage?.value ??
    card?.imageDesktop ??
    "";

  return (
    <div
      ref={!isLoadingImages ? ref : null}
      className="min-h-[24.5rem]"
      id={`card-image-${index}`}
    >
      <div>
        {isLoadingImages && <Skeleton className="h-[24.5rem] w-full" />}
        {!isLoadingImages && imageToUse && (
          <Image
            src={imageToUse}
            alt="Services Section Image"
            width={600}
            height={620}
            className="w-full object-cover"
          />
        )}
      </div>
    </div>
  );
};
export default ServiceImageCard;
