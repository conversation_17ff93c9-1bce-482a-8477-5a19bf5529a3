import Image from "next/image";
import { IServiceItem } from ".";
import Text from "@/components/ui/Text";
import { ISbDataSourceEntry } from "@/common/types";
import Skeleton from "@/components/ui/Skeleton";

const MobileCard = ({
  card,
  images,
  isLoadingImages,
}: {
  card: IServiceItem;
  images: ISbDataSourceEntry[] | null;
  isLoadingImages: boolean;
}) => {
  const selectedImage = images?.find(
    (image) =>
      (image?.dimension_value &&
        image?.dimension_value === card?.imageDesktop) ||
      (image?.value && image?.value === card?.imageDesktop),
  );

  const imageToUse =
    selectedImage?.dimension_value ??
    selectedImage?.value ??
    card?.imageDesktop ??
    "";

  return (
    <div>
      <Text tag="h3" style="h4" className="text-primary01-100">
        {card.heading}
      </Text>
      <Text tag="p" style="mb1" className="pt-lg text-neutral01-75">
        {card.bodyContent}
      </Text>
      <div
        className="max-w-[35.9rem] pt-2xl sm:pt-4xl"
        id={`mobile-card-${card._uid}`}
      >
        {isLoadingImages && <Skeleton className="h-[24.5rem] w-full" />}
        {!isLoadingImages && imageToUse && (
          <Image
            src={imageToUse}
            alt="Services Section Image"
            width={650}
            height={430}
            className="w-full object-cover"
          />
        )}
      </div>
    </div>
  );
};

export default MobileCard;
