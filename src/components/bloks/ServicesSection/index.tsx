"use client";

import {
  ISbDataSourceEntry,
  ISbDataSourceResult,
  IStoryblokLink,
} from "@/common/types";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import MobileCard from "./MobileCard";
import { cn } from "@/common/utils";
import { motion } from "motion/react";
import { useEffect, useState, useCallback } from "react";
import ServiceImageCard from "./ServiceImageCard";
import { IComponentTitleH2Props } from "@/components/bloks/ComponentTitleH2";
import { fetchDataSource } from "@/common/storyblok";
import ComponentTitleHeading from "../ComponentTitleHeading";

export interface IServiceItem {
  bodyContent: string;
  component: string;
  heading: string;
  imageDesktop?: string;
  _uid: string;
}

export interface IServicesSectionProps {
  blok: {
    component: string;
    services: IServiceItem[];
    heading: string;
    preHeading: string;
    buttonLabel: string;
    buttonLink: IStoryblokLink;
    serviceSectionHeader: IComponentTitleH2Props[];
    language?: string;
  }
}

const ServicesSection = ({ blok }: IServicesSectionProps) => {
  const { services, serviceSectionHeader, language = "en" } = blok;
  const [images, setImages] = useState<ISbDataSourceEntry[] | null>(null);

  const [isLoadingImages, setIsLoadingImages] = useState(true);

  const [visibleImageIndex, setVisibleImageIndex] = useState<number | null>(
    null,
  );

  const callWhichImageIsVisible = useCallback((imageIndex: number) => {
    setVisibleImageIndex(imageIndex);
  }, []);

  const serviceSectionTitle = serviceSectionHeader?.[0];

  useEffect(() => {
    const getImagesFromDataSource = async () => {
      const data: ISbDataSourceResult = await fetchDataSource(
        "service-image-hub",
        false,
        language,
      );

      setImages(data?.data?.datasource_entries ?? null);
      setIsLoadingImages(false);
    };

    void getImagesFromDataSource();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <>
      {serviceSectionTitle && (
        <ComponentTitleHeading blok={serviceSectionTitle} />
      )}
      <section className="relative">
        <Container className="!pt-xl">
          <div className="relative hidden grid-cols-2 items-center sm:gap-[60px] md:gap-[30px] lg:grid lg:gap-[140px]">
            <div className="basis-1/2 self-start pt-[110px] h-full pb-[330px] xl:pt-[28%]">
              {/* Text */}
              {services?.map((service, index) => {
                return (
                  <div
                    className={cn(
                      "sticky self-start",
                      "sm:top-[calc(50vh-150px)] md:top-[calc(50vh-160px)] lg:top-[calc(50vh-200px)]",
                    )}
                    key={service._uid}
                    id={`card-text-${index}`}
                  >
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{
                        opacity: visibleImageIndex === index ? 1 : 0,
                      }}
                      transition={{
                        duration: visibleImageIndex === index ? 0.5 : 0,
                        ease: "easeInOut",
                      }}
                      className="t-0 absolute"
                    >
                      <Text
                        tag="h3"
                        style="h3"
                        className={cn("text-primary01-100")}
                      >
                        {service.heading}
                      </Text>
                      <Text
                        tag="p"
                        style="b1"
                        className="pb-3xl pt-xl text-neutral01-75"
                      >
                        {service.bodyContent}
                      </Text>
                    </motion.div>
                  </div>)

              })}
            </div>
            <div className="flex basis-1/2 flex-col gap-80 lg:py-[33px] lg:pr-0 xl:pr-[4.41rem]">
              {services?.map((card, index) => (
                <ServiceImageCard
                  key={card._uid}
                  card={card}
                  index={index}
                  callWhichImageIsVisible={callWhichImageIsVisible}
                  images={images}
                  isLoadingImages={isLoadingImages}
                />
              ))}
            </div>
          </div>

          {/* For Mobile: Single Column Layout without Animations */}
          <div className="flex flex-col gap-[75px] lg:hidden">
            {services?.map((service) => (
              <MobileCard
                key={service._uid}
                card={service}
                images={images}
                isLoadingImages={isLoadingImages}
              />
            ))}
          </div>
        </Container>
      </section>
    </>
  );
};

export default ServicesSection;
