"use client";

import Text from "@/components/ui/Text";
import { StarIcon } from "@heroicons/react/24/solid";
import { useState } from "react";
import { cn } from "@/common/utils";
import Container from "@/components/ui/Container";

interface ScoreCard {
    _uid: string;
    heading: string;
    scoreNumber: string;
}

interface ScoreGroup {
    _uid: string;
    heading: string;
    scores: ScoreCard[];
}

interface Props {
    blok: {
        preHeading: string;
        subHeading: string;
        title: string;
        heading: string;
        bodyContent: string;
        scoreGroups: ScoreGroup[];
    };
}

const CaseStudiesTestScores = ({ blok }: Props) => {
    const { preHeading, heading, subHeading, title, bodyContent, scoreGroups } = blok;
    const [activeTab, setActiveTab] = useState(0);

    return (
        <Container size="caseStudy">
            <div className="text-center mb-[30px] md:mb-[60px] lg:mb-[75px] w-full lg:w-[800px] mx-auto">
                <Text tag="p" style="h5" className="text-primary01-50 mb-5">{preHeading}</Text>
                <Text tag="h2" style="sh3" className="text-black mb-[30px]">{heading}</Text>
                <Text tag="p" style="b2" className="text-neutral01-75">{subHeading}</Text>
            </div>

            <div className="flex flex-col lg:flex-row md:justify-between md:items-center">
                <div className="w-full lg:w-[36%] xl:w-[41%] 2xl:w-[44%] mb-10 lg:mb-0">
                    <Text tag="h3" style="sh5" className="text-black flex items-center mb-3">
                        <StarIcon className="size-4 text-primary01-50 mr-[6px]" />
                        {title}
                    </Text>
                    <Text tag="p" style="b2" className="text-neutral01-75">
                        {bodyContent}
                    </Text>
                </div>

                <div className="w-full md:w-[484px]">
                    <div className="flex">
                        {scoreGroups.map((group, index) => (
                            <button
                                key={group._uid}
                                onClick={() => setActiveTab(index)}
                                className={cn(
                                    "text-left pr-2 flex-1 pb-2 border-b transition",
                                    index === activeTab
                                        ? "border-primary01-75 text-primary01-75"
                                        : "border-grays-G5 text-grays-G5"
                                )}
                            >
                                <Text
                                    tag="span"
                                    style="t2"
                                    mdStyle="h5"
                                    className="!font-display-sans line-clamp-2"
                                >
                                    {group.heading}
                                </Text>
                            </button>
                        ))}
                    </div>

                    <div className="overflow-hidden w-full">
                        <div
                            className="flex transition-transform duration-500 ease-in-out"
                            style={{ transform: `translateX(-${activeTab * 100}%)` }}
                        >
                            {scoreGroups.map((group) => (
                                <div key={group._uid} className="w-full shrink-0 pt-4">
                                    <div className="grid grid-cols-3 gap-3 md:gap-4">
                                        {group.scores.map((score) => (
                                            <div
                                                key={score._uid}
                                                className="aspect-[1] w-full px-[10px] md:px-3.5 pt-3 md:pt-6 rounded flex flex-col items-start justify-center text-white"
                                                style={{
                                                    background: "linear-gradient(180deg, #3A0407 0%, #74070E 18.75%, #F5170D 100%)",
                                                    backdropFilter: "blur(1.4px)",
                                                }}
                                            >
                                                <div className="w-full border-t border-white h-[97%]">
                                                    <Text
                                                        tag="p"
                                                        style="h3"
                                                        mdStyle="h1"
                                                        className="w-full text-white pt-1 md:pt-2"
                                                    >
                                                        {score.scoreNumber}
                                                    </Text>
                                                    <Text
                                                        tag="p"
                                                        style="b4"
                                                        mdStyle="b3"
                                                        className="w-full text-white line-clamp-2 !text-[10px] md:!text-[0.875rem] mt-[2px] md:mt-[7px]"
                                                    >
                                                        {score.heading}
                                                    </Text>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </Container>
    );
};

export default CaseStudiesTestScores;