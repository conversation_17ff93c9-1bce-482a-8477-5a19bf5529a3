import React from "react";
import { storyblokEditable } from "@storyblok/react/rsc";
import Text from "@/components/ui/Text";

interface CaseStudyHonorProps {
  blok: {
    _uid: string;
    component: string;
    preHeading: string;
    heading: string;
  };
  yearTextColor?: string;
}

const CaseStudyHonor: React.FC<CaseStudyHonorProps> = ({ blok, yearTextColor = "text-white" }) => {
  const { preHeading, heading } = blok;

  return (
    <div {...storyblokEditable(blok)} className="flex flex-col gap-[0.4375rem] w-full">
      <Text tag="span" style="t3" className={yearTextColor}>
        {preHeading}
      </Text>

      <Text tag="h3" style="sh6" className="text-white">
        {heading}
      </Text>
    </div>
  );
};

export default CaseStudyHonor;