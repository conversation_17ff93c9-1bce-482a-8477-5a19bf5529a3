import React from "react";
import { storyblokEditable } from "@storyblok/react/rsc";
import Container from "@/components/ui/Container";
import { cn } from "@/common/utils";
import { StarIcon } from "@heroicons/react/24/solid";
import { TrophyIcon } from "@heroicons/react/24/outline";
import Text from "@/components/ui/Text";
import CaseStudyHonor from "./CaseStudyHonor";

interface HonorItem {
  _uid: string;
  component: string;
  preHeading: string;
  heading: string;
}

interface CaseStudyHighlightsProps {
  blok: {
    _uid: string;
    component: string;
    heading: string;
    bodyContent: string;
    caseStudyHonor: HonorItem[];
  };
}

const honorColors = [
  "bg-gradient-to-r from-neutral01-50 to-neutral01-25",
  "bg-gradient-to-r from-grey-700 to-grays-G5",
  "bg-gradient-to-r from-primary01-75 to-primary01-50",
];

const yearTextColors = [
  "text-white",
  "text-neutral01-0",
  "text-white",
];

const CaseStudyHighlights: React.FC<CaseStudyHighlightsProps> = ({ blok }) => {
  const { heading, bodyContent, caseStudyHonor } = blok;
  const topHonors = caseStudyHonor?.slice(0, 3) || [];

  return (
    <section {...storyblokEditable(blok)} className="bg-white py-[4.6875rem]">
      <Container>
        <div className="flex flex-col gap-[3.75rem] lg:flex-row lg:gap-[4.6875rem] lg:items-stretch xl:gap-[6.75rem] 2xl:gap-[8.4375rem]">
          <div className="flex flex-col gap-[1.125rem] lg:w-1/3 lg:justify-center xl:w-2/5">
            <div className="flex items-start gap-[0.375rem]">
              <div className="w-[0.875rem] h-[1.8125rem] flex items-center justify-start">
                <StarIcon className="w-[0.875rem] h-[0.875rem] text-primary01-50" />
              </div>
              <Text tag="h2" style="sh6" mdStyle="sh5" className="text-black">
                {heading}
              </Text>
            </div>
            <Text tag="p" style="b2" className="text-neutral01-75">
              {bodyContent}
            </Text>
          </div>

          <div className="flex flex-col gap-[0.9375rem] lg:w-2/3 xl:w-3/5">
            {topHonors.map((honor, index) => (
              <div
                key={honor._uid}
                className={cn(
                  "flex items-center gap-[1.1875rem] min-h-[6.625rem] p-[1rem] rounded w-full",
                  honorColors[index]
                )}
              >
                <div className="flex items-center justify-center w-[3.6875rem] h-[3.6875rem] flex-shrink-0">
                  <TrophyIcon className="w-[3rem] h-[3rem] text-white" />
                </div>
                <div className="flex-1">
                  <CaseStudyHonor blok={honor} yearTextColor={yearTextColors[index]} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default CaseStudyHighlights; 