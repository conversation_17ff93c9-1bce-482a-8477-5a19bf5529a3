import React from "react";
import Text from "@/components/ui/Text";
import { IStoryblokAssetProps } from "@/common/types";
import { fetchStoryblokStories } from "@/common/storyblok";
import StoryblokLink from "@/components/ui/StoryblokLink";

type Props = {
  learnMoreLabel: string;
  link: string;
  heading: string;
  descriptionText: string;
  image: IStoryblokAssetProps;
};

const ServicesCard = async ({
  learnMoreLabel,
  heading,
  descriptionText,
  link,
  image,
}: Props) => {
  let serviceLink = "";
  try {
    const story = (
      await fetchStoryblokStories({
        uuids: [link],
        draftMode: true,
      })
    )?.data;
    serviceLink = story?.stories?.[0]?.full_slug ?? "";
  } catch {}

  return (
    <StoryblokLink
      link={{
        url: `/${serviceLink}`,
        cached_url: "",
        linktype: "",
      }}
    >
      <div className="flex size-full flex-col rounded bg-white shadow-[0px_0px_17.521px_0px_rgba(86,65,46,0.10)] transition-shadow hover:shadow-[0px_5px_25px_0px_rgba(0,0,0,0.25)]">
        <div
          className="relative size-full h-[12.5rem] max-h-[12.5rem] overflow-hidden rounded-t"
          style={{
            backgroundImage: `url(${image.filename})`,
            backgroundSize: "cover",
            backgroundPosition: "center right",
          }}
        />

        <div className="flex min-h-[300px] flex-1 flex-col gap-y-lg p-6 pt-8">
          <Text className="text-grays-G1" tag="p" style="h4">
            {heading}
          </Text>
          <Text className="flex-1 text-neutral01-75" tag="p" style="b1">
            {descriptionText}
          </Text>

          <div className="mt-auto flex">
            <Text
              tag="span"
              style="b2"
              className="font-bold text-primary01-75 hover:text-primary01-50"
            >
              {learnMoreLabel} →
            </Text>
          </div>
        </div>
      </div>
    </StoryblokLink>
  );
};

export default ServicesCard;
