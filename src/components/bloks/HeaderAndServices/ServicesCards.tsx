import ServiceCard from "./ServiceCard";
import SlideCarousel from "@/components/ui/SlideCarousel";
import { Service } from ".";

interface EventCardsProps {
  learnMoreLabel: string;
  services: Service[];
}

const ServiceCards = ({ learnMoreLabel, services }: EventCardsProps) => {
  return (
    <SlideCarousel transparent={true} wrapperClassName="!pt-0">
      {services.map((service) => {
        return (
          <ServiceCard
            descriptionText={service.descriptionText}
            link={service.link}
            heading={service.heading}
            key={service.heading}
            learnMoreLabel={learnMoreLabel}
            image={service.image}
          />
        );
      })}
    </SlideCarousel>
  );
};

export default ServiceCards;
