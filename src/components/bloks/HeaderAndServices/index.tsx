import React from "react";
import { IStoryblokAssetProps, IStoryblokLink } from "@/common/types";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import StoryblokLink from "@/components/ui/StoryblokLink";
import Button from "@/components/ui/Button";
import ServicesCards from "./ServicesCards";

export interface Service {
  heading: string;
  descriptionText: string;
  link: string;
  image: IStoryblokAssetProps;
}

interface ComponentH2 {
  preHeading: string;
  heading: string;
  bodyContent: string;
  buttonLabel: string;
  useCTA: boolean;
  link: { link: IStoryblokLink }[];
}

export interface IHeaderAndServicesProps {
  blok: {
    title: ComponentH2[];
    serviceCards: Service[];
    learnMoreLabel: string;
  };
}

export default function HeaderAndServicesGrid({
  blok,
}: IHeaderAndServicesProps) {
  const { title, learnMoreLabel, serviceCards } = blok;
  const { preHeading, heading, bodyContent, useCTA, buttonLabel, link } =
    title[0] ?? {};

  return (
    <section className="bg-grays-G6">
      <Container className="!pb-3xl lg:!pb-4xl">
        <div className="mb-1.5 flex flex-col gap-5 lg:flex-row lg:items-end lg:justify-between lg:gap-8">
          <div className="mb-3xl md:max-w-[75%] lg:mb-0 lg:max-w-[60%] xl:max-w-[55%] 2xl:max-w-[50%]">
            <Text
              tag="h3"
              style="q4"
              mdStyle="ph1"
              className="italic text-primary01-50"
            >
              {preHeading}
            </Text>
            <Text
              tag="h3"
              style="h3"
              mdStyle="h2"
              className="mt-2 text-primary01-75"
            >
              {heading}
            </Text>
            <Text
              tag="p"
              style="b2"
              mdStyle="b1"
              className="mt-2 text-neutral01-75 md:mt-4"
            >
              {bodyContent}
            </Text>
          </div>
          {useCTA && link?.[0] && (
            <StoryblokLink link={link[0].link} className="shrink-0">
              <Button theme="secondary" colour="maroon">
                {buttonLabel}
              </Button>
            </StoryblokLink>
          )}
        </div>
      </Container>
      <div>
        <ServicesCards
          learnMoreLabel={learnMoreLabel}
          services={serviceCards}
        />
      </div>
    </section>
  );
}
