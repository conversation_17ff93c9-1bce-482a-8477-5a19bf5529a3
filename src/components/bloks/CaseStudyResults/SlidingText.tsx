"use client";

import { cn } from "@/common/utils";
import { useAnimate } from "motion/react";
import React, { useEffect } from "react";

export default function SlidingLine({
  timer,
  isCurrent,
  isSelectionMode,
}: {
  timer: number;
  isCurrent: boolean;
  isSelectionMode: boolean;
}) {
  const [redLine, animate] = useAnimate();

  useEffect(() => {
    if (isCurrent && !isSelectionMode) {
      animate(
        redLine.current,
        { height: "100%" },
        { duration: timer / 1000, ease: "easeInOut" },
      );

      return () => {
        animate(
          redLine.current,
          { height: "0" },
          { duration: 0.65, ease: "easeIn" },
        );
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCurrent, isSelectionMode]);

  return (
    <div className={cn("flex w-px bg-[#D9D9D9]", !isCurrent && "items-end")}>
      <div
        ref={redLine}
        className="w-px bg-primary01-50"
        style={{ height: isCurrent && isSelectionMode ? "100%" : "0%" }}
      />
    </div>
  );
}
