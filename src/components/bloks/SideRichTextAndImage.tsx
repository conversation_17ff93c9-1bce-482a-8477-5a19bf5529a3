"use client";

import React, { useEffect, useRef, useState } from "react";
import { RichEditor } from "@crimson-education/helios-editor-renderer";
import Image from "next/image";
import Container from "@/components/ui/Container";
import CtaButton from "@/components/bloks/CtaButton";
import { IStoryblokLinkProps } from "@/common/types";

interface Props {
    blok: {
        bodyContent?: {
            content: any;
        }[];
        bodyImage?: {
            id: string;
            alt?: string;
            filename: string;
            title?: string;
        };
        callToAction?: {
            colour: string;
            appearance: string;
            link: IStoryblokLinkProps[];
            text: string;
        }[];
    };
}

const SideRichTextAndImage = ({ blok }: Props) => {
    const { bodyContent, bodyImage } = blok;
    const contentRef = useRef<HTMLDivElement>(null);
    const [enableSticky, setEnableSticky] = useState(false);

    const getAltText = () => {
        if (!bodyImage) return "image";
        if (bodyImage.alt?.trim()) return bodyImage.alt.trim();
        if (bodyImage.title?.trim()) return bodyImage.title.trim();
        const fileName = bodyImage.filename?.split("/").pop();
        return fileName?.replace(/\.[^.]+$/, "") ?? "image";
    };

    useEffect(() => {
        const checkHeight = () => {
            const contentHeight = contentRef.current?.offsetHeight ?? 0;
            const viewportHeight = window.innerHeight;
            setEnableSticky(contentHeight > viewportHeight * 0.7);
        };

        checkHeight();
        window.addEventListener("resize", checkHeight);
        return () => window.removeEventListener("resize", checkHeight);
    }, []);

    return (
        <Container>
            <div className="flex flex-col lg:flex-row gap-y-[46px] lg:gap-x-[60px]">
                <div className="lg:w-1/2" ref={contentRef}>
                    {bodyContent?.map((RichTextContent, index) => (
                        <div className="prose-helios w-full" key={index}>
                            <RichEditor value={RichTextContent.content} />
                        </div>
                    ))}
                    {blok?.callToAction && blok.callToAction.length > 0 && blok?.callToAction[0] && (
                        <div className="mt-8">
                            <CtaButton blok={blok.callToAction[0]} />
                        </div>
                    )}
                </div>

                {bodyImage?.filename && (
                    <div className="lg:w-1/2 w-full">
                        <div
                            className={
                                enableSticky
                                    ? "lg:sticky lg:top-[120px] max-h-[600px] overflow-hidden"
                                    : "max-h-[600px] overflow-hidden"
                            }
                        >
                            <Image
                                src={bodyImage.filename}
                                alt={getAltText()}
                                width={700}
                                height={700}
                                sizes="(min-width: 768px) 740px, 100vw"
                                className="w-full h-auto object-cover rounded"
                            />
                        </div>
                    </div>
                )}
            </div>
        </Container>
    );
};

export default SideRichTextAndImage;