import * as Yup from "yup";
import {
  emailValidation,
  textValidation,
  localNumberValidation,
} from "@/components/ui/Forms/utils/validationRules";

import { ALevelIBDetail } from "./types";

interface ValidationErrorMessages {
  requiredError?: string;
  invalidEmailError?: string;
  subjectRequiredError?: string;
  gradeRequiredError?: string;
  invalidPhoneError?: string;
  hasCountryCodeError?: string;
  privacyPolicyError?: string;
}

const { array, object, string, boolean } = Yup;

const createCountryCodeObjectValidation = (
  hasCountryCodeErrorMessage: string,
) => {
  return object().shape({
    value: string().required(hasCountryCodeErrorMessage),
    label: string(),
  });
};

const createEmailValidation = (
  invalidEmailErrorLabel: string,
  requiredErrorLabel: string,
) => {
  const emailSchema = emailValidation(
    true,
    true,
    true,
    invalidEmailErrorLabel,
    requiredErrorLabel,
  );

  return (
    emailSchema ??
    string().email(invalidEmailErrorLabel).required(requiredErrorLabel)
  );
};

export const createALevelIBDetailsValidation = (
  subjectRequiredErrorLabel: string,
  gradeRequiredErrorLabel: string,
) => {
  return array()
    .of(
      object().shape({
        subject: string().required(subjectRequiredErrorLabel),
        subjectType: string().required("Subject type is required"),
        grade: string().required(gradeRequiredErrorLabel),
      }),
    )
    .min(1, "At least one subject is required")
    .test(
      "unique-subjects",
      "Duplicate subjects are not allowed",
      function (value) {
        if (!value || !Array.isArray(value)) return true;

        const subjects = value
          .filter(
            (item): item is ALevelIBDetail =>
              item && typeof item === "object" && "subject" in item,
          )
          .map((item) => item.subject)
          .filter((subject) => subject && subject.trim() !== "");

        const uniqueSubjects = new Set(subjects);
        return subjects.length === uniqueSubjects.size;
      },
    )
    .test(
      "complete-subjects",
      "All subjects must be complete",
      function (value) {
        if (!value || !Array.isArray(value)) return true;
        for (let i = 0; i < value.length; i++) {
          const item = value[i];
          if (!item || typeof item !== "object") continue;

          const detail = item as ALevelIBDetail;
          if (!detail.subject || !detail.grade || !detail.subjectType) {
            return this.createError({
              path: `aLevelIBDetails[${i}]`,
              message: "Please complete all required fields for this subject",
            });
          }
        }
        return true;
      },
    );
};

export const createIBALevelValidationSchemas = ({
  requiredError = "This field is required",
  invalidEmailError = "Please enter a valid email address",
  subjectRequiredError = "Subject is required",
  gradeRequiredError = "Grade is required",
  invalidPhoneError = "Please enter a valid phone number",
  hasCountryCodeError = "Please select a country code",
  privacyPolicyError = "You must accept the privacy policy",
}: ValidationErrorMessages = {}) => [
  // Step 1: Curriculum selection
  object({
    activeCurriculum: string().required("Please select a curriculum"),
  }),

  // Step 2: Subject selection and grades
  object({
    aLevelIBDetails: createALevelIBDetailsValidation(
      subjectRequiredError,
      gradeRequiredError,
    ),
  }),

  // Step 3: Personal information
  object({
    firstName: textValidation(true, requiredError),
    lastName: textValidation(true, requiredError),
    email: createEmailValidation(invalidEmailError, requiredError),
    country: textValidation(true, requiredError),
    schoolName: textValidation(true, requiredError),
    gradeYearLevel: textValidation(true, requiredError),
    countryCode: createCountryCodeObjectValidation(hasCountryCodeError),
    phoneNumber: localNumberValidation(true, invalidPhoneError, requiredError),
    privacyPolicy: boolean().oneOf([true], privacyPolicyError),
    commsOptIn: boolean(),
  }),
];
