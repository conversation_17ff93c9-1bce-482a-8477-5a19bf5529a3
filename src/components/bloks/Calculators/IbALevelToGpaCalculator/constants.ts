import { IGradeMap } from "./types";

export const SUBJECT_RADIO_LABEL_MAP: Record<string, string[]> = {
  aLevel: ["A2", "AS"],
  IB: ["HL", "SL"],
};

export const MAX_PAGES = 3;
export const MAX_SUBJECTS = 7;

export const GRADE_CHOICES_MAP: Record<string, string[]> = {
  aLevel: ["A*", "A", "B", "C", "D", "E", "F", "G", "U"],
  IB: ["7", "6", "5", "4", "3", "2", "1"],
};

export const A_LEVEL_GRADE_MAP: IGradeMap = {
  "A*": 4.0,
  A: 4.0,
  B: 3.7,
  C: 3.0,
  D: 2.3,
  E: 2.0,
  F: 1.3,
  G: 1.0,
  U: 0,
};

export const IB_GRADE_MAP: IGradeMap = {
  7: 4.0,
  6: 3.7,
  5: 3.3,
  4: 3.0,
  3: 2.7,
  2: 2.3,
  1: 2.0,
};

export const MAX_COUNT_OF_A_LEVEL_SUBJECTS = 7;
export const MAX_COUNT_OF_IB_SUBJECTS = 6;
