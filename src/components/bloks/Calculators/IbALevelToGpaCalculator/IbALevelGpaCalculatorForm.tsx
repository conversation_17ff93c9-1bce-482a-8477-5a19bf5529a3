"use client";
import { useState, useRef, useEffect } from "react";
import { Formik, Form, FormikHelpers } from "formik";

import { DeviceType } from "@/common/types";

import { createIBALevelValidationSchemas } from "./validationSchemas";
import FormLayout from "./FormLayout";
import { handleFormSubmit } from "./formSubmitHandler";
import { useDeviceType } from "../useDeviceType";
import StepIndicator from "../StepIndicator";
import FormButtons from "../FormButtons";

import {
  IBALevelGpaBlokProps,
  IBALevelGpaFormValues,
  ICurriculumType,
  ISubjectType,
} from "./types";

const FORM_OFFSET_Y = 38;

const stagingMarketoProgrammeName = "OP-Script Testing";

const IbALevelGpaCalculatorForm = ({
  blok,
  dynamicContentId = "",
}: {
  blok: IBALevelGpaBlokProps;
  dynamicContentId: string;
}) => {
  const deviceType = useDeviceType();
  const [step, setStep] = useState(1);
  const [, setDirection] = useState<"forward" | "backward">("forward");
  const [animationComplete, setAnimationComplete] = useState(true);

  const isDesktop = deviceType === DeviceType.Desktop;
  const maxSteps = 3;

  const initialValues: IBALevelGpaFormValues = {
    activeCurriculum: ICurriculumType.aLevel,
    firstName: "",
    lastName: "",
    email: "",
    country: "",
    schoolName: "",
    gradeYearLevel: "",
    countryCode: { value: "", label: "" },
    phoneNumber: "",
    privacyPolicy: false,
    commsOptIn: false,
    aLevelIBDetails: [
      {
        subject: "",
        subjectType: ISubjectType.A2,
        grade: "",
      },
    ],
  };

  const validationSchemas = createIBALevelValidationSchemas({
    requiredError: blok.requiredLabel,
    invalidEmailError: blok.invalidEmailErrorLabel,
    subjectRequiredError: blok.requiredLabel,
    gradeRequiredError: blok.requiredLabel,
    invalidPhoneError: blok.invalidPhoneNumberErrorLabel,
    hasCountryCodeError: blok.requiredLabel,
    privacyPolicyError: blok.requiredLabel,
  });

  const isProd =
    process.env.NODE_ENV === "production" &&
    process.env.NEXT_PUBLIC_VERCEL_ENV === "production";

  const handleSubmit = async (
    values: IBALevelGpaFormValues,
    actions: FormikHelpers<IBALevelGpaFormValues>,
  ) => {
    if (step < maxSteps) {
      setAnimationComplete(false);
      setDirection("forward");
      setTimeout(() => {
        setStep(step + 1);
        void actions.setTouched({});
        actions.setSubmitting(false);
        requestAnimationFrame(() => {
          setAnimationComplete(true);
        });
      }, 300);
    } else {
      const successPageLink =
        blok.successPageLink?.[0]?.link?.cached_url ??
        blok.successPageLink?.[0]?.link?.url;
      const newTab = blok.successPageLink?.[0]?.newTab;
      const programName = isProd
        ? blok.marketoProgrammeName
        : stagingMarketoProgrammeName;

      await handleFormSubmit({
        values,
        actions,
        programName,
        successPageLink,
        newTab,
      });
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setAnimationComplete(false);
      setDirection("backward");
      setTimeout(() => {
        setStep((prevStep) => Math.max(1, prevStep - 1));
        requestAnimationFrame(() => {
          setAnimationComplete(true);
        });
      }, 300);
    }
  };

  const getCurrentValidationSchema = () => {
    return validationSchemas[step - 1];
  };

  const formRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isDesktop && formRef.current && dynamicContentId) {
      const dynamicMainContent = document.getElementById(dynamicContentId);
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const formHeight = entry.contentRect.height;
          const calculatedPadding = formHeight - FORM_OFFSET_Y;
          const dynamicPaddingTop = Math.max(0, calculatedPadding);
          if (dynamicMainContent) {
            dynamicMainContent.style.paddingTop = `${dynamicPaddingTop}px`;
          }
        }
      });
      resizeObserver.observe(formRef.current);
      const initialHeight = formRef.current.offsetHeight;
      const calculatedPadding = initialHeight - FORM_OFFSET_Y;
      const dynamicPaddingTop = Math.max(0, calculatedPadding);
      if (dynamicMainContent) {
        dynamicMainContent.style.paddingTop = `${dynamicPaddingTop}px`;
      }
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [isDesktop, dynamicContentId]);

  return (
    <div
      ref={formRef}
      className="rounded-[12px] bg-white px-[1.125rem] py-6 shadow-[0px_0px_21.83px_0px_rgba(86,65,46,0.12)] md:p-[1.875rem] lg:p-5xl"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={getCurrentValidationSchema()}
        onSubmit={handleSubmit}
        validateOnChange
        validateOnBlur
      >
        {({ isSubmitting, ...formikProps }) => {
          return (
            <Form noValidate>
              <div className="">
                <FormLayout step={step} deviceType={deviceType} blok={blok} />
              </div>

              <div className="mt-5xl">
                <FormButtons
                  step={step}
                  mobileMaxStep={3}
                  tabletMaxStep={3}
                  desktopMaxStep={3}
                  deviceType={deviceType}
                  isSubmitting={isSubmitting}
                  animationComplete={animationComplete}
                  handleBack={handleBack}
                  formikProps={formikProps}
                  blok={blok}
                />
              </div>
              <div className="mt-4xl">
                <StepIndicator currentStep={step} totalSteps={maxSteps} />
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default IbALevelGpaCalculatorForm;
