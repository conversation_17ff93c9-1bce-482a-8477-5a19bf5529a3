import { SectionItem } from "@/common/types";

export interface IBALevelGpaBlokProps {
  subtitle: string;
  heading: string;
  subheading: string;
  aLevelHeading: string;
  aLevelSubheading: string;
  subjectPlaceholder: string;
  IBHeading: string;
  IBSubheading: string;
  gradePlaceholder: string;
  privacyPolicyLabel: string;
  studyPathwaysLabel: string;
  formSubheading: string;
  description: string;
  aLevelLabel: string;
  ibLabel: string;
  subjectLabel: string;
  gradeLabel: string;
  addSubjectLabel: string;
  nextButtonLabel: string;
  previousButtonLabel: string;
  submitButtonLabel: string;
  emailPlaceholder: string;
  phoneNumberLabel: string;
  requiredLabel: string;
  invalidPhoneNumberErrorLabel: string;
  lastNamePlaceholder: string;
  firstNamePlaceholder: string;
  schoolNamePlaceholder: string;
  invalidEmailErrorLabel: string;
  contentTwoMainHeading: string;
  contentTwoSubHeadingLabel: string;
  gradeYearLevelPlaceholder: string;
  contentTwoDescriptionLabel: string;
  countryLabel: string;
  successPageLink: {
    link: {
      id: string;
      url: string;
      linkType: string;
      cached_url: string;
    };
    newTab: boolean;
    component: string;
  }[];
  sections: SectionItem[];
  bottomSections: SectionItem[];
  marketoProgrammeName: string;
}

export type IGradeMap = Record<string, number>;

export enum ICurriculumType {
  aLevel = "aLevel",
  IB = "IB",
  NONE = "",
}

export enum ISubjectType {
  A2 = "A2",
  AS = "AS",
  HL = "HL",
  SL = "SL",
}

export interface ALevelIBDetail {
  subject: string;
  subjectType: ISubjectType;
  grade: string;
}

export interface IBALevelGpaFormValues {
  // step 1
  activeCurriculum: ICurriculumType;
  // step 2
  aLevelIBDetails: ALevelIBDetail[];
  // step 3
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  schoolName: string;
  gradeYearLevel: string;
  country: string;
  countryCode: {
    value: string;
    label: string;
  };
  privacyPolicy: boolean;
  commsOptIn: boolean;
}

export interface IBALevelGpaFormErrors {
  activeCurriculum?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  schoolName?: string;
  gradeYearLevel?: string;
  privacyPolicy?: string;
  commsOptIn?: string;
  localNumber?: string;
  country?: string;
  countryCode?: {
    value?: string;
    label?: string;
  };
  aLevelIBDetails?: {
    subject?: string;
    subjectType?: string;
    grade?: string;
  }[];
}

export interface IBALevelGpaFormTouched {
  activeCurriculum?: boolean;
  firstName?: boolean;
  lastName?: boolean;
  email?: boolean;
  phoneNumber?: boolean;
  schoolName?: boolean;
  gradeYearLevel?: boolean;
  privacyPolicy?: boolean;
  commsOptIn?: boolean;
  localNumber?: boolean;
  country?: boolean;
  countryCode?: {
    value?: boolean;
    label?: boolean;
  };
  aLevelIBDetails?: {
    subject?: boolean;
    subjectType?: boolean;
    grade?: boolean;
  }[];
}

export const A_LEVEL_GRADES = [
  { value: "A*", label: "A*" },
  { value: "A", label: "A" },
  { value: "B", label: "B" },
  { value: "C", label: "C" },
  { value: "D", label: "D" },
  { value: "E", label: "E" },
  { value: "U", label: "U" },
];

export const IB_GRADES = [
  { value: "7", label: "7" },
  { value: "6", label: "6" },
  { value: "5", label: "5" },
  { value: "4", label: "4" },
  { value: "3", label: "3" },
  { value: "2", label: "2" },
  { value: "1", label: "1" },
];
