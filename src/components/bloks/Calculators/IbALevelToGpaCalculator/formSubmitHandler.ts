import { FormikHel<PERSON> } from "formik";
import { submitIBALevelToGpaForms, IB_A_LEVEL_GPA_RESULT_KEY } from "./util";

import { IBALevelGpaFormValues } from "./types";

interface SubmitHandlerOptions {
  values: IBALevelGpaFormValues;
  actions: FormikHelpers<IBALevelGpaFormValues>;
  programName: string;
  successPageLink?: string;
  newTab?: boolean;
}

export const handleFormSubmit = async ({
  values,
  actions,
  programName,
  successPageLink,
  newTab,
}: SubmitHandlerOptions) => {
  actions.setSubmitting(true);

  try {
    const gpaScore = await submitIBALevelToGpaForms(values, programName);
    if (typeof window !== "undefined") {
      sessionStorage.setItem(
        IB_A_LEVEL_GPA_RESULT_KEY,
        JSON.stringify({
          gpaScore: gpaScore,
          firstName: values.firstName,
          lastName: values.lastName,
        }),
      );
    }

    if (successPageLink) {
      if (newTab) {
        window.open(`/${successPageLink}`, "_blank");
      } else {
        window.location.href = `/${successPageLink}`;
      }
    }

    return { success: true, gpaScore };
  } catch (error) {
    actions.setStatus({
      error: "Failed to submit form. Please try again later.",
    });
    return { success: false, error };
  } finally {
    actions.setSubmitting(false);
  }
};
