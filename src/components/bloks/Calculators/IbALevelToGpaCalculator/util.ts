import {
  getCookie,
  gtmEvents,
  triggerCustomEvent,
  trackFacebookEvent,
  fbEvents,
} from "@/common/analytics";

import { A_LEVEL_GRADE_MAP, IB_GRADE_MAP } from "./constants";

import {
  ICurriculumType,
  ALevelIBDetail,
  IGradeMap,
  IBALevelGpaFormValues,
} from "./types";

export const calculateGpa = (
  curriculum: ICurriculumType,
  subjects: ALevelIBDetail[],
) => {
  switch (curriculum) {
    case ICurriculumType.aLevel:
      return calculateALevelGpa(subjects);
    case ICurriculumType.IB:
      return calculateIBGpa(subjects);
    default:
      return 0;
  }
};

const calculateALevelGpa = (subjects: ALevelIBDetail[]) => {
  const totalSubjects = subjects.length;
  const totalGpa = getTotalGpa(subjects, A_LEVEL_GRADE_MAP);
  return totalGpa / totalSubjects;
};

const calculateIBGpa = (subjects: ALevelIBDetail[]) => {
  const totalSubjects = subjects.length;
  const totalGpa = getTotalGpa(subjects, IB_GRADE_MAP);
  return totalGpa / totalSubjects;
};

const getTotalGpa = (subjects: ALevelIBDetail[], gradeMap: IGradeMap) =>
  subjects.reduce((total, subject) => {
    const grade = gradeMap[subject.grade] ?? 0;
    return total + grade;
  }, 0);

// Send form data to marketo
export const submitMarketoForm = async (
  marketoProgramName: string,
  inputs: IBALevelGpaFormValues,
  gpaScore: number,
) => {
  const {
    firstName,
    lastName,
    email,
    commsOptIn,
    country,
    privacyPolicy,
    schoolName,
    gradeYearLevel,
    phoneNumber,
  } = inputs;

  const marketoBodyObject = {
    munchkinId: typeof window !== "undefined" ? getCookie("_mkto_trk") : "",
    marketoLeadBody: {
      programName: marketoProgramName,
      source: "Crimson Website",
      reason: "Filled out A-Level/IB to GPA Calculator Submission",
      lookupField: "email",
      input: [
        {
          firstName: firstName,
          lastName: lastName,
          email: email,
          MKT_UTM_Campaign__c: getCookie("x-crimson-utm_campaign"),
          MKT_UTM_Medium__c:
            getCookie("x-crimson-utm_medium") ?? "organic_search",
          MKT_UTM_Source__c: getCookie("x-crimson-utm_source"),
          MKT_UTM_Term__c: getCookie("x-crimson-utm_term"),
          MKT_UTM_Content__c: getCookie("x-crimson-utm_content"),
          fbclid__c: getCookie("x-crimson-fbclid"),
          gclid__c: getCookie("x-crimson-gclid"),
          School__c: schoolName,
          School_Year_Grade_Level__c: gradeYearLevel,
          country: country,
          predictedGPAScore: gpaScore,
          phone: phoneNumber !== "" ? phoneNumber : "+211111111",
          privacyPolicy: privacyPolicy,
          unsubscribed: !commsOptIn,
        },
      ],
    },
  };

  try {
    const response = await fetch("/api/calculators", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(marketoBodyObject),
    });

    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }

    if (typeof window !== "undefined") {
      if (
        (
          window as {
            solve?: { identify?: (data: any) => void };
          }
        )?.solve?.identify
      ) {
        (
          window as {
            solve?: { identify?: (data: any) => void };
          }
        )?.solve?.identify?.({
          email: email,
          attributes: {
            last_form_touch: "ib_a_level_to_gpa",
          },
          tags: ["Student"],
        });
      }

      triggerCustomEvent(
        gtmEvents.IB_ALEVEL_TO_GPA_FORM_SUBMISSION,
        "ib-aLevel-gpa-calculator-submit",
      );
      trackFacebookEvent(fbEvents.LEAD);
    }

    return await response.json();
  } catch (error) {
    console.error("Error submitting to Marketo:", error);
    throw error;
  }
};

export const submitIBALevelToGpaForms = async (
  formValues: IBALevelGpaFormValues,
  marketoProgrammeName: string,
) => {
  const activeCurriculum = formValues.activeCurriculum;
  const aLevelIBDetails = formValues.aLevelIBDetails;
  const gpaScore = calculateGpa(activeCurriculum, aLevelIBDetails);

  const decimalGpaScore = Number(gpaScore.toFixed(1));

  await submitMarketoForm(marketoProgrammeName, formValues, decimalGpaScore);

  return decimalGpaScore;
};

export const IB_A_LEVEL_GPA_RESULT_KEY = "ibALevelGpaResult";
