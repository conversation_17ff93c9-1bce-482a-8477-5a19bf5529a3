// useDeviceType.ts
import { useState, useEffect } from "react";
import { DeviceType } from "@/common/types";

export const useDeviceType = () => {
  const [deviceType, setDeviceType] = useState<DeviceType>(DeviceType.Mobile);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setDeviceType(DeviceType.Mobile);
      } else if (width < 1024) {
        setDeviceType(DeviceType.Tablet);
      } else {
        setDeviceType(DeviceType.Desktop);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return deviceType;
};
