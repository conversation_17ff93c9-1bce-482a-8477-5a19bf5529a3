import React, { useEffect } from "react";
import { useFormikContext } from "formik";

import TextField from "@/components/ui/FormInputFields/TextField";

import {
  NceaGpaFormErrors,
  NceaGpaFormTouched,
  INceaGpaFormValues,
} from "./types";

interface GradeFieldProps {
  level: string;
  grade: string;
  label: string;
}

function setupNumberInputScrollPrevention() {
  const preventWheelOnNumberInputs = (e: WheelEvent) => {
    const target = e.target as HTMLElement;
    if (
      target.tagName === "INPUT" &&
      (target as HTMLInputElement).type === "number"
    ) {
      e.preventDefault();
    }
  };

  const preventTouchOnNumberInputs = (e: TouchEvent) => {
    const target = document.activeElement as HTMLElement;
    if (
      target &&
      target.tagName === "INPUT" &&
      (target as HTMLInputElement).type === "number"
    ) {
      e.stopPropagation();
    }
  };
  document.addEventListener("wheel", preventWheelOnNumberInputs, {
    passive: false,
  });
  document.addEventListener("touchstart", preventTouchOnNumberInputs, {
    passive: false,
  });
  document.addEventListener("touchmove", preventTouchOnNumberInputs, {
    passive: false,
  });

  return () => {
    document.removeEventListener("wheel", preventWheelOnNumberInputs);
    document.removeEventListener("touchstart", preventTouchOnNumberInputs);
    document.removeEventListener("touchmove", preventTouchOnNumberInputs);
  };
}

const IntScoreField: React.FC<GradeFieldProps> = ({ level, grade, label }) => {
  const { errors, touched, values } = useFormikContext<INceaGpaFormValues>();

  const fieldKey = `${level}${grade}` as keyof INceaGpaFormValues;
  const errorKey = `${level}${grade}` as keyof NceaGpaFormErrors;
  const touchedKey = `${level}${grade}` as keyof NceaGpaFormTouched;

  const fieldValue = values[fieldKey];

  const placeholder = fieldValue === "" ? "0" : "";

  useEffect(() => {
    const cleanup = setupNumberInputScrollPrevention();
    return cleanup;
  }, []);

  return (
    <div className="flex flex-col">
      <TextField
        type="number"
        nameOfField={`${level}${grade}`}
        label={label}
        placeholder={placeholder}
        theme="light"
        error={errors[errorKey] as string}
        touched={touched[touchedKey] as boolean}
      />
    </div>
  );
};

export default IntScoreField;
