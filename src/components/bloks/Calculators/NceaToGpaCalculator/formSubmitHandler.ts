import { FormikHelpers } from "formik";
import {
  NceaLevelDetails,
  submitNceaToGpaForms,
  NCEA_GPA_RESULT_KEY,
} from "./util";

import { INceaGpaFormValues } from "./types";

interface SubmitHandlerOptions {
  values: INceaGpaFormValues;
  actions: FormikHelpers<INceaGpaFormValues>;
  programName: string;
  successPageLink?: string;
  newTab?: boolean;
}

const convertEmptyStringsToZero = (values: INceaGpaFormValues) => {
  const convertedValues = { ...values };

  const gradeFields = [
    "level1N",
    "level1A",
    "level1M",
    "level1E",
    "level2N",
    "level2A",
    "level2M",
    "level2E",
    "level3N",
    "level3A",
    "level3M",
    "level3E",
  ];

  gradeFields.forEach((field) => {
    const key = field as keyof INceaGpaFormValues;
    if (
      convertedValues[key] === "" ||
      convertedValues[key] === null ||
      convertedValues[key] === undefined
    ) {
      (convertedValues[key] as unknown as number) = 0;
    } else {
      (convertedValues[key] as number) = Number(convertedValues[key]);
    }
  });

  return convertedValues;
};

export const handleFormSubmit = async ({
  values,
  actions,
  programName,
  successPageLink,
  newTab,
}: SubmitHandlerOptions) => {
  const processedValues = convertEmptyStringsToZero(values);

  actions.setSubmitting(true);

  try {
    const levelDetails: NceaLevelDetails[] = [
      {
        nonAchieved: processedValues.level1N as number,
        achieved: processedValues.level1A as number,
        merit: processedValues.level1M as number,
        excellent: processedValues.level1E as number,
      },
      {
        nonAchieved: processedValues.level2N as number,
        achieved: processedValues.level2A as number,
        merit: processedValues.level2M as number,
        excellent: processedValues.level2E as number,
      },
      {
        nonAchieved: processedValues.level3N as number,
        achieved: processedValues.level3A as number,
        merit: processedValues.level3M as number,
        excellent: processedValues.level3E as number,
      },
    ];

    const marketoData = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email,
      country: values.country,
      schoolName: values.schoolName,
      gradeYearLevel: values.gradeYearLevel,
      phoneNumber: `${values?.countryCode?.value}${values.phoneNumber}`,
      levelDetails: levelDetails,
      commsOptIn: "yes",
      privacyPolicy: "yes",
    };

    const gpaScore = await submitNceaToGpaForms(marketoData, programName);

    if (typeof window !== "undefined") {
      sessionStorage.setItem(
        NCEA_GPA_RESULT_KEY,
        JSON.stringify({
          gpaScore,
          firstName: values.firstName,
          lastName: values.lastName,
        }),
      );
    }

    if (successPageLink) {
      if (newTab) {
        window.open(`/${successPageLink}`, "_blank");
      } else {
        window.location.href = `/${successPageLink}`;
      }
    }

    return { success: true, gpaScore };
  } catch (error) {
    actions.setStatus({
      error: "Failed to submit form. Please try again later.",
    });
    return { success: false, error };
  } finally {
    actions.setSubmitting(false);
  }
};
