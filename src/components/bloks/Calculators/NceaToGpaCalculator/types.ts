import { SectionItem, StoryData } from "@/common/types";
interface FullBlokConfig {
  _uid: string;
  anchorId: string;
  formComponent: any[];
  header: string;
  pageTitle: string;
  subHeader: string;
  description: string;
  coverImage: string;
  excerpt: string;
  title: string;
  subtitle: string;
  blogTags: {
    tag: string;
    component: string;
  }[];
  MarkDown?: string;
  titleImage?: string;
  sections: SectionItem[];
  bottomSections: SectionItem[];

  levelLabel: string;
  nonAchievedLabel: string;
  achievedLabel: string;
  meritLabel: string;
  excellentLabel: string;
  nextButtonLabel: string;
  previousButtonLabel: string;
  submitButtonLabel: string;
  emailPlaceholder: string;
  phoneNumberLabel: string;
  requiredErrorLabel: string;
  lastNamePlaceholder: string;
  firstNamePlaceholder: string;
  schoolNamePlaceholder: string;
  invalidEmailErrorLabel: string;
  contentTwoMainHeading: string;
  contentTwoSubHeadingLabel: string;
  gradeYearLevelPlaceholder: string;
  contentTwoDescriptionLabel: string;
  countryLabel: string;
  successPageLink: {
    link: {
      id: string;
      url: string;
      linkType: string;
      cached_url: string;
    };
    newTab: boolean;
    component: string;
  }[];
  marketoProgrammeName: string;
}

type FormFields = Pick<
  FullBlokConfig,
  | "levelLabel"
  | "nonAchievedLabel"
  | "achievedLabel"
  | "meritLabel"
  | "excellentLabel"
  | "nextButtonLabel"
  | "previousButtonLabel"
  | "submitButtonLabel"
  | "emailPlaceholder"
  | "phoneNumberLabel"
  | "requiredErrorLabel"
  | "lastNamePlaceholder"
  | "firstNamePlaceholder"
  | "schoolNamePlaceholder"
  | "invalidEmailErrorLabel"
  | "contentTwoMainHeading"
  | "contentTwoSubHeadingLabel"
  | "gradeYearLevelPlaceholder"
  | "contentTwoDescriptionLabel"
  | "countryLabel"
  | "successPageLink"
  | "marketoProgrammeName"
>;

export interface NceaGpaFormBlokProps extends FormFields {
  description: string;
}

export interface NceaGpaCalculatorPageProps {
  locale: string;
  slug: string;
  uuid: string;
  story: StoryData["story"];
  blok: FullBlokConfig;
}

export interface NceaGpaFormErrors {
  level1N?: string;
  level1A?: string;
  level1M?: string;
  level1E?: string;
  level2N?: string;
  level2A?: string;
  level2M?: string;
  level2E?: string;
  level3N?: string;
  level3A?: string;
  level3M?: string;
  level3E?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  country?: string;
  schoolName?: string;
  gradeYearLevel?: string;
  phoneNumber?: string;
  countryCode?: {
    label: string;
    value: string;
  };
}

export interface NceaGpaFormTouched {
  level1N?: boolean;
  level1A?: boolean;
  level1M?: boolean;
  level1E?: boolean;
  level2N?: boolean;
  level2A?: boolean;
  level2M?: boolean;
  level2E?: boolean;
  level3N?: boolean;
  level3A?: boolean;
  level3M?: boolean;
  level3E?: boolean;
  firstName?: boolean;
  lastName?: boolean;
  email?: boolean;
  country?: boolean;
  schoolName?: boolean;
  gradeYearLevel?: boolean;
  phoneNumber?: boolean;
  countryCode?: {
    label: boolean;
    value: boolean;
  };
}

export interface INceaGpaFormValues {
  level1N: string | number;
  level1A: string | number;
  level1M: string | number;
  level1E: string | number;
  level2N: string | number;
  level2A: string | number;
  level2M: string | number;
  level2E: string | number;
  level3N: string | number;
  level3A: string | number;
  level3M: string | number;
  level3E: string | number;
  firstName: string;
  lastName: string;
  email: string;
  country: string;
  schoolName: string;
  gradeYearLevel: string;
  countryCode: {
    label: string;
    value: string;
  };
  phoneNumber: string;
}
