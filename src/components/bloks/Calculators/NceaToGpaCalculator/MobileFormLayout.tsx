import React from "react";
import { DeviceType } from "@/common/types";
import IntScoreField from "./IntScoreField";

import Text from "@/components/ui/Text";
import CountryField from "@/components/ui/FormInputFields/CountryField";
import PhoneNumberField from "@/components/ui/FormInputFields/PhoneNumberField";
import TextField from "@/components/ui/FormInputFields/TextField";

import { NceaGpaFormBlokProps, INceaGpaFormValues } from "./types";

import { useFormikContext } from "formik";

interface MobileFormContentProps {
  step: number;
  blok: NceaGpaFormBlokProps;
  deviceType: DeviceType;
}

const MobileFormLayout: React.FC<MobileFormContentProps> = ({ step, blok }) => {
  const { values, setFieldValue, errors, touched } =
    useFormikContext<INceaGpaFormValues>();

  switch (step) {
    case 1:
      return (
        <>
          <div className="mb-6 md:mb-6 lg:mb-5xl">
            <Text tag="p" style="mb1" className="text-center">
              {blok.description}
            </Text>
          </div>

          <Text tag="p" style="sh6" className="mb-3 text-primary01-75">
            {blok.levelLabel} 1
          </Text>
          <div className="flex flex-col gap-6">
            <IntScoreField
              level="level1"
              grade="N"
              label={blok.nonAchievedLabel}
            />
            <IntScoreField
              level="level1"
              grade="A"
              label={blok.achievedLabel}
            />
            <IntScoreField level="level1" grade="M" label={blok.meritLabel} />
            <IntScoreField
              level="level1"
              grade="E"
              label={blok.excellentLabel}
            />
          </div>
        </>
      );
    case 2:
      return (
        <>
          <div className="mb-6 md:mb-6 lg:mb-5xl">
            <Text tag="p" style="mb1" className="text-center">
              {blok.description}
            </Text>
          </div>
          <Text tag="p" style="sh6" className="mb-3 text-primary01-75">
            {blok.levelLabel} 2
          </Text>
          <div className="flex flex-col gap-6">
            <IntScoreField
              level="level2"
              grade="N"
              label={blok.nonAchievedLabel}
            />
            <IntScoreField
              level="level2"
              grade="A"
              label={blok.achievedLabel}
            />
            <IntScoreField level="level2" grade="M" label={blok.meritLabel} />
            <IntScoreField
              level="level2"
              grade="E"
              label={blok.excellentLabel}
            />
          </div>
        </>
      );
    case 3:
      return (
        <>
          <div className="mb-6 md:mb-6 lg:mb-5xl">
            <Text tag="p" style="mb1" className="text-center">
              {blok.description}
            </Text>
          </div>
          <Text tag="p" style="sh6" className="mb-3 text-primary01-75">
            {blok.levelLabel} 3
          </Text>
          <div className="flex flex-col gap-6">
            <IntScoreField
              level="level3"
              grade="N"
              label={blok.nonAchievedLabel}
            />
            <IntScoreField
              level="level3"
              grade="A"
              label={blok.achievedLabel}
            />
            <IntScoreField level="level3" grade="M" label={blok.meritLabel} />
            <IntScoreField
              level="level3"
              grade="E"
              label={blok.excellentLabel}
            />
          </div>
        </>
      );
    case 4:
      return (
        <>
          <Text
            tag="p"
            style="mb1"
            className="mb-6 text-center text-base text-primary01-75"
          >
            {blok.contentTwoDescriptionLabel}
          </Text>

          <div className="grid grid-cols-1 gap-6">
            <TextField
              nameOfField="firstName"
              label={blok.firstNamePlaceholder}
              placeholder={blok.firstNamePlaceholder}
              theme="light"
              error={errors?.firstName}
              touched={touched?.firstName}
            />
            <TextField
              nameOfField="lastName"
              label={blok.lastNamePlaceholder}
              placeholder={blok.lastNamePlaceholder}
              theme="light"
              error={errors.lastName}
              touched={touched.lastName}
            />
            <TextField
              nameOfField="email"
              label={blok.emailPlaceholder}
              placeholder={blok.emailPlaceholder}
              theme="light"
              error={errors.email}
              touched={touched.email}
            />

            <CountryField
              label={blok.countryLabel}
              language="en"
              nameOfField="country"
              setFieldValue={setFieldValue as any}
              theme="light"
              placeholder={blok.countryLabel}
              error={errors.country}
              touched={touched.country}
            />

            <TextField
              nameOfField="schoolName"
              label={blok.schoolNamePlaceholder}
              placeholder={blok.schoolNamePlaceholder}
              theme="light"
              error={errors?.schoolName}
              touched={touched?.schoolName}
            />
            <TextField
              type="number"
              nameOfField="gradeYearLevel"
              label={blok.gradeYearLevelPlaceholder}
              placeholder={blok.gradeYearLevelPlaceholder}
              theme="light"
              error={errors?.gradeYearLevel}
              touched={touched?.gradeYearLevel}
            />
          </div>

          <div className="mt-4xl">
            <PhoneNumberField
              label={blok.phoneNumberLabel}
              nameOfField="phoneNumber"
              localNumberFieldName="phoneNumber"
              countryCodeFieldName="countryCode"
              localNumberPlaceholder={blok.phoneNumberLabel}
              countryCodePlaceholder="▾"
              theme="light"
              formId="nceaGpaForm"
              language="en"
              autofillCountryCodeByIP={true}
              errors={{
                countryCode: {
                  value: errors.countryCode?.value ?? "",
                  label: errors.countryCode?.label,
                },
                localNumber: errors.phoneNumber ?? "",
              }}
              touched={{
                countryCode: {
                  value: touched.countryCode?.value ?? false,
                  label: touched.countryCode?.label ?? false,
                },
                localNumber: touched.phoneNumber ?? false,
              }}
              values={{
                countryCode: values.countryCode,
                localNumber: values.phoneNumber,
              }}
              enableCountryLinking
            />
          </div>
        </>
      );
    default:
      return null;
  }
};

export default MobileFormLayout;
