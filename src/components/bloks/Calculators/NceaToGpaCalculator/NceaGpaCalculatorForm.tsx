"use client";
import { useState, useRef, useEffect } from "react";
import { Formik, Form, FormikHelpers } from "formik";
import { DeviceType } from "@/common/types";

import {
  createMobileValidationSchemas,
  createTabletDesktopValidationSchemas,
} from "./validationSchemas";
import { handleFormSubmit } from "./formSubmitHandler";
import { useDeviceType } from "../useDeviceType";

import MobileFormLayout from "./MobileFormLayout";
import TabletDesktopFormLayout from "./TabletDesktopFormLayout";
import StepIndicator from "../StepIndicator";
import FormButtons from "../FormButtons";

import { INceaGpaFormValues } from "./types";

const FORM_OFFSET_Y = 38;

interface NceaGpaCalculatorFormProps {
  blok: {
    description: string;
    levelLabel: string;
    nonAchievedLabel: string;
    achievedLabel: string;
    meritLabel: string;
    excellentLabel: string;
    nextButtonLabel: string;
    previousButtonLabel: string;
    submitButtonLabel: string;
    emailPlaceholder: string;
    phoneNumberLabel: string;
    requiredErrorLabel: string;
    lastNamePlaceholder: string;
    firstNamePlaceholder: string;
    schoolNamePlaceholder: string;
    invalidEmailErrorLabel: string;
    contentTwoMainHeading: string;
    contentTwoSubHeadingLabel: string;
    gradeYearLevelPlaceholder: string;
    contentTwoDescriptionLabel: string;
    countryLabel: string;
    successPageLink: {
      link: {
        id: string;
        url: string;
        linkType: string;
        cached_url: string;
      };
      newTab: boolean;
      component: string;
    }[];
    marketoProgrammeName: string;
  };
  dynamicContentId?: string;
}

const stagingMarketoProgrammeName = "OP-Script Testing";

const NceaGpaCalculatorForm = ({
  blok,
  dynamicContentId = "",
}: NceaGpaCalculatorFormProps) => {
  const deviceType = useDeviceType();
  const [step, setStep] = useState(1);
  const [, setDirection] = useState<"forward" | "backward">("forward");
  const [animationComplete, setAnimationComplete] = useState(true);

  const isMobile = deviceType === DeviceType.Mobile;
  const maxSteps = isMobile ? 4 : 2;

  const initialValues: INceaGpaFormValues = {
    level1N: "",
    level1A: "",
    level1M: "",
    level1E: "",
    level2N: "",
    level2A: "",
    level2M: "",
    level2E: "",
    level3N: "",
    level3A: "",
    level3M: "",
    level3E: "",
    firstName: "",
    lastName: "",
    email: "",
    country: "",
    schoolName: "",
    gradeYearLevel: "",
    countryCode: { value: "", label: "" },
    phoneNumber: "",
  };

  const validationSchemas = createMobileValidationSchemas(
    blok.requiredErrorLabel,
    blok.invalidEmailErrorLabel,
  );

  const tabletDesktopValidationSchemas = createTabletDesktopValidationSchemas(
    blok.requiredErrorLabel,
    blok.invalidEmailErrorLabel,
  );

  const isProd =
    process.env.NODE_ENV === "production" &&
    process.env.NEXT_PUBLIC_VERCEL_ENV === "production";

  const handleSubmit = async (
    values: INceaGpaFormValues,
    actions: FormikHelpers<INceaGpaFormValues>,
  ) => {
    if (step < maxSteps) {
      setAnimationComplete(false);
      setDirection("forward");
      setTimeout(() => {
        setStep(step + 1);
        void actions.setTouched({});
        actions.setSubmitting(false);
        requestAnimationFrame(() => {
          setAnimationComplete(true);
        });
      }, 300);
    } else {
      const successPageLink =
        blok.successPageLink?.[0]?.link?.cached_url ??
        blok.successPageLink?.[0]?.link?.url;
      const newTab = blok.successPageLink?.[0]?.newTab;
      const programName = isProd
        ? blok.marketoProgrammeName
        : stagingMarketoProgrammeName;

      await handleFormSubmit({
        values,
        actions,
        programName,
        successPageLink,
        newTab,
      });
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setAnimationComplete(false);
      setDirection("backward");
      setTimeout(() => {
        setStep((prevStep) => Math.max(1, prevStep - 1));
        requestAnimationFrame(() => {
          setAnimationComplete(true);
        });
      }, 300);
    }
  };

  const getCurrentValidationSchema = () => {
    if (isMobile) {
      return validationSchemas[step - 1];
    } else {
      return tabletDesktopValidationSchemas[step - 1];
    }
  };

  const formRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isMobile && formRef.current && dynamicContentId) {
      const dynamicMainContent = document.getElementById(dynamicContentId);
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const formHeight = entry.contentRect.height;
          const calculatedPadding = formHeight - FORM_OFFSET_Y;
          const dynamicPaddingTop = Math.max(0, calculatedPadding);
          if (dynamicMainContent) {
            dynamicMainContent.style.paddingTop = `${dynamicPaddingTop}px`;
          }
        }
      });
      resizeObserver.observe(formRef.current);
      const initialHeight = formRef.current.offsetHeight;
      const calculatedPadding = initialHeight - FORM_OFFSET_Y;
      const dynamicPaddingTop = Math.max(0, calculatedPadding);
      if (dynamicMainContent) {
        dynamicMainContent.style.paddingTop = `${dynamicPaddingTop}px`;
      }
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [isMobile, dynamicContentId]);

  return (
    <div
      ref={formRef}
      className="rounded-[12px] bg-white px-[1.125rem] py-6 shadow-[0px_0px_21.83px_0px_rgba(86,65,46,0.12)] md:p-[1.875rem] lg:p-5xl"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={getCurrentValidationSchema()}
        onSubmit={handleSubmit}
        validateOnChange
        validateOnBlur
      >
        {({ isSubmitting, ...formikProps }) => {
          return (
            <Form noValidate>
              <div className="min-h-[320px] md:min-h-[400px]">
                {isMobile ? (
                  <MobileFormLayout
                    step={step}
                    blok={blok}
                    deviceType={deviceType}
                  />
                ) : (
                  <TabletDesktopFormLayout
                    step={step}
                    blok={blok}
                    deviceType={deviceType}
                  />
                )}
              </div>

              <div className="mt-5xl">
                <FormButtons
                  step={step}
                  mobileMaxStep={4}
                  tabletMaxStep={2}
                  desktopMaxStep={2}
                  deviceType={deviceType}
                  isSubmitting={isSubmitting}
                  animationComplete={animationComplete}
                  handleBack={handleBack}
                  formikProps={formikProps}
                  blok={blok}
                />
              </div>

              <div className="mt-4xl">
                <StepIndicator currentStep={step} totalSteps={maxSteps} />
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default NceaGpaCalculatorForm;
