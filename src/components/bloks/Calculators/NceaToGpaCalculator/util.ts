import {
  getCookie,
  gtmEvents,
  triggerCustomEvent,
  trackFacebookEvent,
  fbEvents,
} from "@/common/analytics";

export interface NceaLevelDetails {
  nonAchieved: number;
  achieved: number;
  merit: number;
  excellent: number;
}

/**
 * Calculates eligible grade points and number of credits for one level of NCEA
 */
export const calculateNceaToGpaPerLevel = (
  excellenceCredits: number,
  meritCredits: number,
  achievedCredits: number,
  notAchieved: number,
): [number, number] => {
  // Add excellence credits to GPA, subject to limit of 100 credits
  let totalCredits = Math.min(excellenceCredits, 100);
  // Update total credits counted towards GPA
  let totalGradeValues = Math.min(excellenceCredits, 100) * 4;

  // Add merit credits to GPA, subject to limit of 100 credits
  totalGradeValues += Math.min(100 - totalCredits, meritCredits) * 3;
  // Update total credits counted towards GPA
  totalCredits += Math.min(100 - totalCredits, meritCredits);

  // Add achieved credits to GPA, subject to limit of 100 credits
  totalGradeValues += Math.min(100 - totalCredits, achievedCredits) * 2;
  // Update total credits counted towards GPA
  totalCredits += Math.min(100 - totalCredits, achievedCredits);

  // Update total credits counted towards GPA
  totalCredits += Math.min(100 - totalCredits, notAchieved);

  return [totalGradeValues, totalCredits];
};

export const convertNceaToGpa = (levelDetails: NceaLevelDetails[]): string => {
  const result: [number, number][] = levelDetails.map((details) => {
    const { nonAchieved, achieved, merit, excellent } = details;
    return calculateNceaToGpaPerLevel(excellent, merit, achieved, nonAchieved);
  });

  const level_1_results: [number, number] = result?.[0] ?? [0, 0];
  const level_2_results: [number, number] = result?.[1] ?? [0, 0];
  const level_3_results: [number, number] = result?.[2] ?? [0, 0];

  const totalGradeValues: number =
    level_1_results[0] + level_2_results[0] + level_3_results[0];
  const totalCredits: number =
    level_1_results[1] + level_2_results[1] + level_3_results[1];

  if (totalGradeValues === 0 && totalCredits === 0) return (2.0).toFixed(2);
  return (totalGradeValues / totalCredits).toFixed(2);
};

// Send form data to marketo
export const submitMarketoForm = async (
  marketoProgramName: string,
  inputs: {
    firstName: string;
    lastName: string;
    email: string;
    commsOptIn?: string;
    country: string;
    privacyPolicy?: string;
    schoolName: string;
    gradeYearLevel: string;
    localNumber?: string;
    countryCode?: { value: string };
    phoneNumber: string;
  },
  gpaScore: string,
) => {
  const {
    firstName,
    lastName,
    email,
    commsOptIn = "yes",
    country,
    privacyPolicy = "yes",
    schoolName,
    gradeYearLevel,
    phoneNumber,
  } = inputs;

  const marketoBodyObject = {
    munchkinId: typeof window !== "undefined" ? getCookie("_mkto_trk") : "",
    marketoLeadBody: {
      programName: marketoProgramName,
      source: "Crimson Website",
      reason: "Filled out NCEA Calculator Submission",
      lookupField: "email",
      input: [
        {
          firstName: firstName,
          lastName: lastName,
          email: email,
          MKT_UTM_Campaign__c: getCookie("x-crimson-utm_campaign"),
          MKT_UTM_Medium__c:
            getCookie("x-crimson-utm_medium") ?? "organic_search",
          MKT_UTM_Source__c: getCookie("x-crimson-utm_source"),
          MKT_UTM_Term__c: getCookie("x-crimson-utm_term"),
          MKT_UTM_Content__c: getCookie("x-crimson-utm_content"),
          fbclid__c: getCookie("x-crimson-fbclid"),
          gclid__c: getCookie("x-crimson-gclid"),
          School__c: schoolName,
          School_Year_Grade_Level__c: gradeYearLevel,
          country: country,
          predictedGPAScore: gpaScore,
          phone: phoneNumber !== "" ? phoneNumber : "+211111111",
          privacyPolicy: privacyPolicy,
          unsubscribed: !commsOptIn,
        },
      ],
    },
  };

  try {
    const response = await fetch("/api/calculators", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(marketoBodyObject),
    });

    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }

    if (typeof window !== "undefined") {
      if (
        (
          window as {
            solve?: { identify?: (data: any) => void };
          }
        )?.solve?.identify
      ) {
        (
          window as {
            solve?: { identify?: (data: any) => void };
          }
        )?.solve?.identify?.({
          email: email,
          attributes: {
            last_form_touch: "ncea_to_gpa",
          },
          tags: ["Student"],
        });
      }

      triggerCustomEvent(
        gtmEvents.NCEA_TO_GPA_FORM_SUBMISSION,
        "ncea-gpa-calculator-submit",
      );
      trackFacebookEvent(fbEvents.LEAD);
    }

    return await response.json();
  } catch (error) {
    console.error("Error submitting to Marketo:", error);
    throw error;
  }
};

export const submitNceaToGpaForms = async (
  formValues: {
    firstName: string;
    lastName: string;
    email: string;
    commsOptIn?: string;
    country: string;
    privacyPolicy?: string;
    schoolName: string;
    gradeYearLevel: string;
    localNumber?: string;
    countryCode?: { value: string };
    levelDetails: NceaLevelDetails[];
    phoneNumber: string;
  },
  marketoProgrammeName: string,
) => {
  const gpaScore = convertNceaToGpa(formValues.levelDetails);

  await submitMarketoForm(marketoProgrammeName, formValues, gpaScore);

  return gpaScore;
};

export const NCEA_GPA_RESULT_KEY = "nceaGpaResult";
