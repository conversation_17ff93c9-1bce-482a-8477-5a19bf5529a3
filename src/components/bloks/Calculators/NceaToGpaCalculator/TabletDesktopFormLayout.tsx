import React from "react";
import { cn } from "@/common/utils";
import { DeviceType } from "@/common/types";
import Text from "@/components/ui/Text";
import CountryField from "@/components/ui/FormInputFields/CountryField";
import PhoneNumberField from "@/components/ui/FormInputFields/PhoneNumberField";
import TextField from "@/components/ui/FormInputFields/TextField";

import { useFormikContext } from "formik";

import { NceaGpaFormBlokProps, INceaGpaFormValues } from "./types";

import IntScoreField from "./IntScoreField";

interface TabletDesktopFormContentProps {
  step: number;
  blok: NceaGpaFormBlokProps;
  deviceType: DeviceType;
}

const TabletDesktopFormLayout: React.FC<TabletDesktopFormContentProps> = ({
  step,
  blok,
  deviceType,
}) => {
  const { values, setFieldValue, errors, touched } =
    useFormikContext<INceaGpaFormValues>();

  if (step === 1) {
    // Section A: NCEA Levels
    return (
      <>
        <div className="mb-5xl">
          <Text tag="p" style="b1" className="text-center">
            {blok.description}
          </Text>
        </div>
        {/* Level 1 */}
        <div className="mb-4xl">
          <h4 className="mb-3 text-base font-medium text-primary01-75">
            {blok.levelLabel} 1
          </h4>
          <div className="grid grid-cols-4 gap-4">
            <IntScoreField
              level="level1"
              grade="N"
              label={blok.nonAchievedLabel}
            />
            <IntScoreField
              level="level1"
              grade="A"
              label={blok.achievedLabel}
            />
            <IntScoreField level="level1" grade="M" label={blok.meritLabel} />
            <IntScoreField
              level="level1"
              grade="E"
              label={blok.excellentLabel}
            />
          </div>
        </div>

        {/* Level 2 */}
        <div className="mb-4xl">
          <h4 className="mb-3 text-base font-medium text-primary01-75">
            {blok.levelLabel} 2
          </h4>
          <div className="grid grid-cols-4 gap-4">
            <IntScoreField
              level="level2"
              grade="N"
              label={blok.nonAchievedLabel}
            />
            <IntScoreField
              level="level2"
              grade="A"
              label={blok.achievedLabel}
            />
            <IntScoreField level="level2" grade="M" label={blok.meritLabel} />
            <IntScoreField
              level="level2"
              grade="E"
              label={blok.excellentLabel}
            />
          </div>
        </div>

        {/* Level 3 */}
        <div>
          <h4 className="mb-3 text-base font-medium text-primary01-75">
            {blok.levelLabel} 3
          </h4>
          <div className="grid grid-cols-4 gap-4">
            <IntScoreField
              level="level3"
              grade="N"
              label={blok.nonAchievedLabel}
            />
            <IntScoreField
              level="level3"
              grade="A"
              label={blok.achievedLabel}
            />
            <IntScoreField level="level3" grade="M" label={blok.meritLabel} />
            <IntScoreField
              level="level3"
              grade="E"
              label={blok.excellentLabel}
            />
          </div>
        </div>
      </>
    );
  } else {
    // Section B: Personal Information
    return (
      <>
        <Text
          tag="p"
          style="b1"
          className="mb-6 text-center text-base text-primary01-75"
        >
          {blok.contentTwoDescriptionLabel}
        </Text>

        <div
          className={cn(
            "grid gap-x-5 gap-y-10",
            deviceType === DeviceType.Desktop ? "grid-cols-2" : "grid-cols-1",
          )}
        >
          <TextField
            nameOfField="firstName"
            label={blok.firstNamePlaceholder}
            placeholder={blok.firstNamePlaceholder}
            theme="light"
            error={errors?.firstName}
            touched={touched?.firstName}
          />
          <TextField
            nameOfField="lastName"
            label={blok.lastNamePlaceholder}
            placeholder={blok.lastNamePlaceholder}
            theme="light"
            error={errors.lastName}
            touched={touched.lastName}
          />
          <TextField
            nameOfField="email"
            label={blok.emailPlaceholder}
            placeholder={blok.emailPlaceholder}
            theme="light"
            error={errors.email}
            touched={touched.email}
          />

          <CountryField
            label={blok.countryLabel}
            language="en"
            nameOfField="country"
            setFieldValue={setFieldValue as any}
            theme="light"
            placeholder={blok.countryLabel}
            error={errors.country}
            touched={touched.country}
          />

          <TextField
            nameOfField="schoolName"
            label={blok.schoolNamePlaceholder}
            placeholder={blok.schoolNamePlaceholder}
            theme="light"
            error={errors?.schoolName}
            touched={touched?.schoolName}
          />
          <TextField
            type="number"
            nameOfField="gradeYearLevel"
            label={blok.gradeYearLevelPlaceholder}
            placeholder={blok.gradeYearLevelPlaceholder}
            theme="light"
            error={errors?.gradeYearLevel}
            touched={touched?.gradeYearLevel}
          />
        </div>

        <div className="mt-4xl">
          <PhoneNumberField
            label={blok.phoneNumberLabel}
            nameOfField="phoneNumber"
            localNumberFieldName="phoneNumber"
            countryCodeFieldName="countryCode"
            localNumberPlaceholder={blok.phoneNumberLabel}
            countryCodePlaceholder="▾"
            theme="light"
            formId="nceaGpaForm"
            language="en"
            autofillCountryCodeByIP={true}
            errors={{
              countryCode: {
                value: errors.countryCode?.value ?? "",
                label: errors.countryCode?.label,
              },
              localNumber: errors.phoneNumber ?? "",
            }}
            touched={{
              countryCode: {
                value: touched.countryCode?.value ?? false,
                label: touched.countryCode?.label ?? false,
              },
              localNumber: touched.phoneNumber ?? false,
            }}
            values={{
              countryCode: values.countryCode,
              localNumber: values.phoneNumber,
            }}
            enableCountryLinking
          />
        </div>
      </>
    );
  }
};

export default TabletDesktopFormLayout;
