import * as Yup from "yup";
import {
  emailValidation,
  textValidation,
  localNumberValidation,
} from "@/components/ui/Forms/utils/validationRules";

const { number, object, string } = Yup;

const createScoreValidation = (
  isNumberErrorMessage = "Please enter a valid number",
) => {
  return number()
    .min(0, "Score cannot be negative")
    .typeError(isNumberErrorMessage);
};

const createCountryCodeObjectValidation = (
  hasCountryCodeErrorMessage: string,
) => {
  return object().shape({
    value: string().required(hasCountryCodeErrorMessage),
    label: string(),
  });
};

const createEmailValidation = (
  invalidEmailErrorLabel: string,
  requiredErrorLabel: string,
) => {
  const emailSchema = emailValidation(
    true,
    true,
    true,
    invalidEmailErrorLabel,
    requiredErrorLabel,
  );

  return (
    emailSchema ??
    string().email(invalidEmailErrorLabel).required(requiredErrorLabel)
  );
};

export const createMobileValidationSchemas = (
  requiredErrorLabel: string,
  invalidEmailErrorLabel: string,
  invalidPhoneErrorLabel = "Please enter a valid phone number",
  isNumberErrorMessage = "Please enter a valid number",
  hasCountryCodeErrorMessage = "Please select a country code",
) => [
  object({
    level1N: createScoreValidation(isNumberErrorMessage),
    level1A: createScoreValidation(isNumberErrorMessage),
    level1M: createScoreValidation(isNumberErrorMessage),
    level1E: createScoreValidation(isNumberErrorMessage),
  }),

  object({
    level2N: createScoreValidation(isNumberErrorMessage),
    level2A: createScoreValidation(isNumberErrorMessage),
    level2M: createScoreValidation(isNumberErrorMessage),
    level2E: createScoreValidation(isNumberErrorMessage),
  }),

  object({
    level3N: createScoreValidation(isNumberErrorMessage),
    level3A: createScoreValidation(isNumberErrorMessage),
    level3M: createScoreValidation(isNumberErrorMessage),
    level3E: createScoreValidation(isNumberErrorMessage),
  }),

  object({
    firstName: textValidation(true, requiredErrorLabel),
    lastName: textValidation(true, requiredErrorLabel),
    email: createEmailValidation(invalidEmailErrorLabel, requiredErrorLabel),
    country: textValidation(true, requiredErrorLabel),
    schoolName: textValidation(true, requiredErrorLabel),
    gradeYearLevel: textValidation(true, requiredErrorLabel),
    countryCode: createCountryCodeObjectValidation(hasCountryCodeErrorMessage),
    phoneNumber: localNumberValidation(
      true,
      invalidPhoneErrorLabel,
      requiredErrorLabel,
    ),
  }),
];

export const createTabletDesktopValidationSchemas = (
  requiredErrorLabel: string,
  invalidEmailErrorLabel: string,
  invalidPhoneErrorLabel = "Please enter a valid phone number",
  isNumberErrorMessage = "Please enter a valid number",
  hasCountryCodeErrorMessage = "Please select a country code",
) => [
  object({
    level1N: createScoreValidation(isNumberErrorMessage),
    level1A: createScoreValidation(isNumberErrorMessage),
    level1M: createScoreValidation(isNumberErrorMessage),
    level1E: createScoreValidation(isNumberErrorMessage),
    level2N: createScoreValidation(isNumberErrorMessage),
    level2A: createScoreValidation(isNumberErrorMessage),
    level2M: createScoreValidation(isNumberErrorMessage),
    level2E: createScoreValidation(isNumberErrorMessage),
    level3N: createScoreValidation(isNumberErrorMessage),
    level3A: createScoreValidation(isNumberErrorMessage),
    level3M: createScoreValidation(isNumberErrorMessage),
    level3E: createScoreValidation(isNumberErrorMessage),
  }),

  object({
    firstName: textValidation(true, requiredErrorLabel),
    lastName: textValidation(true, requiredErrorLabel),
    email: createEmailValidation(invalidEmailErrorLabel, requiredErrorLabel),
    country: textValidation(true, requiredErrorLabel),
    schoolName: textValidation(true, requiredErrorLabel),
    gradeYearLevel: textValidation(true, requiredErrorLabel),
    countryCode: createCountryCodeObjectValidation(hasCountryCodeErrorMessage),
    phoneNumber: localNumberValidation(
      true,
      invalidPhoneErrorLabel,
      requiredErrorLabel,
    ),
  }),
];
