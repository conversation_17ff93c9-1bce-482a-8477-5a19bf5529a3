"use client";
import { useState, useRef, useEffect } from "react";
import { Formik, Form, FormikHelpers } from "formik";

import { DeviceType } from "@/common/types";
import {
  createDesktopTabletUshValidationSchemas,
  createMobileUshValidationSchemas,
  createEmptyUhsDetail,
} from "./validationSchemas";
import { INFO_SECTION_SCROLL_POSITION } from "./constants";

import FormLayout from "./FormLayout";
import { handleFormSubmit } from "./formSubmitHandler";

import { useDeviceType } from "../useDeviceType";

import StepIndicator from "../StepIndicator";
import FormButtons from "../FormButtons";

import {
  UhsGpaCalculatorBlokProps,
  UhsGpaFormValues,
  IGradeType,
} from "./types";

const FORM_OFFSET_Y = 8;

const stagingMarketoProgrammeName = "OP-Script Testing";

const UhsGpaCalculatorForm = ({
  blok,
  dynamicContentId = "",
}: {
  blok: UhsGpaCalculatorBlokProps;
  dynamicContentId?: string;
}) => {
  const deviceType = useDeviceType();
  const [step, setStep] = useState(1);
  const [, setDirection] = useState<"forward" | "backward">("forward");
  const [animationComplete, setAnimationComplete] = useState(true);

  const isMobile = deviceType === DeviceType.Mobile;
  const maxSteps = isMobile ? 3 : 2;

  const initialValues: UhsGpaFormValues = {
    gradeType: IGradeType.letter,
    firstName: "",
    lastName: "",
    email: "",
    country: "",
    schoolName: "",
    gradeYearLevel: "",
    countryCode: { value: "", label: "" },
    phoneNumber: "",
    privacyPolicy: false,
    commsOptIn: false,
    ushDetails: [createEmptyUhsDetail(IGradeType.letter)],
  };

  const validationSchemas = isMobile
    ? createMobileUshValidationSchemas(
        blok.requiredErrorLabel || "This field is required",
        blok.invalidEmailErrorLabel || "Please enter a valid email address",
        blok.requiredErrorLabel || "Course name is required",
        blok.requiredErrorLabel || "Grade is required",
        blok.requiredErrorLabel || "Credits is required",
        blok.requiredErrorLabel || "Course type is required",
        blok.requiredErrorLabel || "Please enter a valid phone number",
        blok.requiredErrorLabel || "Please select a country code",
      )
    : createDesktopTabletUshValidationSchemas(
        blok.requiredErrorLabel || "This field is required",
        blok.invalidEmailErrorLabel || "Please enter a valid email address",
        blok.requiredErrorLabel || "Course name is required",
        blok.requiredErrorLabel || "Grade is required",
        blok.requiredErrorLabel || "Credits is required",
        blok.requiredErrorLabel || "Course type is required",
        blok.requiredErrorLabel || "Please enter a valid phone number",
        blok.requiredErrorLabel || "Please select a country code",
      );

  const isProd =
    process.env.NODE_ENV === "production" &&
    process.env.NEXT_PUBLIC_VERCEL_ENV === "production";

  const getCurrentValidationSchema = () => {
    return validationSchemas[step - 1];
  };

  const handleSubmit = async (
    values: UhsGpaFormValues,
    actions: FormikHelpers<UhsGpaFormValues>,
  ) => {
    if (step < maxSteps) {
      setAnimationComplete(false);
      setDirection("forward");
      setTimeout(() => {
        setStep(step + 1);
        void actions.setTouched({});
        actions.setSubmitting(false);
        // When a course section adds a relatively large number of courses, it causes the page scrollY to be relatively large. When clicking next to jump to the info section, you need to manually set the scrollY to give the user a better experience.
        window.scrollTo({
          top: INFO_SECTION_SCROLL_POSITION,
          left: 0,
          behavior: "smooth",
        });
        requestAnimationFrame(() => {
          setAnimationComplete(true);
        });
      }, 300);
    } else {
      const successPageLink = blok.successPageLink?.[0]?.link?.cached_url;
      const newTab = blok.successPageLink?.[0]?.newTab;
      const programName = isProd
        ? blok.marketoProgrammeName
        : stagingMarketoProgrammeName;

      await handleFormSubmit({
        values,
        actions,
        programName,
        successPageLink,
        newTab,
      });
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setAnimationComplete(false);
      setDirection("backward");
      setTimeout(() => {
        setStep(Math.max(1, step - 1));
        requestAnimationFrame(() => {
          setAnimationComplete(true);
        });
      }, 300);
    }
  };

  const formRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isMobile && formRef.current && dynamicContentId) {
      const dynamicMainContent = document.getElementById(dynamicContentId);
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const formHeight = entry.contentRect.height;
          const calculatedPadding = formHeight - FORM_OFFSET_Y;
          const dynamicPaddingTop = Math.max(0, calculatedPadding);
          if (dynamicMainContent) {
            dynamicMainContent.style.paddingTop = `${dynamicPaddingTop}px`;
          }
        }
      });
      resizeObserver.observe(formRef.current);
      const initialHeight = formRef.current.offsetHeight;
      const calculatedPadding = initialHeight - FORM_OFFSET_Y;
      const dynamicPaddingTop = Math.max(0, calculatedPadding);
      if (dynamicMainContent) {
        dynamicMainContent.style.paddingTop = `${dynamicPaddingTop}px`;
      }
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [isMobile, dynamicContentId]);

  return (
    <div
      ref={formRef}
      className="rounded-[12px] bg-white px-[1.125rem] py-6 shadow-[0px_0px_21.83px_0px_rgba(86,65,46,0.12)] md:p-[1.875rem] lg:p-5xl"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={getCurrentValidationSchema()}
        onSubmit={handleSubmit}
        validateOnChange
        validateOnBlur
        enableReinitialize
      >
        {({ isSubmitting, errors, ...formikProps }) => {
          console.log("Formik errors:", errors);
          return (
            <Form noValidate>
              <div className="">
                <FormLayout step={step} deviceType={deviceType} blok={blok} />
              </div>

              <div className="mt-5xl">
                <FormButtons
                  step={step}
                  mobileMaxStep={3}
                  tabletMaxStep={2}
                  desktopMaxStep={2}
                  deviceType={deviceType}
                  isSubmitting={isSubmitting}
                  animationComplete={animationComplete}
                  handleBack={handleBack}
                  formikProps={formikProps}
                  blok={blok}
                />
              </div>

              <div className="mt-4xl">
                <StepIndicator currentStep={step} totalSteps={maxSteps} />
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default UhsGpaCalculatorForm;
