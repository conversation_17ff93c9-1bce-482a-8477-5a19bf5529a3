import {
  getCookie,
  gtmEvents,
  triggerCustomEvent,
  trackFacebookEvent,
  fbEvents,
} from "@/common/analytics";

import { UhsGpaFormValues, IGradeType } from "./types";

/**
 * Converts a letter grade into an unweighted GPA.
 *
 * @param {string} grade - letter grade.
 * @return {number} - US unweighted GPA for one subject.
 */
const convertLetterGradeToPoints = (grade: string): number => {
  if (grade === "A+") {
    return 4;
  } else if (grade === "A") {
    return 4;
  } else if (grade === "A-") {
    return 3.7;
  } else if (grade === "B+") {
    return 3.3;
  } else if (grade === "B") {
    return 3;
  } else if (grade === "B-") {
    return 2.7;
  } else if (grade === "C+") {
    return 2.3;
  } else if (grade === "C") {
    return 2;
  } else if (grade === "C-") {
    return 1.7;
  } else if (grade === "D+") {
    return 1.3;
  } else if (grade === "D") {
    return 1;
  } else {
    return 0;
  }
};

/**
 * Converts a percentage grade into an unweighted GPA.
 *
 * @param {number} grade - percentage grade from 0 to 100.
 * @return {number} - US unweighted GPA for one subject.
 */
const convertPercentGradeToPoints = (grade: number): number => {
  if (grade >= 97) {
    return 4;
  } else if (grade >= 93) {
    return 4;
  } else if (grade >= 90) {
    return 3.7;
  } else if (grade >= 87) {
    return 3.3;
  } else if (grade >= 83) {
    return 3;
  } else if (grade >= 80) {
    return 2.7;
  } else if (grade >= 77) {
    return 2.3;
  } else if (grade >= 73) {
    return 2;
  } else if (grade >= 70) {
    return 1.7;
  } else if (grade >= 67) {
    return 1.3;
  } else if (grade >= 65) {
    return 1;
  } else {
    return 0;
  }
};

/**
 * Determines the points added to a course for weighted GPA.
 *
 * @param {string} courseType
 * @return {number} - Points added to the course
 */
const weighting = (courseType: string): number => {
  if (
    courseType === "IB HL" ||
    courseType === "AP" ||
    courseType === "College Level"
  ) {
    return 1;
  } else if (
    courseType === "Honors" ||
    courseType === "IB SL" ||
    courseType === "Dual Enrollment"
  ) {
    return 0.5;
  } else if (courseType === "Regular") {
    return 0;
  } else {
    return 0;
  }
};

interface NormalizedSubject {
  subjectName: string;
  grade: string | number;
  credits: number;
  courseType: string;
}

interface GpaResult {
  unweightedGpa: number;
  weightedGpa: number;
}

/**
 * Standardize the USH transcript object.
 * @param {UhsGpaFormValues} formData - USH form data
 * @return {NormalizedSubject[]} - Normalized USH transcript
 */
const normalizeUshTranscript = (
  formData: UhsGpaFormValues,
): NormalizedSubject[] => {
  const normalizedTranscript: NormalizedSubject[] = [];
  const { gradeType, ushDetails } = formData;

  for (const item of ushDetails) {
    const normalizedSubject = {
      subjectName: item.course,
      grade:
        gradeType === IGradeType.letter
          ? item.letterGrade
          : item.percentageGrade,
      credits: parseInt(item.credits as string, 10) || 0,
      courseType: item.courseType,
    };
    normalizedTranscript.push(normalizedSubject);
  }

  return normalizedTranscript;
};

/**
 * Converts a US high school transcript grades into an unweighted GPA and weighted GPA.
 *
 * @param {UhsGpaFormValues} formData - Form data containing transcript and grade type
 * @return {GpaResult} - Object with unweighted and weighted GPA
 */
const convertUshToGpa = (formData: UhsGpaFormValues): GpaResult => {
  const transcript = normalizeUshTranscript(formData);
  const gradeType = formData.gradeType;

  if (transcript.length === 0) {
    return {
      unweightedGpa: 0,
      weightedGpa: 0,
    };
  }

  let creditsCounted = 0;
  let totalUnweightedScore = 0;
  let totalWeightedScore = 0;

  for (const subject of transcript) {
    let unweightedScore = 0;

    if (gradeType === IGradeType.letter) {
      // 4 4
      unweightedScore = convertLetterGradeToPoints(subject.grade as string);
    } else if (gradeType === IGradeType.percentage) {
      unweightedScore = convertPercentGradeToPoints(subject.grade as number);
    }

    // 4 + 0
    // 4 + 0.5
    const weightedScore = unweightedScore + weighting(subject.courseType);

    creditsCounted += subject.credits;
    // 4  * 1 + 4 * 2
    totalUnweightedScore += unweightedScore * subject.credits;
    // 4 * 1 + 4.5 * 2
    totalWeightedScore += weightedScore * subject.credits;
  }

  if (creditsCounted === 0) {
    return {
      unweightedGpa: 0,
      weightedGpa: 0,
    };
  }

  console.log(`WJM--计算出来的总分`, {
    totalUnweightedScore,
    totalWeightedScore,
    creditsCounted,
    unweightedGpa: totalUnweightedScore / creditsCounted,
    weightedGpa: totalWeightedScore / creditsCounted,
  });

  return {
    unweightedGpa: totalUnweightedScore / creditsCounted,
    weightedGpa: totalWeightedScore / creditsCounted,
  };
};

/**
 * Calculate GPA and submit form to Marketo
 * @param {UhsGpaFormValues} formValues - Form values
 * @param {string} marketoProgrammeName - Marketo program name
 * @returns {Promise<GpaResult>} - Calculated GPA result
 */
export const submitUhsGpaForm = async (
  formValues: UhsGpaFormValues,
  marketoProgrammeName: string,
): Promise<{
  unweightedGpa: string;
  weightedGpa: string;
}> => {
  const { unweightedGpa, weightedGpa } = convertUshToGpa(formValues);

  console.log(`WJM--计算出来带小数`, {
    unweightedGpa,
    weightedGpa,
  });

  const parsedScore = {
    unweightedGpa: unweightedGpa.toFixed(2),
    weightedGpa: weightedGpa.toFixed(2),
  };

  await submitMarketoForm(marketoProgrammeName, formValues, parsedScore);

  return parsedScore;
};

/**
 * Send form data to marketo
 * @param {string} marketoProgramName - Marketo program name
 * @param {UhsGpaFormValues} inputs - Form inputs
 * @param {number} gpaScore - Calculated GPA score
 */
export const submitMarketoForm = async (
  marketoProgramName: string,
  inputs: UhsGpaFormValues,
  gpaScore: {
    weightedGpa: string;
    unweightedGpa: string;
  },
): Promise<any> => {
  const {
    firstName,
    lastName,
    email,
    commsOptIn,
    country,
    privacyPolicy,
    schoolName,
    gradeYearLevel,
    phoneNumber,
    countryCode,
  } = inputs;

  const fullPhoneNumber =
    phoneNumber && countryCode?.value
      ? `${countryCode.value}${phoneNumber}`
      : phoneNumber || "+211111111";

  const marketoBodyObject = {
    munchkinId: getCookie("_mkto_trk"),
    marketoLeadBody: {
      programName: marketoProgramName,
      source: "Crimson Website",
      reason: "Filled out US High School GPA Calculator Submission",
      lookupField: "email",
      input: [
        {
          firstName: firstName,
          lastName: lastName,
          email: email,
          MKT_UTM_Campaign__c: getCookie("x-crimson-utm_campaign"),
          MKT_UTM_Medium__c:
            getCookie("x-crimson-utm_medium") ?? "organic_search",
          MKT_UTM_Source__c: getCookie("x-crimson-utm_source"),
          MKT_UTM_Term__c: getCookie("x-crimson-utm_term"),
          MKT_UTM_Content__c: getCookie("x-crimson-utm_content"),
          fbclid__c: getCookie("x-crimson-fbclid"),
          gclid__c: getCookie("x-crimson-gclid"),
          School__c: schoolName,
          School_Year_Grade_Level__c: gradeYearLevel,
          country: country,
          weightedGPACalculator: gpaScore.weightedGpa || 0,
          unweightedGPACalculator: gpaScore.unweightedGpa || 0,
          phone: fullPhoneNumber,
          privacyPolicy: privacyPolicy,
          unsubscribed: !commsOptIn,
        },
      ],
    },
  };

  try {
    const response = await fetch("/api/calculators", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(marketoBodyObject),
    });

    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }

    if (typeof window !== "undefined") {
      if (
        (
          window as {
            solve?: { identify?: (data: any) => void };
          }
        )?.solve?.identify
      ) {
        (
          window as {
            solve?: { identify?: (data: any) => void };
          }
        )?.solve?.identify?.({
          email: email,
          attributes: {
            last_form_touch: "ush_gpa",
          },
          tags: ["Student"],
        });
      }
      triggerCustomEvent(
        gtmEvents.USH_GPA_FORM_SUBMISSION,
        "ush-gpa-calculator-submit",
      );
      // Facebook tracking
      trackFacebookEvent(fbEvents.LEAD);
    }

    return await response.json();
  } catch (error) {
    console.error("Error submitting to Marketo:", error);
    throw error;
  }
};

export const USH_GPA_RESULT_KEY = "ushGpaResult";

export {
  convertUshToGpa,
  normalizeUshTranscript,
  convertLetterGradeToPoints,
  convertPercentGradeToPoints,
  weighting,
};

export type { GpaResult, NormalizedSubject };
