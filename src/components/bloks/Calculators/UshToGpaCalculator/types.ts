import { SectionItem, StoryData } from "@/common/types";

export interface UhsGpaCalculatorBlokProps {
  pageTitle: string;
  subheader: string;
  subtitle: string;
  heading: string;
  subheading: string;
  aLevelHeading: string;
  aLevelSubheading: string;
  subjectPlaceholder: string;
  IBHeading: string;
  IBSubheading: string;
  gradePlaceholder: string;

  letterGradeLabel: string;
  percentageGradeLabel: string;

  privacyPolicyLabel: string;
  studyPathwaysLabel: string;

  privacyPolicyPlaceholder: string;
  termConditionsPlaceholder: string;

  formSubheading: string;
  description: string;

  gradeFormatLabel: string;
  courseLabel: string;
  addRowLabel: string;
  subjectLabel: string;
  gradeLabel: string;
  addSubjectLabel: string;
  nextButtonLabel: string;
  previousButtonLabel: string;
  submitButtonLabel: string;
  emailPlaceholder: string;
  phoneNumberLabel: string;
  requiredErrorLabel: string;
  requiredLabel: string;
  lastNamePlaceholder: string;
  firstNamePlaceholder: string;
  schoolNamePlaceholder: string;
  invalidEmailErrorLabel: string;

  contentTwoMainHeading: string;
  contentTwoSubHeadingLabel: string;
  gradeYearLevelPlaceholder: string;
  contentTwoDescriptionLabel: string;
  countryLabel: string;
  successPageLink: {
    link: {
      id: string;
      url: string;
      linkType: string;
      cached_url: string;
    };
    component: string;
    newTab: boolean;
  }[];
  topSections?: {
    heading: string;
    subheading: string;
  }[];
  sections: SectionItem[];
  bottomSections: SectionItem[];
  marketoProgrammeName: string;
}

export interface UhsGpaCalculatorPageProps {
  locale: string;
  slug: string;
  uuid: string;
  story: StoryData["story"];
  blok: UhsGpaCalculatorBlokProps;
}

export type IGradeMap = Record<string, number>;

export enum IGradeType {
  letter = "letter",
  percentage = "percentage",
}

export interface UhsDetail {
  course: string;
  credits: number | string;
  courseType: string;
  letterGrade: string;
  percentageGrade: number | string;
  gradeType: IGradeType;
}

export interface UhsGpaFormValues {
  // step 1
  gradeType: IGradeType;
  // step 2
  ushDetails: UhsDetail[];
  // step 3
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  schoolName: string;
  gradeYearLevel: string;
  country: string;
  countryCode: {
    value: string;
    label: string;
  };
  privacyPolicy: boolean;
  commsOptIn: boolean;
}

export interface UhsGpaFormErrors {
  gradeType?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  schoolName?: string;
  gradeYearLevel?: string;
  privacyPolicy?: string;
  commsOptIn?: string;
  localNumber?: string;
  country?: string;
  countryCode?: {
    value?: string;
    label?: string;
  };
  ushDetails?: {
    course?: string;
    credits?: string;
    courseType?: string;
    letterGrade?: string;
    percentageGrade?: string;
  }[];
}

export interface UhsGpaFormTouched {
  gradeType?: boolean;
  firstName?: boolean;
  lastName?: boolean;
  email?: boolean;
  phoneNumber?: boolean;
  schoolName?: boolean;
  gradeYearLevel?: boolean;
  privacyPolicy?: boolean;
  commsOptIn?: boolean;
  localNumber?: boolean;
  country?: boolean;
  countryCode?: {
    value?: boolean;
    label?: boolean;
  };
  ushDetails?: {
    course?: boolean;
    credits?: boolean;
    courseType?: boolean;
    letterGrade?: boolean;
    percentageGrade?: boolean;
  }[];
}
