import React, { useEffect } from "react";

import { AddSubjectIcon, RemoveSubjectIcon } from "@/components/icons/Subjects";
import { cn } from "@/common/utils";

import { DeviceType } from "@/common/types";
import Text from "@/components/ui/Text";
import CountryField from "@/components/ui/FormInputFields/CountryField";
import PhoneNumberField from "@/components/ui/FormInputFields/PhoneNumberField";
import TextField from "@/components/ui/FormInputFields/TextField";
import SelectField from "@/components/ui/FormInputFields/SelectField";
import CheckboxField from "@/components/ui/FormInputFields/CheckboxField";
import RectRadioField from "@/components/ui/FormInputFields/RectRadioField";
import { useFormikContext, getIn } from "formik";

import { createEmptyUhsDetail } from "./validationSchemas";

import {
  UhsGpaCalculatorBlokProps,
  UhsGpaFormValues,
  IGradeType,
} from "./types";

import { GRADE_CHOICES, COURSE_CHOICES } from "./constants";

interface FormLayoutProps {
  step: number;
  blok: UhsGpaCalculatorBlokProps;
  deviceType: DeviceType;
}

const FormLayout: React.FC<FormLayoutProps> = ({ step, blok, deviceType }) => {
  const { values, setFieldValue, errors, touched, setFieldError } =
    useFormikContext<UhsGpaFormValues>();

  const isMobile = deviceType === DeviceType.Mobile;

  const addCourse = async () => {
    const newCourse = createEmptyUhsDetail(values.gradeType);
    await setFieldValue("ushDetails", [...values.ushDetails, newCourse]);
  };

  const removeCourse = async (index: number) => {
    const updatedCourses = values.ushDetails.filter((_, i) => i !== index);
    await setFieldValue("ushDetails", updatedCourses);
  };

  const getGradeOptions = () => {
    if (values.gradeType === IGradeType.letter) {
      return GRADE_CHOICES;
    }

    return [];
  };

  useEffect(() => {
    if (values.gradeType && values.ushDetails.length > 0) {
      const updatedDetails = values.ushDetails.map((detail) => ({
        ...detail,
        gradeType: values.gradeType,

        letterGrade:
          values.gradeType === IGradeType.letter ? detail.letterGrade : "",
        percentageGrade:
          values.gradeType === IGradeType.percentage
            ? detail.percentageGrade
            : "",
      }));

      void setFieldValue("ushDetails", updatedDetails);
    }
  }, [values.gradeType]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const shouldCheckDuplicates = isMobile ? step === 2 : step === 1;

    if (shouldCheckDuplicates && values.ushDetails.length > 0) {
      const courseNames = values.ushDetails
        .map((detail) => detail.course)
        .filter((course) => course.trim() !== "");

      values.ushDetails.forEach((_, index) => {
        const currentError = getIn(errors, `ushDetails.${index}.course`);
        if (currentError === "Duplicate courses are not allowed") {
          setFieldError(`ushDetails.${index}.course`, undefined);
        }
      });

      const duplicates = new Set<string>();
      const duplicateIndexes = new Set<number>();

      courseNames.forEach((course, index) => {
        const firstIndex = courseNames.indexOf(course);
        if (firstIndex !== index) {
          duplicates.add(course);
          duplicateIndexes.add(index);
          duplicateIndexes.add(firstIndex);
        }
      });

      duplicateIndexes.forEach((index) => {
        setFieldError(
          `ushDetails.${index}.course`,
          "Duplicate courses are not allowed",
        );
      });
    }
  }, [values.ushDetails, step, setFieldError, errors, isMobile]);

  const renderGradeTypeSelection = () => (
    <>
      <div className="mb-5xl text-center">
        <Text tag="p" style="b1" className="text-black">
          {blok.description}
        </Text>
      </div>

      <div className="mb-3 text-left">
        <Text tag="p" style="b2" className="text-black">
          {blok.gradeFormatLabel}
        </Text>
      </div>
      <div className="flex flex-col justify-center gap-4 sm:flex-row sm:gap-4">
        <RectRadioField
          name="gradeType"
          value={IGradeType.letter}
          label={blok.letterGradeLabel}
          theme="light"
          className="flex-1"
        />
        <RectRadioField
          name="gradeType"
          value={IGradeType.percentage}
          label={blok.percentageGradeLabel}
          theme="light"
          className="flex-1"
        />
      </div>
    </>
  );

  const renderCourseInput = () => {
    const isLetterGrade = values.gradeType === IGradeType.letter;
    const maxCourses = 8;
    return (
      <>
        {values.ushDetails.map((detail, index) => (
          <div
            key={index}
            className={cn(
              "bg-gray-50 relative rounded-lg",
              index === 0 ? "" : "mt-[1.5rem] sm:mt-[2.5rem]",
            )}
          >
            {/* Desktop/Tablet layout */}
            <div className="relative hidden sm:flex sm:items-start sm:justify-between sm:gap-4">
              <div className="flex-1">
                <TextField
                  nameOfField={`ushDetails.${index}.course`}
                  label={`${blok.courseLabel}`}
                  placeholder={blok.subjectPlaceholder}
                  theme="light"
                  error={getIn(errors, `ushDetails.${index}.course`)}
                  touched={getIn(touched, `ushDetails.${index}.course`)}
                />
              </div>

              <div className="flex-1">
                {isLetterGrade ? (
                  <SelectField
                    nameOfField={`ushDetails.${index}.letterGrade`}
                    label={blok.gradeLabel}
                    placeholder={blok.gradePlaceholder}
                    options={getGradeOptions()}
                    theme="light"
                    error={getIn(errors, `ushDetails.${index}.letterGrade`)}
                    touched={getIn(touched, `ushDetails.${index}.letterGrade`)}
                  />
                ) : (
                  <TextField
                    nameOfField={`ushDetails.${index}.percentageGrade`}
                    label={blok.gradeLabel}
                    placeholder="85"
                    type="number"
                    theme="light"
                    error={getIn(errors, `ushDetails.${index}.percentageGrade`)}
                    touched={getIn(
                      touched,
                      `ushDetails.${index}.percentageGrade`,
                    )}
                  />
                )}
              </div>

              <div className="w-28">
                <TextField
                  nameOfField={`ushDetails.${index}.credits`}
                  label="Credits"
                  placeholder="0"
                  type="number"
                  theme="light"
                  error={getIn(errors, `ushDetails.${index}.credits`)}
                  touched={getIn(touched, `ushDetails.${index}.credits`)}
                />
              </div>

              <div className="flex-1">
                <SelectField
                  nameOfField={`ushDetails.${index}.courseType`}
                  label="Course Type"
                  placeholder="Select type"
                  options={COURSE_CHOICES}
                  theme="light"
                  error={getIn(errors, `ushDetails.${index}.courseType`)}
                  touched={getIn(touched, `ushDetails.${index}.courseType`)}
                />
              </div>

              <div className="shrink-0 opacity-0">
                <RemoveSubjectIcon className="opacity-0" />
              </div>

              <button
                type="button"
                onClick={() => removeCourse(index)}
                disabled={values.ushDetails.length <= 1}
                className="disabled:text-gray-400 absolute right-0 top-[68px] rounded-md transition-colors enabled:text-red-500 enabled:hover:bg-red-50 enabled:hover:text-red-700 disabled:cursor-not-allowed"
              >
                <RemoveSubjectIcon />
              </button>
            </div>

            {/* Mobile layout */}
            <div className="rounded-[8px] bg-neutral01-0 px-[12px] py-[18px] sm:hidden">
              <button
                type="button"
                onClick={() => removeCourse(index)}
                disabled={values.ushDetails.length <= 1}
                className="disabled:text-gray-400 absolute right-0 top-0 z-10 rounded-md p-2 transition-colors enabled:text-red-500 enabled:hover:bg-red-50 enabled:hover:text-red-700 disabled:cursor-not-allowed"
              >
                <RemoveSubjectIcon />
              </button>

              <div className="mb-4">
                <TextField
                  nameOfField={`ushDetails.${index}.course`}
                  label={`${blok.courseLabel}`}
                  placeholder={blok.subjectPlaceholder}
                  theme="light"
                  error={getIn(errors, `ushDetails.${index}.course`)}
                  touched={getIn(touched, `ushDetails.${index}.course`)}
                />
              </div>
              <div className="mb-4">
                <TextField
                  nameOfField={`ushDetails.${index}.credits`}
                  label="Credits"
                  placeholder="0"
                  type="number"
                  theme="light"
                  error={getIn(errors, `ushDetails.${index}.credits`)}
                  touched={getIn(touched, `ushDetails.${index}.credits`)}
                />
              </div>

              <div className="mb-4">
                {isLetterGrade ? (
                  <SelectField
                    nameOfField={`ushDetails.${index}.letterGrade`}
                    label={blok.gradeLabel}
                    placeholder={blok.gradePlaceholder}
                    options={getGradeOptions()}
                    theme="light"
                    error={getIn(errors, `ushDetails.${index}.letterGrade`)}
                    touched={getIn(touched, `ushDetails.${index}.letterGrade`)}
                  />
                ) : (
                  <TextField
                    nameOfField={`ushDetails.${index}.percentageGrade`}
                    label={`${blok.gradeLabel} (%)`}
                    placeholder="85"
                    type="number"
                    theme="light"
                    error={getIn(errors, `ushDetails.${index}.percentageGrade`)}
                    touched={getIn(
                      touched,
                      `ushDetails.${index}.percentageGrade`,
                    )}
                  />
                )}
              </div>

              <div>
                <SelectField
                  nameOfField={`ushDetails.${index}.courseType`}
                  label="Course Type"
                  placeholder="Select type"
                  options={COURSE_CHOICES}
                  theme="light"
                  error={getIn(errors, `ushDetails.${index}.courseType`)}
                  touched={getIn(touched, `ushDetails.${index}.courseType`)}
                />
              </div>
            </div>
          </div>
        ))}

        {values.ushDetails.length < maxCourses && (
          <button
            type="button"
            onClick={addCourse}
            className="mt-[24px] flex w-fit items-center justify-start gap-2"
          >
            <AddSubjectIcon className="size-5" />
            <Text tag="p" style="b1" className="!text-[16px] text-black">
              {blok.addRowLabel}
            </Text>
          </button>
        )}
      </>
    );
  };

  const renderPersonalInfo = () => (
    <>
      <div className="mb-[50px] text-center">
        <Text tag="p" style="b1" className="text-primary01-75">
          {blok.contentTwoDescriptionLabel}
        </Text>
      </div>

      <div
        className={`grid gap-x-5 gap-y-10 ${
          deviceType === DeviceType.Desktop ? "grid-cols-2" : "grid-cols-1"
        } `}
      >
        <TextField
          nameOfField="firstName"
          label={blok.firstNamePlaceholder}
          placeholder={blok.firstNamePlaceholder}
          theme="light"
          error={errors?.firstName}
          touched={touched?.firstName}
        />
        <TextField
          nameOfField="lastName"
          label={blok.lastNamePlaceholder}
          placeholder={blok.lastNamePlaceholder}
          theme="light"
          error={errors.lastName}
          touched={touched.lastName}
        />
        <TextField
          nameOfField="email"
          label={blok.emailPlaceholder}
          placeholder={blok.emailPlaceholder}
          theme="light"
          error={errors.email}
          touched={touched.email}
        />

        <CountryField
          label={blok.countryLabel ?? "Country"}
          language="en"
          nameOfField="country"
          setFieldValue={setFieldValue as any}
          theme="light"
          placeholder={blok.countryLabel ?? "Select your country"}
          error={errors.country}
          touched={touched.country}
        />

        <TextField
          nameOfField="schoolName"
          label={blok.schoolNamePlaceholder}
          placeholder={blok.schoolNamePlaceholder}
          theme="light"
          error={errors?.schoolName}
          touched={touched?.schoolName}
        />
        <TextField
          type="number"
          nameOfField="gradeYearLevel"
          label={blok.gradeYearLevelPlaceholder ?? "Grade"}
          placeholder={blok.gradeYearLevelPlaceholder}
          theme="light"
          error={errors?.gradeYearLevel}
          touched={touched?.gradeYearLevel}
        />
      </div>

      <div className="mt-[40px]">
        <PhoneNumberField
          label={blok.phoneNumberLabel}
          nameOfField="phoneNumber"
          localNumberFieldName="phoneNumber"
          countryCodeFieldName="countryCode"
          localNumberPlaceholder={blok.phoneNumberLabel}
          countryCodePlaceholder="▾"
          theme="light"
          formId="ushGpaForm"
          language="en"
          autofillCountryCodeByIP={true}
          errors={{
            countryCode: {
              value: errors.countryCode?.value ?? "",
              label: errors.countryCode?.label,
            },
            localNumber: errors.phoneNumber ?? "",
          }}
          touched={{
            countryCode: {
              value: touched.countryCode?.value ?? false,
              label: touched.countryCode?.label ?? false,
            },
            localNumber: touched.phoneNumber ?? false,
          }}
          values={{
            countryCode: values.countryCode,
            localNumber: values.phoneNumber,
          }}
          enableCountryLinking
        />
      </div>

      <div className="mt-[40px]">
        <CheckboxField
          nameOfField="privacyPolicy"
          label={blok.privacyPolicyPlaceholder}
          theme="light"
          error={errors.privacyPolicy}
          touched={touched.privacyPolicy}
          value={values?.privacyPolicy}
          formId="ushGpaForm"
          renderMarkdown
        />
      </div>
      <div className="mt-[32px]">
        <CheckboxField
          nameOfField="commsOptIn"
          label={blok.termConditionsPlaceholder}
          theme="light"
          error={errors.commsOptIn}
          touched={touched.commsOptIn}
          value={values?.commsOptIn}
          formId="ushGpaForm"
        />
      </div>
    </>
  );

  if (isMobile) {
    switch (step) {
      case 1:
        return renderGradeTypeSelection();
      case 2:
        return renderCourseInput();
      case 3:
        return renderPersonalInfo();
      default:
        return null;
    }
  } else {
    switch (step) {
      case 1:
        return (
          <>
            {renderGradeTypeSelection()}
            <div className="mt-[1.5625rem]">{renderCourseInput()}</div>
          </>
        );
      case 2:
        return renderPersonalInfo();
      default:
        return null;
    }
  }
};

export default FormLayout;
