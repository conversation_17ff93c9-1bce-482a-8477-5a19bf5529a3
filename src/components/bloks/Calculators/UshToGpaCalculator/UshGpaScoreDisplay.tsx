"use client";
import React, { useState, useEffect } from "react";
import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import GpaScoreDisplay from "@/components/bloks/Calculators/GpaScoreDisplay";
import { USH_GPA_RESULT_KEY } from "./util";

interface UhsScoreResult {
  gpaScore: {
    unweightedGpa: string;
    weightedGpa: string;
  };
}

const UhsGpaScoreDisplay: React.FC<{
  unweightedGpaDescription?: string;
  weightedGpaDescription?: string;
  descriptionLabel?: string;
}> = ({
  unweightedGpaDescription,
  weightedGpaDescription,
  descriptionLabel,
}) => {
  const [scoreResult, setScoreResult] = useState<UhsScoreResult | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedData = sessionStorage.getItem(USH_GPA_RESULT_KEY);
      if (storedData) {
        try {
          const result = JSON.parse(storedData) as UhsScoreResult;
          setScoreResult(result);
        } catch (error) {
          console.warn(
            `Failed to parse score result for key "${USH_GPA_RESULT_KEY}":`,
            error,
          );
          setScoreResult({
            gpaScore: {
              unweightedGpa: "0.00",
              weightedGpa: "0.00",
            },
          });
        }
      } else {
        setScoreResult({
          gpaScore: {
            unweightedGpa: "0.00",
            weightedGpa: "0.00",
          },
        });
      }
    }
  }, []);

  return (
    <div className="z-8 relative flex flex-col items-center pt-[119px] md:pt-[125px] lg:pt-[138px]">
      <div
        className={cn(
          "flex items-center gap-6 md:gap-8 lg:gap-10",
          "flex-col md:flex-col lg:flex-row",
        )}
      >
        <div>
          <Text
            tag="h1"
            style="sh3"
            mdStyle="sh2"
            lgStyle="sh2"
            className="w-[20.375rem] text-center text-primary01-75 md:w-[39.625rem] lg:w-[39.75rem]"
          >
            {unweightedGpaDescription ?? "Unweighted GPA"}
          </Text>

          <div
            className={`relative mx-auto my-[20px] flex h-[105px] w-[269px] items-center justify-center rounded-[4px] transition-all duration-700 md:my-[20px] md:h-[102px] md:w-[402px] lg:my-[25px] lg:h-[140px] lg:w-[442px]`}
          >
            <div className="absolute inset-0 rounded-[4px] bg-gradient-to-br from-primary01-75 via-primary01-25 to-primary01-50 p-[2px]">
              <div className="size-full rounded-[2px] bg-neutral01-25">
                <GpaScoreDisplay
                  gpaScoreSessionKey={USH_GPA_RESULT_KEY}
                  gpaScoreString={scoreResult?.gpaScore?.unweightedGpa}
                />
              </div>
            </div>
          </div>
        </div>

        <div>
          <Text
            tag="h1"
            style="sh3"
            mdStyle="sh2"
            lgStyle="sh2"
            className="w-[20.375rem] text-center text-primary01-75 md:w-[39.625rem] lg:w-[39.75rem]"
          >
            {weightedGpaDescription ?? "Weighted GPA"}
          </Text>

          <div
            className={`relative mx-auto my-[20px] flex h-[105px] w-[269px] items-center justify-center rounded-[4px] transition-all duration-700 md:my-[20px] md:h-[102px] md:w-[402px] lg:my-[25px] lg:h-[140px] lg:w-[442px]`}
          >
            <div className="absolute inset-0 rounded-[4px] bg-gradient-to-br from-primary01-75 via-primary01-25 to-primary01-50 p-[2px]">
              <div className="size-full rounded-[2px] bg-neutral01-25">
                <GpaScoreDisplay
                  gpaScoreSessionKey={USH_GPA_RESULT_KEY}
                  gpaScoreString={scoreResult?.gpaScore?.weightedGpa}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Text
        tag="p"
        style="sh4"
        className="w-80 text-center !text-[1.75rem] text-primary01-75 md:w-[39.625rem] lg:w-[51.625rem]"
      >
        {descriptionLabel ?? `We sent your result via email`}
      </Text>
    </div>
  );
};

export default UhsGpaScoreDisplay;
