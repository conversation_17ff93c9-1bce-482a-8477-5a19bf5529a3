import * as Yup from "yup";
import {
  emailValidation,
  textValidation,
  localNumberValidation,
} from "@/components/ui/Forms/utils/validationRules";

import { UhsDetail, IGradeType } from "./types";

const { array, object, string, boolean, number } = Yup;

const createCountryCodeObjectValidation = (
  hasCountryCodeErrorMessage: string,
) => {
  return object().shape({
    value: string().required(hasCountryCodeErrorMessage),
    label: string(),
  });
};

const createEmailValidation = (
  invalidEmailErrorLabel: string,
  requiredErrorLabel: string,
) => {
  const emailSchema = emailValidation(
    true,
    true,
    true,
    invalidEmailErrorLabel,
    requiredErrorLabel,
  );

  return (
    emailSchema ??
    string().email(invalidEmailErrorLabel).required(requiredErrorLabel)
  );
};

export const createUhsDetailsValidation = (
  courseRequiredErrorLabel: string,
  gradeRequiredErrorLabel: string,
  creditsRequiredErrorLabel: string,
  courseTypeRequiredErrorLabel: string,
) => {
  return array()
    .of(
      object().shape({
        course: string().required(courseRequiredErrorLabel),
        credits: number()
          .positive("Credits must be a positive number")
          .required(creditsRequiredErrorLabel),
        courseType: string().required(courseTypeRequiredErrorLabel),
        letterGrade: string().when("gradeType", {
          is: IGradeType.letter,
          then: (schema) => schema.required(gradeRequiredErrorLabel),
          otherwise: (schema) => schema.notRequired(),
        }),
        percentageGrade: string().when("gradeType", {
          is: IGradeType.percentage,
          then: (schema) =>
            schema
              .required(gradeRequiredErrorLabel)
              .test(
                "is-valid-percentage",
                "Percentage must be between 0 and 100",
                (value) => {
                  if (!value) return false;
                  const num = parseFloat(value);
                  return !isNaN(num) && num >= 0 && num <= 100;
                },
              ),
          otherwise: (schema) => schema.notRequired(),
        }),
        gradeType: string()
          .oneOf(
            [IGradeType.letter, IGradeType.percentage],
            "Invalid grade type",
          )
          .required("Grade type is required"),
      }),
    )
    .min(1, "At least one course is required")
    .test(
      "unique-courses",
      "Duplicate courses are not allowed",
      function (value) {
        if (!value || !Array.isArray(value)) return true;

        const courses = value
          .filter(
            (item) => item && typeof item === "object" && "course" in item,
          )
          .map((item) => item.course)
          .filter((course) => course && course.trim() !== "");

        const uniqueCourses = new Set(courses);
        return courses.length === uniqueCourses.size;
      },
    )
    .test("complete-courses", "All courses must be complete", function (value) {
      if (!value || !Array.isArray(value)) return true;

      for (let i = 0; i < value.length; i++) {
        const item = value[i];
        if (!item || typeof item !== "object") continue;

        const detail = item as unknown as UhsDetail;

        if (!detail.course || !detail.courseType || !detail.credits) {
          return this.createError({
            path: `ushDetails[${i}]`,
            message: "Please complete all required fields for this course",
          });
        }

        if (detail.gradeType === IGradeType.letter && !detail.letterGrade) {
          return this.createError({
            path: `ushDetails[${i}].letterGrade`,
            message: "Letter grade is required",
          });
        }

        if (
          detail.gradeType === IGradeType.percentage &&
          !detail.percentageGrade
        ) {
          return this.createError({
            path: `ushDetails[${i}].percentageGrade`,
            message: "Percentage grade is required",
          });
        }
      }
      return true;
    });
};

export const createDesktopTabletUshValidationSchemas = (
  requiredErrorLabel = "This field is required",
  invalidEmailErrorLabel = "Please enter a valid email address",
  courseRequiredErrorLabel = "Course name is required",
  gradeRequiredErrorLabel = "Grade is required",
  creditsRequiredErrorLabel = "Credits is required",
  courseTypeRequiredErrorLabel = "Course type is required",
  invalidPhoneErrorLabel = "Please enter a valid phone number",
  hasCountryCodeErrorMessage = "Please select a country code",
) => [
  // Step 1: Grade type selection and course details
  object({
    gradeType: string()
      .oneOf(
        [IGradeType.letter, IGradeType.percentage],
        "Please select a valid grade type",
      )
      .required("Please select a grade type"),
    ushDetails: createUhsDetailsValidation(
      courseRequiredErrorLabel,
      gradeRequiredErrorLabel,
      creditsRequiredErrorLabel,
      courseTypeRequiredErrorLabel,
    ),
  }),

  // Step 2: Personal information
  object({
    firstName: textValidation(true, requiredErrorLabel),
    lastName: textValidation(true, requiredErrorLabel),
    email: createEmailValidation(invalidEmailErrorLabel, requiredErrorLabel),
    country: textValidation(true, requiredErrorLabel),
    schoolName: textValidation(true, requiredErrorLabel),
    gradeYearLevel: textValidation(true, requiredErrorLabel),
    countryCode: createCountryCodeObjectValidation(hasCountryCodeErrorMessage),
    phoneNumber: localNumberValidation(
      true,
      invalidPhoneErrorLabel,
      requiredErrorLabel,
    ),
    privacyPolicy: boolean().oneOf(
      [true],
      "You must accept the privacy policy",
    ),
    commsOptIn: boolean(),
  }),
];

export const createMobileUshValidationSchemas = (
  requiredErrorLabel = "This field is required",
  invalidEmailErrorLabel = "Please enter a valid email address",
  courseRequiredErrorLabel = "Course name is required",
  gradeRequiredErrorLabel = "Grade is required",
  creditsRequiredErrorLabel = "Credits is required",
  courseTypeRequiredErrorLabel = "Course type is required",
  invalidPhoneErrorLabel = "Please enter a valid phone number",
  hasCountryCodeErrorMessage = "Please select a country code",
) => [
  // Step 1: Grade type selection
  object({
    gradeType: string()
      .oneOf(
        [IGradeType.letter, IGradeType.percentage],
        "Please select a valid grade type",
      )
      .required("Please select a grade type"),
  }),

  // Step 2: Course details
  object({
    ushDetails: createUhsDetailsValidation(
      courseRequiredErrorLabel,
      gradeRequiredErrorLabel,
      creditsRequiredErrorLabel,
      courseTypeRequiredErrorLabel,
    ),
  }),

  // Step 3: Personal information
  object({
    firstName: textValidation(true, requiredErrorLabel),
    lastName: textValidation(true, requiredErrorLabel),
    email: createEmailValidation(invalidEmailErrorLabel, requiredErrorLabel),
    country: textValidation(true, requiredErrorLabel),
    schoolName: textValidation(true, requiredErrorLabel),
    gradeYearLevel: textValidation(true, requiredErrorLabel),
    countryCode: createCountryCodeObjectValidation(hasCountryCodeErrorMessage),
    phoneNumber: localNumberValidation(
      true,
      invalidPhoneErrorLabel,
      requiredErrorLabel,
    ),
    privacyPolicy: boolean().oneOf(
      [true],
      "You must accept the privacy policy",
    ),
    commsOptIn: boolean(),
  }),
];

export const createEmptyUhsDetail = (gradeType: IGradeType): UhsDetail => ({
  course: "",
  credits: "",
  courseType: "",
  letterGrade: "",
  percentageGrade: "",
  gradeType: gradeType,
});
