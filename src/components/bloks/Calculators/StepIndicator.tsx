import React from "react";
import { cn } from "@/common/utils";

const StepIndicator = ({
  currentStep,
  totalSteps,
}: {
  currentStep: number;
  totalSteps: number;
}) => {
  return (
    <div className="flex items-center justify-center">
      {Array.from({ length: totalSteps }).map((_, index) => (
        <div
          key={index}
          className={cn(
            "h-1 w-7 rounded-full transition-all duration-300 md:w-[3.875rem]",
            index + 1 === currentStep ? "bg-primary01-75" : "bg-grays-G5",
            index < totalSteps - 1 ? "mr-[6px]" : "",
          )}
        />
      ))}
    </div>
  );
};

export default StepIndicator;
