import React from "react";
import { FormikHel<PERSON> } from "formik";
import { DeviceType } from "@/common/types";

import Button from "@/components/ui/Button";

interface FormButtonsProps {
  step: number;
  deviceType: DeviceType;
  isSubmitting: boolean;
  animationComplete: boolean;
  handleBack: () => void;
  formikProps: FormikHelpers<any>;
  mobileMaxStep: number;
  tabletMaxStep: number;
  desktopMaxStep: number;
  blok: {
    nextButtonLabel: string;
    previousButtonLabel: string;
    submitButtonLabel: string;
  };
}

const FormButtons: React.FC<FormButtonsProps> = ({
  step,
  deviceType,
  isSubmitting,
  animationComplete,
  handleBack,
  mobileMaxStep,
  tabletMaxStep,
  desktopMaxStep,
  blok,
}) => {
  const isMobile = deviceType === DeviceType.Mobile;
  const isTablet = deviceType === DeviceType.Tablet;
  const isDesktop = deviceType === DeviceType.Desktop;

  const getMaxSteps = () => {
    if (isMobile) return mobileMaxStep;
    if (isTablet) return tabletMaxStep;
    if (isDesktop) return desktopMaxStep;
    return desktopMaxStep;
  };

  const maxSteps = getMaxSteps();
  const isLastStep = step === maxSteps;

  const LoadingSpinner = () => (
    <svg
      className="mr-2 size-4 animate-spin"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      ></circle>
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  );

  const previousButtonLabel = blok?.previousButtonLabel ?? "Back";
  const buttonLabel = isLastStep
    ? blok.submitButtonLabel
    : blok.nextButtonLabel;

  const isDisabled = isSubmitting || !animationComplete;

  if (step > 1) {
    return (
      <div className="flex w-full justify-between">
        <div className={isDisabled ? "pointer-events-none opacity-50" : ""}>
          <Button
            type="button"
            colour="maroon"
            theme="secondary"
            onClick={handleBack}
            disabled={false}
          >
            {previousButtonLabel}
          </Button>
        </div>

        <div className={isDisabled ? "pointer-events-none opacity-50" : ""}>
          <Button
            type="submit"
            colour="maroon"
            theme="primary"
            disabled={isDisabled}
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center">
                <LoadingSpinner />
                <span>Submitting...</span>
              </span>
            ) : (
              buttonLabel
            )}
          </Button>
        </div>
      </div>
    );
  } else {
    return (
      <div className="flex w-full justify-end">
        <div className={isDisabled ? "pointer-events-none opacity-50" : ""}>
          <Button
            type="submit"
            disabled={isDisabled}
            colour="maroon"
            theme="primary"
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center">
                <LoadingSpinner />
                <span>Submitting...</span>
              </span>
            ) : (
              buttonLabel
            )}
          </Button>
        </div>
      </div>
    );
  }
};

export default FormButtons;
