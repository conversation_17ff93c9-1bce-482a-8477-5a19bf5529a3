"use client";
import { useState, useEffect } from "react";
import { cn } from "@/common/utils";

interface ScoreResult {
  gpaScore: string | Record<string, string>;
  firstName?: string;
  lastName?: string;
}

interface ScoreDisplayProps {
  gpaScoreSessionKey?: string;
  className?: string;
  loadingSize?: "sm" | "md" | "lg";
  animationDelay?: number;
  gpaScoreString?: string;
}

const GpaScoreDisplay = ({
  gpaScoreSessionKey,
  className = "",
  loadingSize = "md",
  animationDelay = 150,
  gpaScoreString = "",
}: ScoreDisplayProps) => {
  const [scoreResult, setScoreResult] = useState<ScoreResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (gpaScoreSessionKey && gpaScoreString === "") {
      if (typeof window !== "undefined") {
        const storedData = sessionStorage.getItem(gpaScoreSessionKey);
        if (storedData) {
          try {
            const result = JSON.parse(storedData) as ScoreResult;
            setScoreResult(result);
          } catch (error) {
            console.warn(
              `Failed to parse score result for key "${gpaScoreSessionKey}":`,
              error,
            );
            setScoreResult({
              gpaScore: "0.00",
              firstName: "",
              lastName: "",
            });
          }
        } else {
          setScoreResult({
            gpaScore: "0.00",
            firstName: "",
            lastName: "",
          });
        }
        setIsLoading(false);
      }
    }
  }, [gpaScoreSessionKey, gpaScoreString]);

  useEffect(() => {
    if (!isLoading && scoreResult) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, animationDelay);

      return () => clearTimeout(timer);
    }
  }, [isLoading, scoreResult, animationDelay]);

  if (!isLoading && !scoreResult?.gpaScore) {
    return null;
  }

  const loadingSizes = {
    sm: "size-3 border-[1.5px]",
    md: "size-4 border-2",
    lg: "size-6 border-[3px]",
  };

  const defaultContainerClasses = `
    flex size-full items-center justify-center 
    font-display-sans text-[100px] font-bold leading-[85%] text-primary01-75 
    transition-all duration-1000 
    md:text-[110px] lg:text-[130px]
  `.trim();

  const animationClasses = isVisible
    ? "translate-y-0 opacity-100"
    : "translate-y-4 opacity-0";

  console.log(`WJM--gpaScore`, scoreResult);

  return (
    <div className={cn(defaultContainerClasses, animationClasses, className)}>
      {isLoading ? (
        <div
          className={cn(
            "animate-spin rounded-full border-primary01-75/30 border-t-primary01-75",
            loadingSizes[loadingSize],
          )}
        />
      ) : (
        <>{gpaScoreString === "" ? scoreResult?.gpaScore : gpaScoreString}</>
      )}
    </div>
  );
};

export default GpaScoreDisplay;
