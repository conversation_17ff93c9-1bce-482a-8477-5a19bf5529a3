import { storyblokEditable, StoryblokComponent } from "@storyblok/react/rsc";
import type { Theme, TBackgroundColour } from "@/common/types";
import Container from "../ui/Container";
import Text from "@/components/ui/Text";
import ImageWrapper from "../ui/ImageWrapper";
import {
  generateImageAltFromFilename,
  getDataSourceBackgroundImages,
} from "@/common/utils";

export interface IWebinarEventHeader {
  [key: string]: any;
  _uid: string;
  backgroundImage: [{ _uid: string; image: string; altText: string }];
  thumbnailImage: [{ _uid: string; image: string; altText: string }];
  ctaButton: [{ _uid: string }];
  backgroundColour: TBackgroundColour;
  theme: Theme;
  title: string;
  description: string;
  backgroundImagePreset?: string;
}

interface Props {
  blok: IWebinarEventHeader;
  eventType: { _uid: string };
  sectionIndex: number;
}

const WebinarEventHeader = ({ blok, eventType }: Props) => {
  const { desktopImage, mobileImage } = getDataSourceBackgroundImages(
    blok?.backgroundImagePreset ?? "",
  );

  return (
    <section
      {...storyblokEditable(blok)}
      className="relative bg-grays-G6 pt-[75px] text-white"
    >
      <div className="absolute inset-0 flex overflow-hidden">
        {desktopImage && (
          <ImageWrapper
            src={desktopImage}
            alt={
              generateImageAltFromFilename(desktopImage) ??
              "Webinar event header background"
            }
            sizes="100vw"
            className="hidden size-full object-cover object-[top_center] md:block"
            width={2880}
            height={1200}
            fetchPriority="high"
            quality={80}
          />
        )}
        {mobileImage && (
          <ImageWrapper
            src={mobileImage}
            alt={
              generateImageAltFromFilename(mobileImage) ??
              "Webinar event header background"
            }
            sizes="(max-width: 600px) 100vw, (max-width: 1200px) 50vw, 400px"
            className="block size-full object-cover object-center md:hidden"
            width={500}
            height={700}
            fetchPriority="high"
          />
        )}
        <div className="absolute inset-0 bg-black/60" />
      </div>

      <Container>
        <div className="relative text-center">
          {eventType && (
            <StoryblokComponent blok={eventType} key={eventType._uid} />
          )}

          <Text
            tag="h1"
            style="mh1.5"
            mdStyle="h2"
            lgStyle="h1"
            className="mb-7"
          >
            {blok.title}
          </Text>

          <Text
            tag="p"
            style="mb1"
            mdStyle="b1"
            className="mx-auto my-[45px] max-w-[854px] md:my-0"
          >
            {blok.description}
          </Text>

          {blok.ctaButton?.length > 0 && (
            <div className="mt-10 hidden md:block">
              <StoryblokComponent
                blok={{
                  ...blok.ctaButton[0],
                  theme: "primary",
                  colour: "white",
                }}
                key={blok.ctaButton[0]._uid}
              />
            </div>
          )}
        </div>
      </Container>
    </section>
  );
};

export default WebinarEventHeader;
