import React, { cache } from "react";
import { fetchStoryBySlug } from "@/common/storyblok";
import { siteUrl } from "@/common/constants";
import ResultStack, { IResultsProps } from "./ResultStackSection";
import { DataSetTarget, getAcceptanceData } from "./utils";
import { College } from "./types";

type StoryData = {
  story: {
    full_slug: string;
  };
};

const fetchUrlSlug = async (id: string) => fetchStoryBySlug(id, false, true);

const getData = cache(
  async (link: string, acceptanceRates: DataSetTarget, language: string) => {
    let properLink = "";
    try {
      const { data } = await fetchUrlSlug(link);

      const { story } = data as StoryData;

      properLink = `${siteUrl}/${story.full_slug}`;
    } catch {
      properLink = "";
    }

    let collegeAdmissionsResults: College[] = [];

    try {
      collegeAdmissionsResults =
        (await getAcceptanceData(acceptanceRates, language)) ?? [];
    } catch {}

    return {
      properLink,
      collegeAdmissionsResults,
    };
  },
);

export default async function ResultStackSection({
  blok,
}: {
  blok: IResultsProps;
}) {
  const { properLink, collegeAdmissionsResults } = await getData(
    blok.link,
    blok.acceptanceRates,
    blok.language,
  ).catch(() => ({ properLink: "", collegeAdmissionsResults: [] }));

  return (
    <ResultStack
      {...blok}
      link={properLink}
      collegeAdmissionsResults={collegeAdmissionsResults}
    />
  );
}
