import { fetchDataSource } from "@/common/storyblok";
import {
  ACCEPTANCE_DATASET,
  ACCEPTANCE_COMPARISON_DIMENSION,
  NAME_DIMENSION,
  aprovedSchools,
} from "./constants";

export const getAcceptanceData = async (
  resultsFor: DataSetTarget = "us",
  language = "en",
) => {
  const perPage = 100;
  const [dataResponse, namesResponse]: SbDataValue[] = await Promise.all([
    fetchDataSource(
      ACCEPTANCE_DATASET,
      false,
      ACCEPTANCE_COMPARISON_DIMENSION,
      perPage,
    ),
    fetchDataSource(
      ACCEPTANCE_DATASET,
      false,
      language === "en" ? NAME_DIMENSION : language,
      perPage,
    ),
  ]);

  const acceptanceData = dataResponse?.data?.datasource_entries;
  const nameData = namesResponse?.data?.datasource_entries;

  const resultingDataset = [];

  if (!acceptanceData?.length || !nameData?.length) return [];

  for (const aprovedSchool of aprovedSchools[resultsFor]) {
    // we want to maintain this order
    const dataIndex =
      nameData?.findIndex((record) =>
        record?.name?.match(new RegExp(aprovedSchool, "ig")),
      ) ?? -1;

    if (
      !nameData[dataIndex]?.dimension_value ||
      !acceptanceData[dataIndex]?.dimension_value
    )
      continue;

    const parsedCollegeStats: {
      general_acceptance_rate: number;
      crimson_acceptance_rate: number;
      crimson_acceptance_rate_UsStudentOnly: number;
      general_acceptance_rate_UsStudentOnly: number;
    } = JSON.parse(acceptanceData[dataIndex].dimension_value);

    const useUSStats = resultsFor === "us";

    resultingDataset.push({
      crimsonAcceptanceRate:
        (useUSStats
          ? parsedCollegeStats.crimson_acceptance_rate_UsStudentOnly
          : undefined) ?? parsedCollegeStats.crimson_acceptance_rate,
      generalAcceptanceRate:
        (useUSStats
          ? parsedCollegeStats.general_acceptance_rate_UsStudentOnly
          : undefined) ?? parsedCollegeStats.general_acceptance_rate,
      college: nameData[dataIndex].dimension_value,
    });
  }

  return resultingDataset;
};


export interface SbDataValue {
  data: {
    datasource_entries?: {
      dimension_value?: string;
      id: number;
      name: string;
      value: string;
    }[];
  };
}

export type DataSetTarget = "us" | "mixed";

