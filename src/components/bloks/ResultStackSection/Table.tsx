import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import React from "react";

interface Results {
  college: string;
  generalAcceptanceRate: number;
  crimsonAcceptanceRate: number;
}

interface TableProps {
  title: string;
  data: Results[];
  heading: string[];
  className: string;
}

const Table: React.FC<React.PropsWithChildren<TableProps>> = ({
  title,
  data,
  heading,
  className,
}) => {
  return (
    <div className={cn("grid overflow-hidden rounded font-body-p", className)}>
      <div className="w-full border-b border-white bg-neutral01-25 px-4 py-3">
        <Text
          tag="p"
          style="h5"
          className="text-center leading-[120%] text-primary01-75"
        >
          {title}
        </Text>
      </div>
      <table className="w-full">
        <thead className="bg-neutral01-25">
          {
            <tr>
              {heading.map((header) => (
                <TableCol
                  key={`head_${header}`}
                  className="border-0 text-center last-of-type:font-bold"
                >
                  {header}
                </TableCol>
              ))}
            </tr>
          }
        </thead>
        <tbody>
          {data?.map((col: Results) => (
            <tr
              key={`${col.college}_row_${col.crimsonAcceptanceRate}`}
              className="even:bg-neutral01-0"
            >
              <TableCol className="text-left! border-r border-neutral01-25 font-bold text-neutral01-75">
                {col.college}
              </TableCol>
              <TableCol className="border-r border-neutral01-25 text-center">
                {col.generalAcceptanceRate}%
              </TableCol>
              <TableCol className="text-center font-bold text-primary01-50">
                {col.crimsonAcceptanceRate}%
              </TableCol>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const TableCol: React.FC<React.HTMLAttributes<HTMLTableCellElement>> = ({
  children,
  className,
}) => (
  <td className={cn("px-3 py-4 text-neutral01-75", className)}>{children}</td>
);

export default Table;
