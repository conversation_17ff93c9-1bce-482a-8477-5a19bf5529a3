"use client";

import {
  easeInOut,
  motion,
  useInView,
  useMotionValue,
  useTransform,
} from "motion/react";
import React, { useMemo, useRef } from "react";
import { GridCell } from "./misc";
import { College } from "./types";
import { cn } from "@/common/utils";
import { DataSetTarget } from "./utils";
import Text from "@/components/ui/Text";

interface ChartProps {
  title: string;
  generalAcceptanceAxisLabel: string;
  crimsonAcceptanceAxisLabel: string;
  colleges: College[];
  resultsFor: DataSetTarget;
  className: string;
}

const Chart: React.FC<ChartProps> = ({
  title,
  generalAcceptanceAxisLabel,
  crimsonAcceptanceAxisLabel,
  colleges,
  resultsFor,
  className,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const isVisible = useInView(ref, { once: true, amount: 0.8 });
  const sizes = useMemo(() => {
    let crimsonLowest = +Infinity;
    let crimsonHighest = 0;

    colleges.forEach((college) => {
      if (college.crimsonAcceptanceRate > crimsonHighest) {
        crimsonHighest = Number(college.crimsonAcceptanceRate);
      }
      if (college.crimsonAcceptanceRate < crimsonLowest) {
        crimsonLowest = Number(college.crimsonAcceptanceRate);
      }
    });

    return {
      min: crimsonLowest,
      max: crimsonHighest,
      offset: resultsFor === "us" ? 10 : 2,
    };
  }, [colleges, resultsFor]);

  return (
    <GridCell className={cn("flex size-full w-full", className)}>
      <Text
        tag="p"
        style="h5"
        className="mb-5 leading-[120%] text-primary01-75"
      >
        {title}
      </Text>
      <div className="mb-6 flex gap-5 font-body-single">
        <Text tag="p" style="b3" className="leading-[120%] text-neutral01-50">
          {generalAcceptanceAxisLabel}
        </Text>
        <Text
          tag="p"
          style="b3"
          className="font-bold leading-[120%] text-primary01-50"
        >
          {crimsonAcceptanceAxisLabel}
        </Text>
      </div>
      <div className="relative w-full flex-1" ref={ref}>
        <div className="relative z-[7] grid grid-cols-1 gap-y-[14px]">
          {isVisible &&
            colleges.map((college) => (
              <ChartRow
                key={`${college.college}`}
                name={college.college}
                crimsonScore={college.crimsonAcceptanceRate}
                generalScore={college.generalAcceptanceRate}
                range={sizes}
              />
            ))}
        </div>
      </div>
    </GridCell>
  );
};

const OutputTransformation = ["0%", "100%"];
const CrimsonOutputTransformation = ["0%", "100%"];

interface Range {
  min: number;
  max: number;
  offset: number;
}

interface ChartRowProps {
  name: string;
  crimsonScore: number;
  generalScore: number;
  range: Range;
}

const ChartRow = ({
  name,
  crimsonScore,
  generalScore,
  range,
}: ChartRowProps) => {
  const { max, offset } = range ?? {};

  const crimsonWidth = useMotionValue(crimsonScore);
  const generalWidth = useMotionValue(generalScore);

  const crimsonWidthAnimtion = useTransform(
    crimsonWidth,
    [0, max],
    CrimsonOutputTransformation,
    {
      ease: easeInOut,
    },
  );
  const generalAnimation = useTransform(
    generalWidth,
    [0, max - offset],
    OutputTransformation,
    {
      ease: easeInOut,
    },
  );

  return (
    <div className="relative grid h-full grid-cols-[20%,auto] items-center gap-4 font-body-single">
      <span className="break-words text-body-single-sm font-bold text-neutral01-75">
        {name}
      </span>
      <div className="flex flex-col gap-1 font-body-single">
        <AnimatedDiv
          width={generalAnimation.get()}
          className="bg-neutral01-25"
          score={generalScore}
        />
        <AnimatedDiv
          className="justify-end bg-gradient-to-r from-primary01-75 to-primary01-50 font-bold text-white"
          width={crimsonWidthAnimtion.get()}
          score={crimsonScore}
        />
      </div>
    </div>
  );
};

const formatNumber = (n: number) => n.toFixed(1).replace(/[.,]0$/, "");

interface AnimatedDivProps {
  width: string;
  initial?: number;
  score: number;
  className: string;
}
const AnimatedDiv: React.FC<React.PropsWithChildren<AnimatedDivProps>> = ({
  score,
  width,
  initial = 0,
  className,
}) => (
  <div className="flex h-[14px] w-full items-center">
    <motion.div
      className={cn("h-full rounded-r", className)}
      animate={{ width }}
      initial={{ width: initial }}
      transition={{ duration: 1.7 }}
    />

    <motion.span
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 1.8, duration: 0.5, type: "tween" }}
      className={"relative z-[7] ml-3 text-xs"}
    >
      {formatNumber(score)}%
    </motion.span>
  </div>
);

export default Chart;
