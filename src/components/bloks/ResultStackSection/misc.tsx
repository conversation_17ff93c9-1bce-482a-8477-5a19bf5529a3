"use client";

import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import { motion, useInView } from "motion/react";
import React, { useRef } from "react";

export const GridCell: React.FC<
  React.PropsWithChildren<{
    layout?: "rows" | "cols";
    className?: string;
  }>
> = ({ children, className = "", layout = "rows" }) => {
  return (
    <div
      className={cn(
        "flex w-full rounded bg-neutral01-0 px-6 py-7 font-body-single",
        layout == "rows" && "flex-col",
        layout == "cols" && "flex-row items-center",
        className,
      )}
    >
      {children}
    </div>
  );
};

export const SwipeUpAnimation = ({
  children,
  className,
}: React.HTMLProps<HTMLHeadingElement>) => {
  const ref = useRef<HTMLDivElement>(null);
  const isVisible = useInView(ref, { once: true, amount: "all" });

  return (
    <div className="w-full overflow-hidden" ref={ref}>
      <motion.h4
        className={cn(
          "font-display-sans font-bold leading-none text-primary01-75",
          className,
        )}
        initial={{ translateY: 100 }}
        animate={{ translateY: isVisible ? 0 : 100 }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
        viewport={{ amount: 0.5 }}
      >
        {children}
      </motion.h4>
    </div>
  );
};

export const StyledParagraph: React.FC<
  React.HTMLAttributes<HTMLParagraphElement>
> = ({ children, className }) => (
  <Text
    tag="p"
    style="b2"
    lgStyle="b1"
    className={cn("text-neutral01-75", className)}
  >
    {children}
  </Text>
);
