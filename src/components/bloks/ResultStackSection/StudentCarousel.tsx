"use client";

import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/common/utils";
import { IStudent } from "@/common/types";
import { motion } from "motion/react";
import Text from "@/components/ui/Text";

const StudentCarousel: React.FC<{ items: IStudent[]; className?: string }> = ({
  items,
  className,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  return (
    <div className={cn("relative h-full object-contain", className)}>
      {items.map((item, idx) => (
        <motion.img
          key={item.image.filename}
          src={item.image.filename}
          alt="Student Results Head Shot"
          initial={false}
          animate={{ opacity: idx === currentIndex ? 1 : 0 }}
          transition={{ duration: 1.5, type: "tween" }}
          className="absolute left-0 top-0 size-full object-cover"
        />
      ))}
      <InfiniteCarousel items={items} beforeSlide={(i) => setCurrentIndex(i)} />
    </div>
  );
};

export const InfiniteCarousel = ({
  beforeSlide,
  items,
}: {
  beforeSlide: (i: number) => void;
  items: IStudent[];
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [index, setIndex] = useState(0);

  const scrollNext = () => {
    if (!containerRef.current) return;
    setIndex((index) => (index + 1) % items.length);
  };

  useEffect(() => {
    if (beforeSlide) beforeSlide(index);
  }, [index, beforeSlide]);

  useEffect(() => {
    const interval = setInterval(scrollNext, 5000);
    return () => {
      clearInterval(interval);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="relative" ref={containerRef}>
      {items.map((item, studentIndex) => (
        <div
          key={studentIndex}
          className="absolute min-w-full shrink-0 text-neutral01-100"
        >
          <StudentDetails
            item={item}
            studentIndex={studentIndex}
            index={index}
          />
        </div>
      ))}
    </div>
  );
};

function StudentDetails({
  item,
  studentIndex,
  index,
}: {
  item: IStudent;
  studentIndex: number;
  index: number;
}): React.JSX.Element {
  return (
    <motion.div
      key={studentIndex}
      initial={{ opacity: 0 }}
      animate={{
        opacity: studentIndex === index ? 1 : 0,
      }}
      transition={{
        duration: studentIndex === index ? 1.0 : 0,
        ease: "easeInOut",
      }}
      className="relative size-full min-h-[200px] text-neutral01-100"
    >
      <div className="absolute top-5 z-[7] mx-[1.63rem]">
        <Text tag="p" style="q3">
          {item.name}
        </Text>
        <Text tag="p" style="b2">
          {item.college},
        </Text>
        <Text tag="p" style="b2">
          {item.classYear}
        </Text>
      </div>
    </motion.div>
  );
}

export default StudentCarousel;
