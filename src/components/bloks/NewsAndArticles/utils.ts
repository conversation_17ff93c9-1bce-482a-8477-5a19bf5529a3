import { formatDateTime } from "@/common/date";
import { fetchStoryblokStories } from "@/common/storyblok";
import { siteUrl } from "@/common/constants";
import { IStory, IStoryContent, ITag } from "@/common/types";

export interface IBlogStoryContent extends IStoryContent {
  excludeCTA: boolean;
  canonicalUrl?: string;
  removeHeaderLogoLink: boolean;
  author: any;
  Authors?: any;
  persona?: string;
  blogTags: ITag[];
  shareImage: string;
  caseStudyHeader?: {
    studentImage?: { filename: string; alt: string };
  }[]
}

export type IBlogStory = IStory<IBlogStoryContent>;

export interface IRecords {
  image: string;
  name: string;
  link: string;
  created_date: string;
}

const draftMode = false;

export async function getBlogs(
  locale: string,
  total = 4,
  exclude: string[],
): Promise<IRecords[]> {
  const filter_query: Record<string, Record<string, string>> = {
    component: { is: "blogPage" },
    shareImage: { is: "not_empty" },
    pageTitle: { is: "not_empty" },
  };

  const { data } = await fetchStoryblokStories<IStory<IBlogStoryContent>>({
    sort_by: "recent",
    per_page: total + TOTAL_PREVIEW_BLOGS, // we want to avoid having duplicate blogs
    starts_with: `${locale}/blog`,
    filter_query,
    draftMode,
  });

  if (data?.stories?.length) {
    const trendingBlogs = data.stories;

    // fetch data for each blog
    return trendingBlogs
      .filter((story) => !exclude.find((uid) => uid === story.uuid))
      .map((story) => ({
        image: story.content.shareImage,
        name: story.content.pageTitle,
        link: `${siteUrl}/${story.full_slug}`,
        created_date: story.published_at || story.created_at,
      }));
  }

  return [];
}

export interface IStoryBlogTrending {
  stories: {
    content: {
      homepageTrending: string[];
    };
  }[];
}

const TOTAL_PREVIEW_BLOGS = 3;

export async function getBlogData(locale: string): Promise<IRecords[]> {
  const response = (await fetchStoryblokStories({
    slug: `${locale}/blog/blog-trendings`,
    draftMode,
  })) as { data: IStoryBlogTrending };

  const trendingBlogs =
    response?.data?.stories?.[0]?.content?.homepageTrending ?? [];
  // fetch data for each blog
  let extraBlogs: IRecords[] = [];

  // must always have ~3 latest blogs on the right
  if ((trendingBlogs.length || 0) < TOTAL_PREVIEW_BLOGS) {
    const difference = TOTAL_PREVIEW_BLOGS - (trendingBlogs.length || 0);

    // fallback to global blogs trendings
    if (extraBlogs.length < difference) {
      const response = (await fetchStoryblokStories({
        slug: `en/blog/blog-trendings`,
        draftMode,
      })) as { data: IStoryBlogTrending };
      const additionalBlogs =
        response?.data?.stories?.[0]?.content?.homepageTrending ?? [];

      trendingBlogs.push(...additionalBlogs);
    }
  }

  if (trendingBlogs?.length) {
    const { data } = await fetchStoryblokStories<IBlogStory>({
      sort_by: "recent",
      uuids: trendingBlogs,
    });

    if (data?.stories?.length) {
      const blogs = data.stories;

      // fetch data for each blog
      extraBlogs = extraBlogs.concat(
        blogs.map((story) => ({
          image: story.content.shareImage,
          name: story.content.pageTitle,
          link: `${siteUrl}/${story.full_slug}`,
          created_date: story.published_at,
        })),
      );
    }
  }

  return extraBlogs;
}

export async function formatDate(date: string, locale: string) {
  const [dateFormatted] = await formatDateTime({
    dateTime: date,
    locale,
    getDateFormat: (locale: string) => {
      const isSpaceless = /^(ja|zh).*/.test(locale ?? "");
      return isSpaceless ? "MMMDo" : "MMMM DD";
    },
  });

  return dateFormatted ?? date;
}
