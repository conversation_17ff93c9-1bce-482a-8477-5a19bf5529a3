"use client";

import { cn } from "@/common/utils";
import { useEffect, useState } from "react";
import { formatDate } from "./utils";

export const DateLabel: React.FC<
  React.PropsWithChildren<
    React.HTMLAttributes<HTMLParagraphElement> & {
      date: string;
      locale: string;
    }
  >
> = ({ date, locale }) => {
  const [formattedDate, setFormattedDate] = useState("");

  useEffect(() => {
    const dateLocale = locale?.split("-")?.[0] ?? "en";
    formatDate(date, dateLocale)
      .then((dt) => setFormattedDate(dt))
      .catch(() => setFormattedDate(date));
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <p
      className={cn(
        "font-body-single text-body2 text-primary01-25 md:text-body-single-2xs lg:text-body-single-sm",
      )}
    >
      {formattedDate}
    </p>
  );
};
