import React from "react";
import Link from "next/link";
import Container from "@/components/ui/Container";
import { siteUrl } from "@/common/constants";
import { fetchStoryblokStories } from "@/common/storyblok";
import { getDayjsDate } from "@/common/date";
import Text from "@/components/ui/Text";
import { Props } from "./types";
import { MajorArticleCard, BlogArticleCard } from "./ArticlesCard";
import { getBlogData, IRecords } from "./utils";
import { storyblokEditable } from "@storyblok/react";

interface StoryData {
  stories: {
    full_slug: string;
  }[];
}

const getData = async (majorArticleLink: string) => {
  let properLink = "";
  try {
    const { data } = (await fetchStoryblokStories({
      uuids: [majorArticleLink],
    })) as { data: StoryData };

    const story = data.stories?.[0];

    if (story) properLink = `${siteUrl}/${story.full_slug}`;
  } catch {
    properLink = "";
  }

  return properLink;
};

const sortBlogs = (blogs: IRecords[]) => {
  try {
    return blogs.sort(
      (left, right) =>
        -getDayjsDate(left.created_date).diff(getDayjsDate(right.created_date)),
    );
  } catch {
    return blogs;
  }
};

export default async function NewsAndArticlesM({ blok, locale }: Props) {
  const {
    heading,
    buttonLabel,
    majorArticleLink,
    majorArticleImage,
    majorArticleTitle,
    publisherLogo,
  } = blok;

  const articleLink = await getData(majorArticleLink);
  const blogs = sortBlogs(await getBlogData(locale));

  return (
    <section
      className="bg-[radial-gradient(164.83%_149.93%_at_0%_0%,_#74070E_0%,_#A40D0E_41.5%,_#3A0407_78%)]"
      {...storyblokEditable({ ...blok })}
    >
      <div className="relative mx-auto w-full">
        <Container className="px-4 md:p-[30px] lg:py-[75px]">
          <section className="min-h-[325px]">
            <div className="flex w-full flex-col gap-xl lg:flex-row lg:items-center lg:justify-between">
              <Text
                tag="h3"
                style="mh1.5"
                mdStyle="h2"
                className="text-h3 text-white"
              >
                {heading}
              </Text>
              <Link
                href={`/${locale}/blog/`}
                className="cursor-pointer self-start rounded border border-white px-7 py-3 font-display-sans text-body2 text-white"
              >
                <Text tag="span" style="button">
                  {buttonLabel}
                </Text>
              </Link>
            </div>
            <div className="mt-[50px] grid min-h-[325px] grid-cols-1 gap-y-10 md:grid-cols-2 md:gap-x-8 lg:gap-10">
              <MajorArticleCard
                majorArticleImage={majorArticleImage}
                majorArticleLink={articleLink}
                publisherLogo={publisherLogo}
                majorArticleTitle={majorArticleTitle}
              />

              <div className="grid grid-cols-1 gap-y-10 md:h-full md:gap-y-[22px] md:overflow-y-auto lg:gap-y-5">
                {blogs.slice(0, 3).map((blog) => {
                  return (
                    <BlogArticleCard
                      key={blog.name}
                      {...blog}
                      locale={locale}
                    />
                  );
                })}
              </div>
            </div>
          </section>
        </Container>
      </div>
    </section>
  );
}
