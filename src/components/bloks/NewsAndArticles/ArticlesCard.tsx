import Link from "next/link";
import Image from "next/image";
import { INewsAndBlogProps } from "./types";
import { Title } from "./misc";
import { DateLabel } from "./DateLabel";
import { IRecords } from "./utils";
import { httpsPath } from "@/common/utils";

interface IArticleProps {
  majorArticleTitle: string;
  majorArticleLink: string;
  majorArticleImage: INewsAndBlogProps["majorArticleImage"];
  publisherLogo: string;
}

export const MajorArticleCard = (props: IArticleProps) => (
  <Link href={props.majorArticleLink}>
    <div className="relative h-[358px] bg-primary01-50 md:h-full">
      <Image
        src={props.majorArticleImage.filename}
        alt={props.majorArticleImage.alt}
        className="size-full object-cover"
        width={800}
        height={384}
      />
      <div className="absolute bottom-0 flex size-full flex-col gap-3 bg-gradient-to-t from-black/75 to-transparent to-50% p-3 text-white md:p-6">
        <div className="mt-auto w-full max-w-[250px]">
          <Image
            src={props.publisherLogo}
            alt="official article"
            width={20}
            height={20}
            className="inline-block size-auto max-w-full object-left"
          />
        </div>
        <Title mdStyle="sh5" className={"whitespace-pre-wrap"}>
          {props.majorArticleTitle}
        </Title>
      </div>
    </div>
  </Link>
);

function getUrl(url: string, locale: string) {
  try {
    return url.replace("/en/", `/${locale}/`);
  } catch {
    return url;
  }
}

export const BlogArticleCard = async (props: IRecords & { locale: string }) => {
  const { image, link, name, created_date, locale } = props;

  return (
    <Link href={getUrl(link, locale)}>
      <div className="flex flex-col gap-5 md:flex-row md:gap-3">
        <Image
          src={httpsPath(image)}
          alt=""
          width={250}
          height={225}
          className="h-[212px] w-full shrink-0 object-cover md:h-[81px] md:w-[123px] lg:h-[129px] lg:w-[182px]"
        />
        <div className="flex h-full flex-col gap-1.5 text-white md:mt-0">
          <DateLabel date={created_date} locale={locale} />
          <Title
            xlStyle="sh5"
            disableMd
            className="md:font-display-serif md:text-b2"
          >
            {name}
          </Title>
        </div>
      </div>
    </Link>
  );
};
