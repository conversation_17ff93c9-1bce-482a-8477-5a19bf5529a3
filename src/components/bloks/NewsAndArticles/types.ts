export interface INewsAndBlogProps {
  _uid: string;
  heading: string;
  component: string;
  locale: string;
  buttonLabel: string;
  publisherLogo: string;
  majorArticleLink: string;
  majorArticleImage: {
    id: number;
    alt: string;
    name: string;
    source: string;
    filename: string;
    fieldtype: string;
    is_external_url: boolean;
  };
  majorArticleTitle: string;
}

export interface Props {
  blok: INewsAndBlogProps;
  locale: string;
}
