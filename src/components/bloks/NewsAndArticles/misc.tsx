import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";

type AllowedTitleStyles = "ph1" | "sh5";

export const Title: React.FC<
  React.PropsWithChildren<{
    className?: string;
    mdStyle?: AllowedTitleStyles;
    xlStyle?: AllowedTitleStyles;
    disableMd?: boolean;
  }>
> = ({ children, className, disableMd, mdStyle = "sh5", xlStyle }) => (
  <Text
    tag="p"
    style="q4"
    mdStyle={!disableMd ? mdStyle : undefined}
    lgStyle={xlStyle ?? "ph1"}
    className={cn("!leading-[120%]", className)}
  >
    {children}
  </Text>
);


