import { highlightText } from "@/common/renderHighlight";
import { cn, generateImageAltFromFilename } from "@/common/utils";
import Container from "@/components/ui/Container";
import {
  CloseQuotation,
  OpenQuotation,
} from "@/components/ui/OpenCloseQuotation";
import Text from "@/components/ui/Text";
import Image from "next/image";
import React from "react";

interface Props {
  blok: {
    heading: string;
    subHeading: string;
    bodyContent: string;
    quote: string;
    attribution: string;
    image?: string;
    bottomPadding: "normal" | "thin";
  };
}

export default function H2BodyAndQuote({ blok }: Props) {
  const {
    heading,
    subHeading,
    bodyContent,
    quote,
    attribution,
    bottomPadding,
    image,
  } = blok;
  const altAttrOfImage = generateImageAltFromFilename(image ?? "quote");
  return (
    <section className="bg-grays-G6">
      <Container
        className={cn(
          "grid gap-10 md:gap-20 lg:grid-cols-2 lg:items-center lg:flex-row lg:gap-[97px] xl:gap-[106px] 2xl:gap-[126px]",
          bottomPadding === "thin" && "pb-10",
        )}
      >
        <div className="">
          <Text
            tag="h2"
            style="mh1.5"
            mdStyle="h2"
            className="mb-10 whitespace-pre text-primary01-75 md:mb-20"
          >
            {heading}
          </Text>
          <Text tag="h3" style="h4" className="mb-6 text-grays-G1">
            {subHeading}
          </Text>
          <Text
            tag="p"
            style="b2"
            mdStyle="b1"
            className="whitespace-pre text-neutral01-75"
          >
            {bodyContent}
          </Text>
        </div>
        <div className={cn(!quote && "flex items-center")}>
          {quote && (
            <>
              <Text
                tag="p"
                style="q4"
                lgStyle="q2"
                xlStyle="q1"
                className="text-grays-G1"
              >
                <OpenQuotation />
                {highlightText(
                  quote,
                  "text-q4 italic text-primary01-50 md:text-q1",
                )}
                <CloseQuotation />
              </Text>
              <Text
                tag="p"
                style="b3"
                className="mt-8 line-clamp-2 text-neutral01-75 lg:line-clamp-1"
              >
                {attribution}
              </Text>
            </>
          )}
          {!quote && !!image && (
            <Image
              src={image}
              alt={altAttrOfImage ?? "quote"}
              className="max-h-full w-full max-w-full"
              width={4024}
              height={2024}
            />
          )}
        </div>
      </Container>
    </section>
  );
}
