import React from "react";
import Text from "@/components/ui/Text";
import Button from "@/components/ui/Button";
import { cn } from "@/common/utils";

import { IStoryblokLink } from "@/common/types";

interface GeneralLink {
  _uid: string;
  link: IStoryblokLink;
  newTab: boolean;
  component: string;
  _editable?: string;
}

export interface IMiscHeader {
  _uid: string;
  link: GeneralLink[];
  add_cta: boolean;
  heading: string;
  component: string;
  body_content: string;
  button_label: string;
  background_image: string;
  _editable?: string;
}

export interface MiscHeaderProps {
  miscHeaderBlok: IMiscHeader;
}

const MiscHeader: React.FC<MiscHeaderProps> = ({ miscHeaderBlok }) => {
  const bgList = miscHeaderBlok?.background_image?.split(",");
  const backgroundImage = bgList?.[0] ?? "";
  const backgroundImageOfMobile = bgList?.[1] ?? "";
  const linkData = miscHeaderBlok?.link?.[0]?.link;
  const linkUrl = linkData?.cached_url ?? linkData?.url ?? "";
  const openInNewTab = linkData?.newTab ?? false;

  const backgroundStyle = {
    "--mobile-bg": backgroundImageOfMobile
      ? `linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.60) 100%), url(${backgroundImageOfMobile}) lightgray 50% / cover no-repeat`
      : "lightgray",
    "--desktop-bg": backgroundImage
      ? `linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.60) 100%), url(${backgroundImage}) lightgray 50% / cover no-repeat`
      : "lightgray",
  } as React.CSSProperties;

  return (
    <div
      className={cn(
        "relative flex h-[632px] w-full px-[25px] md:h-[480px] lg:h-[438px]",
        "[background:var(--mobile-bg)] md:[background:var(--desktop-bg)] lg:[background:var(--desktop-bg)]",
      )}
      style={backgroundStyle}
    >
      <div className="flex size-full flex-col pt-[75px]">
        <div className="flex flex-1 flex-col items-center justify-center text-center text-white">
          <Text
            tag="h1"
            style="mh1.5"
            mdStyle="h1"
            className="max-w-[325px] text-white md:max-w-[708px] lg:max-w-[894px] xl:max-w-[1062px]"
          >
            {miscHeaderBlok?.heading ?? ""}
          </Text>
          <Text
            tag="p"
            style="mb1"
            mdStyle="b1"
            className={cn(
              "mt-[28px] text-grays-G6",
              "max-w-[325px] md:max-w-[638px] lg:max-w-[712px] xl:max-w-[854px]",
              miscHeaderBlok?.add_cta ? "mb-4xl" : "",
            )}
          >
            {miscHeaderBlok?.body_content ?? ""}
          </Text>
          {miscHeaderBlok?.add_cta &&
            miscHeaderBlok?.button_label &&
            linkUrl && (
              <Button
                colour="white"
                theme="primary"
                link={linkData}
                isNew={openInNewTab}
              >
                {miscHeaderBlok.button_label}
              </Button>
            )}
        </div>
      </div>
    </div>
  );
};

export default MiscHeader;
