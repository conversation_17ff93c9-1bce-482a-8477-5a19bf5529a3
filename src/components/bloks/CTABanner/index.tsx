import { IStoryblokAssetProps, IStoryblokLink } from "@/common/types";
import { getResponsiveImageUrl } from "@/common/images";
import Image from "next/image";
import NextVideoWrapper from "@/components/ui/NextVideoWrapper";
import { storyblokEditable } from "@storyblok/react/rsc";
import Button from "@/components/ui/Button";
import Text from "@/components/ui/Text";

export interface ICtaBannerProps {
  heading: string;
  subheading: string;
  backgroundImage: IStoryblokAssetProps;
  backgroundVideo: IStoryblokAssetProps;
  anchorId: string;
  buttonLabel: string;
  buttonLink: IStoryblokLink;
  component: string;
}

interface Props {
  blok: ICtaBannerProps;
}

const CtaBanner = (props: Props) => {
  const { blok } = props;
  const {
    heading,
    subheading,
    backgroundImage,
    backgroundVideo,
    anchorId,
    buttonLabel,
    buttonLink,
  } = blok;


  return (
    <div
      id={anchorId}
      className="relative w-full px-xl py-[4.69rem] sm:py-32 lg:py-44"
      {...storyblokEditable({ ...props })}
    >
      <div className="relative z-[3] mx-auto max-w-[335px] sm:max-w-[490px]">
        <div className="text-center text-white">
          <Text
            style="mh1.5"
            mdStyle="h2"
            tag="h3"
            className="whitespace-pre-line"
          >
            {heading}
          </Text>
          <Text
            tag="h4"
            style="ph1"
            mdStyle="sh2"
            className="mt-[2px] whitespace-pre-line font-light italic"
          >
            {subheading}
          </Text>
        </div>
        <div className="mt-xl w-full text-center">
          <Button link={buttonLink} colour="red" theme="primary">
            {buttonLabel}
          </Button>
        </div>
      </div>

      {(backgroundImage?.filename || backgroundVideo?.filename) && (
        <div className="absolute inset-0 flex overflow-hidden">
          <div className="absolute inset-0 bg-black opacity-40 z-[2]" />

          {backgroundImage?.filename && (
            <>
              <Image
                src={getResponsiveImageUrl(
                  backgroundImage.filename,
                  1080,
                  400,
                  "quality(100)",
                )}
                width={1080}
                height={400}
                className="z-[1] size-full object-cover"
                sizes="100vw"
                alt={backgroundImage.alt}
                loading="eager"
                fetchPriority="high"
              />
            </>
          )}

          {backgroundVideo?.filename && (
            <NextVideoWrapper src={backgroundVideo.filename} />
          )}
        </div>
      )}
    </div>
  );
};

export default CtaBanner;
