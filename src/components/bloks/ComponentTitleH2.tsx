import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import { IStoryblokLinkProps } from "@/common/types";
import Button from "../ui/Button";
import GeneralLink from "./GeneralLink";

export interface IComponentTitleH2Props {
  preHeading: string;
  heading: string;
  buttonLabel: string;
  bodyContent: string;
  useCTA: boolean;
  link: IStoryblokLinkProps[];
  component: "componentTitleH2" | "componentTitleH3" | "componentTitleH4";
}

const ComponentTitleH2 = (props: IComponentTitleH2Props) => {
  const { preHeading, heading, buttonLabel, useCTA, bodyContent, link } = props;
  return (
    <section>
      <Container className="!pb-xl">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className=":max-w-[39.8rem] lg:max-w-[29.7rem] xl:max-w-[33.1rem]">
            <Text
              tag="p"
              style="q4"
              mdStyle="ph1"
              className="italic text-primary01-50"
            >
              {preHeading}
            </Text>
            <Text
              tag="h2"
              style="h3"
              mdStyle="h2"
              className="mt-2 text-primary01-75"
            >
              {heading}
            </Text>
            <Text
              tag="p"
              className="mt-sm whitespace-pre-wrap text-neutral01-75 md:mt-lg"
              style="mb1"
              mdStyle="b1"
            >
              {bodyContent}
            </Text>
          </div>
          {useCTA && link?.[0] && (
            <GeneralLink blok={link[0]}>
              <Button
                theme="secondary"
                colour="maroon"
                className="mt-3xl lg:mt-0"
              >
                {buttonLabel}
              </Button>
            </GeneralLink>
          )}
        </div>
      </Container>
    </section>
  );
};

export default ComponentTitleH2;
