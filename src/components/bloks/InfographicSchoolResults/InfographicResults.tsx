"use client";

import { IStoryblokAssetProps } from "@/common/types";
import { cn, generateImageAltFromFilename } from "@/common/utils";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/16/solid";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import HorizontalLine from "./HorrizontalLine";
import IconButton from "@/components/ui/IconButton";

interface IInfographicSchoolItem {
  school: string;
  body: string;
  image: IStoryblokAssetProps;
  spotlightText1: string;
  bodyContent1: string;
  spotlightText2: string;
  bodyContent2: string;
}

interface IInfographicSchoolResultsProps {
  blok: {
    language: string;
    icons: { schoolName?: string; shortName?: string; icon?: string }[];
    infographicSchoolItems: IInfographicSchoolItem[];
  };
}

const SECONDS_11 = 11000;
const SECONDS_5 = 8000;

export default function InfographicSchoolResults(
  props: IInfographicSchoolResultsProps,
) {
  const [index, setIndex] = useState(0);
  const [selection, setSelection] = useState<number | undefined>(undefined);
  const { infographicSchoolItems, icons } = props.blok;

  // timer
  useEffect(() => {
    const interval = selection === undefined ? SECONDS_5 : SECONDS_11;
    const timer = setTimeout(() => {
      if (selection !== undefined) {
        setSelection(undefined);
        return;
      }
      setIndex((index + 1) % infographicSchoolItems.length);
    }, interval);

    return () => {
      clearTimeout(timer);
    };
  }, [index, selection]);

  const selectOption = (i: number) => {
    setIndex(i);
    setSelection(i);
  };

  const handlePrev = () => {
    setIndex((i) => (i - 1) % infographicSchoolItems.length);
  };

  const handleNext = () => {
    setIndex((i) => (i + 1) % infographicSchoolItems.length);
  };

  return (
    <Container
      size="large"
      className="flex min-h-screen flex-col lg:min-h-none"
    >
      <div className="relative grid flex-1 grid-cols-1 grid-rows-1">
        {infographicSchoolItems.map((schoolDetails, i) => (
          <div
            className={cn(
              "col-start-1 row-start-1 grid w-full gap-[2.5rem] transition-opacity duration-1000 lg:grid-cols-2",
              i === index ? "opacity-1" : "opacity-0",
            )}
            key={`${schoolDetails.school}_${i}`}
          >
            <div className="w-full lg:max-w-[26.8125rem] xl:max-w-[31.25rem] 2xl:max-w-[36.875rem]">
              <Text
                tag="h3"
                style="sh4"
                mdStyle="sh3"
                className="mb-[1.88rem] text-primary01-75"
              >
                {icons?.[i]?.schoolName}
              </Text>
              <Text
                tag="p"
                style="b1"
                className="mb-4xl hidden text-neutral01-75 lg:block"
              >
                {schoolDetails.body}
              </Text>
              <SpotlightText
                spotlight={schoolDetails.spotlightText1}
                body={schoolDetails.bodyContent1}
              />
              <div className="my-3xl h-[0.0375rem] w-[8.0625rem] bg-grays-G5" />
              <SpotlightText
                spotlight={schoolDetails.spotlightText2}
                body={schoolDetails.bodyContent2}
              />
            </div>

            <Image
              width={800}
              height={800}
              src={schoolDetails.image.filename}
              alt={
                schoolDetails.image.alt ||
                generateImageAltFromFilename(schoolDetails.image.filename)
              }
              className="w-full object-contain md:h-[28.125rem] md:w-[35rem] lg:ml-auto lg:h-[23.90625rem] lg:w-[29.75rem] xl:h-[25.3125rem] xl:w-[31.5rem] 2xl:h-[28.125rem] 2xl:w-[35rem]"
            />
          </div>
        ))}
      </div>
      <div className="mt-4xl md:mt-[3.75rem]">
        <div className="mx-auto hidden w-auto justify-center md:flex">
          {icons?.map((schoolLogo, i) =>
            schoolLogo?.icon ? (
              <>
                <div
                  key={schoolLogo.schoolName}
                  onClick={() => selectOption(i)}
                  className={cn(
                    "relative flex max-w-screen-md flex-1 cursor-pointer items-center justify-center gap-md px-[1.31rem] py-4",
                  )}
                >
                  <HorizontalLine
                    isCurrent={i === index}
                    isSelectionMode={selection !== undefined}
                    timer={selection !== undefined ? SECONDS_11 : SECONDS_5}
                  />
                  <Image
                    width={200}
                    height={200}
                    src={schoolLogo.icon}
                    alt={generateImageAltFromFilename(schoolLogo.icon)}
                    className={cn(
                      "h-[2.125rem] w-[1.875rem] transition-[filter] duration-500",
                      i !== index &&
                        "opacity-45 brightness-50 contrast-50 grayscale saturate-50",
                    )}
                  />
                  <Text
                    tag="span"
                    style="b4"
                    className={cn(
                      "text-grays-G4 transition-colors duration-500",
                      i === index && "text-grays-G2",
                    )}
                  >
                    {schoolLogo.shortName}
                  </Text>
                  {i < icons.length - 1 && (
                    <div className="absolute right-0 h-full w-px bg-[linear-gradient(to_bottom,transparent_25%,#C0C0C0,transparent_75%)]" />
                  )}
                </div>
              </>
            ) : (
              <></>
            ),
          )}
        </div>
        <div className={cn("flex justify-end gap-md md:hidden")}>
          <IconButton
            colour={"maroon"}
            onClick={() => handlePrev()}
            Icon={ChevronLeftIcon}
          />
          <IconButton
            colour={"maroon"}
            onClick={() => handleNext()}
            Icon={ChevronRightIcon}
          />
        </div>
      </div>
    </Container>
  );
}

interface ISpotlightProps {
  spotlight: string;
  body: string;
}
const SpotlightText = ({ spotlight, body }: ISpotlightProps) => {
  return (
    <div className="grid gap-md">
      <Text tag="h3" style="h3" className="text-primary01-100">
        {spotlight}
      </Text>
      <Text tag="p" style="b2" className="text-neutral01-75">
        {body}
      </Text>
    </div>
  );
};
