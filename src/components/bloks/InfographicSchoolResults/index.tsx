import { fetchDataSource } from "@/common/storyblok";
import { ISbDataSourceResult, IStoryblokAssetProps } from "@/common/types";
import React from "react";
import InfographicResults from "./InfographicResults";

interface IInfographicSchoolItem {
  school: string;
  body: string;
  image: IStoryblokAssetProps;
  spotlightText1: string;
  bodyContent1: string;
  spotlightText2: string;
  bodyContent2: string;
}

interface IInfographicSchoolResultsProps {
  blok: {
    language: string;
    infographicSchoolItems: IInfographicSchoolItem[];
  };
}

const replaceLang = (lang: string) =>
  !lang || lang === "en" ? "short-name" : lang;

export default async function InfographicSchoolResults(
  props: IInfographicSchoolResultsProps,
) {
  const { infographicSchoolItems, language } = props.blok;
  const iconsResponse = (await fetchDataSource(
    "crimson-student-results",
    false,
    "image-url",
  )) as ISbDataSourceResult;

  const icons = iconsResponse?.data?.datasource_entries;

  const languageResponse = (await fetchDataSource(
    "crimson-student-results",
    false,
    replaceLang(language),
  )) as ISbDataSourceResult;
  const languageData = languageResponse?.data?.datasource_entries;

  const bottomLogos = infographicSchoolItems?.map((infoItem) => {
    const school = icons?.find(
      (dsItem) => dsItem.dimension_value === infoItem.school,
    );
    const translatedSchool = languageData?.find(
      (lItem) => lItem.name === school?.name,
    );

    return {
      schoolName: school?.name,
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      shortName: translatedSchool?.dimension_value || school?.name,
      icon: school?.dimension_value,
    };
  });

  return <InfographicResults blok={{ ...props.blok, icons: bottomLogos }} />;
}
