"use client";

import { cn } from "@/common/utils";
import { useAnimate } from "motion/react";
import React, { useEffect } from "react";

export default function HorizontalLine({
  timer,
  isCurrent,
  isSelectionMode,
}: {
  timer: number;
  isCurrent: boolean;
  isSelectionMode: boolean;
}) {
  const [redLine, animate] = useAnimate();

  useEffect(() => {
    if (isCurrent && !isSelectionMode) {
      animate(
        redLine.current,
        { width: "100%" },
        { duration: timer / 1000, ease: "easeInOut" },
      );

      return () => {
        animate(
          redLine.current,
          { width: "0" },
          { duration: 0.65, ease: "easeIn" },
        );
      };
    }
  }, [isCurrent, isSelectionMode]);

  return (
    <div
      className={cn(
        "absolute left-0 top-0 flex h-px w-full bg-grays-G5",
        isCurrent ? "" : "justify-end",
      )}
    >
      <div
        ref={redLine}
        className="h-px bg-primary01-50"
        style={{ width: isCurrent && isSelectionMode ? "100%" : "0%" }}
      />
    </div>
  );
}
