"use client";

import { cn } from "@/common/utils";
import { useAnimate } from "motion/react";
import React, { useEffect } from "react";

export default function SlidingLine({
  index,
  totalIndeces,
}: {
  index: number;
  totalIndeces: number;
}) {
  const [redLine, animate] = useAnimate();

  const width = 100 / totalIndeces;

  useEffect(() => {
    animate(
      redLine.current,
      { left: `${width * (index - 1)}%` },
      { duration: 0.3, ease: "easeInOut" },
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [index, totalIndeces]);

  return (
    <div className="relative h-px w-full bg-grays-G4">
      <div
        ref={redLine}
        className={cn(
          "relative bottom-full h-[2px] translate-y-[] bg-primary01-75 md:hidden",
        )}
        style={{
          width: `${width}%`,
        }}
      />
    </div>
  );
}
