import Text from "@/components/ui/Text";
import Container from "../ui/Container";
import { ISbDataSourceEntry } from "@/common/types";
import { ISbDataSourceResult } from "@/common/types";
import { fetchDataSource } from "@/common/storyblok";
import { Marquee as MagicUIMarquee } from "@/components/magicui/marquee";
import Image from "next/image";
import { storyblokEditable } from "@storyblok/react/rsc";

export interface ICompanyLogoCarouselProps {
  _uid: string;
  heading: string;
  component: string;
}

interface Props {
  blok: ICompanyLogoCarouselProps;
}

export default async function CompanyLogoCarousel({ blok }: Props) {
  const data: ISbDataSourceResult = await fetchDataSource(
    "alumni-employer-logos",
  );

  const globalCrimsonStudentResults =
    data?.data?.datasource_entries ?? ([] as ISbDataSourceEntry[]);

  return (
    <section className="bg-grays-G6" {...storyblokEditable({ blok })}>
      <Container className="!pb-0">
        <Text
          tag="h3"
          style="sh5"
          className="mx-auto text-center text-neutral01-100"
        >
          {blok.heading}
        </Text>
      </Container>

      <Container size="full" className="!pt-0">
        <MagicUIMarquee
          className="flex pb-0 pt-2xl"
          innerClassName="!gap-[8px] md:!gap-6"
        >
          {globalCrimsonStudentResults.map((item) => (
            <div
              key={item.name}
              className="flex w-[185px] items-center justify-center"
            >
              <Image
                src={item.value}
                alt={item.name}
                width={130}
                height={107}
                className="w-full"
              />
            </div>
          ))}
        </MagicUIMarquee>
      </Container>
    </section>
  );
}
