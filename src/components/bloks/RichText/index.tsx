import { RichEditor } from "@crimson-education/helios-editor-renderer";
import { SectionItem } from "@/common/types";

interface RichTextProps {
  blok: SectionItem;
}

const CrimsonRichText = ({ blok }: RichTextProps) => {
  if (!blok?.content) return null;
  return (
    <div className="prose-helios prose">
      <RichEditor value={blok.content} />
    </div>
  );
};

export default CrimsonRichText;
