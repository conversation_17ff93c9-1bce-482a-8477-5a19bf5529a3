import React from "react";
import Text from "@/components/ui/Text";
import { IMentorProfile } from "@/common/types";
import Image from "next/image";
import { cn } from "@/common/utils";

const MentorCard: React.FC<
  React.PropsWithChildren<{ className?: string } & IMentorProfile>
> = (props) => {
  const { className, Name, ProfilePicture, Byline, SmallBiography } = props;
  return (
    <div
      className={cn(
        "group relative flex h-[500px] w-full flex-col items-center rounded-[4px] md:max-h-max md:w-[200px]",
        className,
      )}
    >
      <div className="relative z-[1] size-full cursor-pointer bg-neutral01-50 bg-[radial-gradient(114.54%_83.43%_at_50.19%_5.09%,_#AE8C6F_0%,_#1D150E_100%)] md:h-[150px] md:min-h-[265px] md:rounded md:group-hover:rounded-b-none xl:h-[265px]">
        <div className="absolute bottom-7 size-full">
          <Image
            src={ProfilePicture?.filename}
            alt={Name}
            className="block max-h-full w-full rounded object-cover object-top group-hover:rounded-none md:max-h-[240px]"
            width={210}
            height={240}
          />
        </div>
        <div className="absolute bottom-0 z-[7] flex size-full flex-col gap-1 bg-gradient-to-t from-black from-10% to-transparent to-80% p-4 text-white md:p-3">
          <Text tag="p" style="sh6" className="mt-auto">
            {Name}
          </Text>
          <Text tag="p" style="q6">
            {Byline}
          </Text>
        </div>
      </div>
      <div
        className={cn(
          "h-24 rounded-b-[4px] bg-white text-body-p-2xs text-neutral01-75 md:h-auto md:origin-top",
          "md:max-h-0 md:scale-y-0 md:transform md:overflow-hidden md:transition-all md:duration-500 md:ease-in-out md:group-hover:max-h-40 md:group-hover:scale-y-100",
        )}
      >
        <div className="p-4 md:px-3 md:py-2">
          <Text tag="p" style="b3" mdStyle="b4">
            {SmallBiography}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default MentorCard;
