import React from "react";
import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import Button from "@/components/ui/Button";
import { AnimatedNumber } from "@/components/bloks/MentorsSection/AnimatedNumber";

const MentorCTA: React.FC<React.PropsWithChildren<IMentorCTA>> = (props) => {
  const {
    title,
    buttonLabel,
    universities = [],
    disableUniversities,
    locale,
  } = props;

  return (
    <div className="flex flex-col items-center">
      <Text
        tag="h3"
        style="mh1.5"
        mdStyle="h4"
        className="mb-6 whitespace-pre text-center text-white"
      >
        {title}
      </Text>
      {!disableUniversities && (
        <div className="flex w-full items-center">
          <AnimatedNumber universities={universities} />
        </div>
      )}
      <Button
        link={{
          cached_url: `${locale}/about-us/our-mentors`,
          linktype: "",
          url: "",
        }}
        theme="secondary"
        colour="white"
        className={cn("mt-8 xl:mt-8", disableUniversities && "mt-0")}
      >
        {buttonLabel}
      </Button>
    </div>
  );
};

interface IMentorCTA {
  title: string;
  universities?: string[];
  buttonLabel: string;
  disableUniversities?: boolean;
  locale: string;
}

export default MentorCTA;
