import React from "react";
import Image from "next/image";
import mentorsImage from "@/images/mentors_room.webp";
import { IMentorsSectionProps } from "./types";
import { getMentorsFromUuid, getTranslatedColleges } from "./utils";
import MentorCTA from "./MentorCta";
import SlideCarousel from "@/components/ui/SlideCarousel";
import MentorCard from "./MentorCard";
import MentorsSection from "./ClientComponent";

export default async function MentorsSectionWrapper(
  props: IMentorsSectionProps & { locale: string },
) {
  const { collegeNames, collegeNamesLanguage, mentors } = props;
  const perPage = 100;
  const untranslatedColleges = collegeNames.split(",");

  // get the schools for scroll animation
  const universities =
    (await getTranslatedColleges(
      untranslatedColleges,
      collegeNamesLanguage,
      perPage,
    )) ?? untranslatedColleges;
  // get profiles
  const collegeMentors = (await getMentorsFromUuid(mentors)) ?? [];

  return (
    <>
      {/* The mobile section is server rendered*/}
      <div className="relative min-h-screen w-full overflow-hidden md:hidden">
        <div className="absolute -z-10 size-full">
          <div className="absolute inset-0 z-[1] bg-black/20" />
          <Image
            src={mentorsImage}
            alt=""
            className="absolute top-0 -z-10 size-full object-cover"
          />
        </div>
        <div className="block w-full pt-[30px] md:hidden">
          <MentorCTA
            title={props.heading}
            buttonLabel={props.buttonLabel}
            locale={props.locale}
            disableUniversities
          />
          <SlideCarousel transparent colour="white">
            {collegeMentors?.map((card, index) => (
              <MentorCard
                key={`mbl_crd_${index}}`}
                {...card}
                className="even:ml-auto"
              />
            ))}
          </SlideCarousel>
        </div>
      </div>
      {/* Desktop version of site */}
      <MentorsSection
        {...props}
        universities={universities}
        collegeMentors={collegeMentors}
      />
    </>
  );
}
