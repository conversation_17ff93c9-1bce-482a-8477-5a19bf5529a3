import { fetchDataSource, fetchStoryblokStories } from "@/common/storyblok";
import { IMentorProfile, IStory } from "@/common/types";
import { ACCEPTANCE_DATASET, NAME_DIMENSION } from "./constants";

export const getMentorsFromUuid = async (uuids: string[]) => {
  const { data } = await fetchStoryblokStories<IStory<IMentorProfile>>({
    uuids: uuids,
    draftMode: true,
  });

  return data?.stories?.map(({ content }) => content) ?? [];
};

interface SbDataValue {
  data: {
    datasource_entries?: {
      dimension_value?: string;
      id: number;
      name: string;
      value: string;
    }[];
  };
}

export const getTranslatedColleges = async (
  colleges: string[],
  collegeNamesLanguage: string,
  perPage: number,
) => {
  const response = (await fetchDataSource(
    ACCEPTANCE_DATASET,
    false,
    collegeNamesLanguage === "en" || !collegeNamesLanguage
      ? NAME_DIMENSION
      : collegeNamesLanguage,
    perPage,
  )) as SbDataValue;

  const nameData = response?.data?.datasource_entries;
  const translatedNames = colleges.map((country) => {
    const translatedCountry = nameData?.find(({ name }) => name === country);

    return translatedCountry?.dimension_value ?? country;
  });

  return translatedNames ?? [];
};
