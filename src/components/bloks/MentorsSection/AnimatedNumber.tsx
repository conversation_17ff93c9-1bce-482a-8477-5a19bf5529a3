"use client";

import { AnimatePresence, motion } from "motion/react";
import { useState, useEffect } from "react";

const variants = {
  initial: {
    opacity: 0,
    y: 100,
  },
  visible: { opacity: 1, y: 0 },
  leaving: { opacity: 0, y: -90 },
};

export const AnimatedNumber: React.FC<
  React.PropsWithChildren<{ universities: string[] }>
> = (props) => {
  // const { locale } = usePageContext();
  const { universities } = props;
  const [universityIndex, setUniversityIndex] = useState<number>(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setUniversityIndex((index) => {
        return (index + 1) % universities.length;
      });
    }, 3000);

    return () => {
      clearInterval(interval);
    };
  }, [universities]);

  const university = universities[universityIndex];

  return (
    <div
      className="relative flex h-[180px] w-full items-center justify-center"
      style={{
        WebkitMaskImage:
          "linear-gradient(to bottom, transparent, black 16%, black 84%, transparent)",
        maskImage:
          "linear-gradient(to bottom, transparent, black 16%, black 84%, transparent)",
      }}
    >
      <AnimatePresence mode="popLayout">
        <motion.div
          key={university}
          className="flex w-full items-center justify-center text-center font-display-sans text-[4.375rem] font-bold !leading-[110%]"
          variants={variants}
          initial="initial"
          animate="visible"
          exit={"leaving"}
          transition={{ duration: 0.5, delay: 0.3, ease: "easeInOut" }}
        >
          {university}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};
