"use client";
import React, { useEffect, useRef, useState } from "react";
import { motion, MotionValue, useScroll, useTransform } from "motion/react";
import Image from "next/image";
import mentorsImage from "@/images/mentors_room.webp";
import Container from "@/components/ui/Container";
import { cn } from "@/common/utils";
import { IMentorProfile } from "@/common/types";
import { IMentorsSectionProps } from "./types";
import MentorCTA from "./MentorCta";
import MentorCard from "./MentorCard";

interface iAdditionalProps {
  universities: string[];
  collegeMentors: IMentorProfile[];
}

export default function MentorsSection(
  props: React.PropsWithChildren<IMentorsSectionProps & iAdditionalProps>,
) {
  const { heading, buttonLabel, universities, collegeMentors, locale } = props;
  const [isMobile, setIsMobile] = useState(false);
  const [pageHeight, setPageHeight] = useState(1024);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    setPageHeight(window.innerHeight);
    window.addEventListener("resize", handleResize);
    handleResize(); // Initial check

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const sectionRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });
  const zoomScroll = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"],
  });

  // we want to create an 'overscroll' to have parrallax impression
  const cardHeight = pageHeight * 0.68;
  const cardTotalHeight =
    Math.ceil(collegeMentors.length / 2) * cardHeight + 200;
  const scrollProgress = useTransform(
    scrollYProgress,
    [0, 1],
    [pageHeight, -cardTotalHeight],
  );
  const zoomAmount = useTransform(
    zoomScroll.scrollYProgress,
    [0, 0.2, 0.8, 1],
    [1, 1.075, 1.075, 1],
  );

  const leftData = collegeMentors.filter((_, index) => index % 2 === 0);
  const rightData = collegeMentors.filter((_, index) => index % 2 === 1);

  return (
    <div
      className="relative hidden w-full bg-cover text-white md:block"
      style={{
        height: isMobile ? undefined : `calc(${cardTotalHeight}px + 200vh)`,
      }}
      ref={sectionRef}
    >
      <motion.div className="top-0 w-full overflow-hidden md:sticky md:h-screen">
        <motion.div
          className="absolute -z-10 size-full"
          style={{ scale: isMobile ? undefined : zoomAmount }}
        >
          <div className="absolute inset-0 z-[1] bg-black/20" />
          <Image
            src={mentorsImage}
            alt=""
            className="absolute top-0 -z-10 size-full object-cover"
          />
        </motion.div>
        <Container className="hidden min-h-screen w-full grid-cols-[20%,60%,20%] items-center md:grid lg:grid-cols-[25%,_50%,_25%]">
          <CardContainer
            style={{ y: scrollProgress }}
            className="relative pt-[300px] [&>*:nth-child(odd)]:ml-auto"
            cardHeight={cardHeight}
          >
            {/* left */}
            {leftData?.map((card, index) => (
              <MentorCard key={`1_crd_${index}}`} {...card} />
            ))}
          </CardContainer>
          <div className="sticky top-0 size-full px-4 pt-[10vh] md:pt-[15vh]">
            <MentorCTA
              title={heading}
              buttonLabel={buttonLabel}
              universities={universities}
              locale={locale}
            />
          </div>
          <CardContainer
            style={{ y: scrollProgress }}
            className="relative [&>*:nth-child(even)]:ml-auto"
            cardHeight={cardHeight}
          >
            {/* right */}
            {rightData?.map((card, index) => (
              <MentorCard key={`2_crd_${index}}`} {...card} />
            ))}
          </CardContainer>
        </Container>
      </motion.div>
    </div>
  );
}

interface IContainerProps {
  className?: string;
  style?: {
    y?: MotionValue<number>;
    x?: MotionValue<number>;
    z?: MotionValue<number>;
  };
  cardHeight: number;
  children: React.ReactNode;
}

const CardContainer: React.FC<IContainerProps> = ({
  className = "",
  cardHeight,
  ...props
}) => (
  <motion.div className={cn("h-full w-full", className)} {...props}>
    {React.Children.map(props.children, (child) => (
      <div
        className="flex w-max max-w-full justify-start"
        style={{ height: cardHeight }}
      >
        {child}
      </div>
    ))}
  </motion.div>
);
