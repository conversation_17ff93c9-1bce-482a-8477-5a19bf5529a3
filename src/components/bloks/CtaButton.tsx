import { type SbBlokData } from "@storyblok/react/rsc";
import type { IStoryblokLinkProps } from "@/common/types";
import Button from "@/components/ui/Button";
import { getSafeButtonColour, getSafeButtonTheme } from "@/common/buttonType";

interface Props extends SbBlokData {
  blok: {
    colour: string;
    appearance: string;
    link: IStoryblokLinkProps[];
    text: string;
  };
}

const CtaButton = ({ blok }: Props) => {
  const { colour, appearance, link, text } = blok;
  const _Link = link[0]?.link;
  const isNewTab = link[0]?.newTab;
  const targetAnchorId = link[0]?.targetAnchorId;

  const safeColour = getSafeButtonColour(colour);
  const safeTheme = getSafeButtonTheme(appearance);

  return (
    <Button
      colour={safeColour}
      theme={safeTheme}
      link={_Link}
      openInNewTab={isNewTab}
      targetAnchorId={targetAnchorId}
      className="my-1"
    >
      {text}
    </Button>
  );
};

export default CtaButton;
