import { IStoryblokAssetProps } from "@/common/types";

export interface ICaseStudyHeaderBlok {
  component: "caseStudyHeader";
  heading: string;
  overlay: string;
  location: string;
  academics: string;
  acceptedTo: string;
  activities: string;
  background: string;
  bioHeading: string;
  bodyContent: string;
  futurePlans: string;
  studentName: string;
  targetField: string;
  studentImage: IStoryblokAssetProps;
  bioSubHeading: string;
  bioBodyContent: {
    content: any;
  }[];
}

export interface CaseStudyHeaderProps {
  pageTitle?: string;
  fullSlug?: string;
  caseStudyHeaderBlok: ICaseStudyHeaderBlok;
}
