"use client";
import Text from "@/components/ui/Text";
import { useLabelTranslation } from "@/common/hooks/useTranslation";

import { CaseStudyHeaderProps } from "./types";

const AcceptToAtTablet: React.FC<CaseStudyHeaderProps> = ({
  caseStudyHeaderBlok,
}) => {
  const { t } = useLabelTranslation();
  return (
    <>
      <div className="flex w-full flex-col justify-start pb-[12px] pt-[30px] md:w-[274px] lg:hidden xl:hidden 2xl:hidden">
        <Text tag="h5" style="h5">
          {t(`Accepted to`)}
        </Text>
        <Text tag="p" style="b1">
          {caseStudyHeaderBlok.acceptedTo}
        </Text>
      </div>
    </>
  );
};

export default AcceptToAtTablet;
