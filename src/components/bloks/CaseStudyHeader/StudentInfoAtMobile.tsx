"use client";
import Text from "@/components/ui/Text";
import MarkdownSection from "@/components/bloks/MarkdownSection";
import { useLabelTranslation } from "@/common/hooks/useTranslation";

import { CaseStudyHeaderProps } from "./types";

const StudentInfoAtMobile: React.FC<CaseStudyHeaderProps> = ({
  caseStudyHeaderBlok,
}) => {
  const { t } = useLabelTranslation();
  return (
    <>
      <div>
        <Text tag="h5" style="h5">
          {t(`Accepted to`)}
        </Text>
        <Text tag="p" style="b1" className="text-neutral01-75">
          {caseStudyHeaderBlok.acceptedTo}
        </Text>
      </div>
      <div>
        <Text tag="h5" style="h5">
          {t(`Target Field`)}
        </Text>
        <Text tag="p" style="b1" className="text-neutral01-75">
          {caseStudyHeaderBlok.targetField}
        </Text>
      </div>
      <div>
        <Text tag="h5" style="h5">
          {t(`Academics`)}
        </Text>
        <MarkdownSection
          blok={{
            bodyContent: caseStudyHeaderBlok.academics,
          }}
          className="!my-0 !text-neutral01-75"
          listStyle="dot"
        />
      </div>
      <div>
        <Text tag="h5" style="h5">
          {t(`Activities`)}
        </Text>
        <MarkdownSection
          blok={{
            bodyContent: caseStudyHeaderBlok.activities,
          }}
          className="!my-0 !text-neutral01-75"
          listStyle="dot"
        />
      </div>
      <div>
        <Text tag="h5" style="h5">
          {t(`Location`)}
        </Text>
        <Text tag="p" style="b1" className="text-neutral01-75">
          {caseStudyHeaderBlok.location}
        </Text>
      </div>
      <div>
        <Text tag="h5" style="h5">
          {t(`Future Plans`)}
        </Text>
        <Text tag="p" style="b1" className="text-neutral01-75">
          {caseStudyHeaderBlok.futurePlans}
        </Text>
      </div>
    </>
  );
};

export default StudentInfoAtMobile;
