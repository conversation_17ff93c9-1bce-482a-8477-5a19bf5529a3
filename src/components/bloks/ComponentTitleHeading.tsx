import { IStoryblokLinkProps } from "@/common/types";
import Button from "../ui/Button";
import Text, { TTag } from "../ui/Text";
import { cva } from "class-variance-authority";
import Container from "../ui/Container";

type Props = {
  blok: {
    preHeading: string;
    heading: string;
    buttonLabel: string;
    bodyContent: string;
    useCTA: boolean;
    component: "componentTitleH3" | "componentTitleH2" | "componentTitleH4";
    link?: IStoryblokLinkProps[];
  };
};

const ComponentTitleHeading = ({ blok }: Props) => {
  const {
    preHeading,
    heading,
    buttonLabel,
    bodyContent,
    useCTA,
    link,
    component,
  } = blok;

  const componentToTagMap: Record<
    "componentTitleH2" | "componentTitleH3" | "componentTitleH4",
    TTag
  > = {
    componentTitleH2: "h2",
    componentTitleH3: "h3",
    componentTitleH4: "h4",
  };

  const ctaLink = link?.[0];

  const headerStyleToComponent: Record<
    "componentTitleH2" | "componentTitleH3" | "componentTitleH4",
    "mh1.5" | "h4"
  > = {
    componentTitleH2: "mh1.5",
    componentTitleH3: "mh1.5",
    componentTitleH4: "h4",
  };

  const headerMdStyleToComponent: Record<
    "componentTitleH2" | "componentTitleH3" | "componentTitleH4",
    "h2" | "h3" | "h4"
  > = {
    componentTitleH2: "h2",
    componentTitleH3: "h3",
    componentTitleH4: "h4",
  };

  return (
    <Container className="!pb-0">
      <div className="mb-[52px] flex w-full flex-col justify-between gap-10 lg:flex-row lg:items-end">
        <div className="md:max-w-[75%] lg:max-w-[60%] xl:max-w-[55%] 2xl:max-w-[50%]">
          {preHeading && (
            <Text
              tag="h3"
              style="sh5"
              mdStyle="ph1"
              className="mb-2 italic text-primary01-50"
            >
              {preHeading}
            </Text>
          )}
          {heading && (
            <Text
              tag={componentToTagMap[component]}
              style={headerStyleToComponent[component]}
              mdStyle={headerMdStyleToComponent[component]}
              className={headingVariants({
                component: component as any,
                hasBodyContent: !!bodyContent,
              })}
            >
              {heading}
            </Text>
          )}
          {bodyContent && (
            <Text
              tag="h3"
              style="mb1"
              mdStyle="b1"
              className="text-neutral01-75"
            >
              {bodyContent}
            </Text>
          )}
        </div>
        {useCTA && link && (
          <Button
            theme="secondary"
            colour="maroon"
            link={ctaLink?.link}
            targetAnchorId={ctaLink?.targetAnchorId}
          >
            {buttonLabel}
          </Button>
        )}
      </div>
    </Container>
  );
};

const headingVariants = cva("font-display-sans", {
  variants: {
    component: {
      componentTitleH2: "text-primary01-75 text-mh1.5 md:text-h2 mb-4",
      componentTitleH3: "text-grays-G1 text-mh1.5 md:text-h3 mb-4",
      componentTitleH4: "text-grays-G1 text-h4 mb-6",
    },
    hasBodyContent: {
      true: "",
      false: "mb-0",
    },
  },
  defaultVariants: {
    hasBodyContent: false,
    component: "componentTitleH2",
  },
});

export default ComponentTitleHeading;
