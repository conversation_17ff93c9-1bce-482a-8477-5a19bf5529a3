import { StoryblokComponent, storyblokEditable } from "@storyblok/react/rsc";
import { TBackgroundColour, Theme } from "@/common/types";
import SideForm from "@/components/ui/SideForm";
import { cn } from "@/common/utils";
import Container from "../ui/Container";

interface Props {
  blok: {
    backgroundColour: TBackgroundColour;
    bodyImage: [{ _uid: string }];
    flipLayout: boolean;
    flipMobileLayout: boolean;
    dataTestId: string;
    sectionIndex: number;
    anchorId: string;
    _uid: string;
    formComponent: any[];
    heading: string;
    subheading: string;
    theme: Theme;
  };
  sectionIndex: number;
}

const SideImageForm = ({ blok }: Props) => {
  const {
    bodyImage,
    flipLayout,
    flipMobileLayout,
    anchorId,
    _uid,
    formComponent = [],
    heading = "",
    subheading = "",
  } = blok;

  return (
    <section id={anchorId} {...storyblokEditable(blok)} className="bg-grays-G6">
      <Container className="mx-auto grid grid-cols-1 gap-3xl">
        <div
          id="standard-web-lead-form"
          className={cn(
            "flex items-center gap-y-[46px] xl:flex-row xl:items-start xl:gap-x-20",
            flipLayout ? "xl:flex-row-reverse" : "xl:flex-row",
            flipMobileLayout ? "flex-col-reverse" : "flex-col",
          )}
        >
          <div className="max-w-[740px] px-5 py-8 xl:w-1/2">
            {blok.bodyImage?.length > 0 && (
              <div className="w-full max-w-[650px] xl:max-w-none">
                <StoryblokComponent
                  blok={bodyImage[0]}
                  key={bodyImage[0]._uid}
                  fit="contain"
                  sizes="(min-width: 768px) 740px, 100vw"
                />
              </div>
            )}
          </div>

          <div className="w-full xl:w-1/2 xl:rounded-2xl">
            {formComponent?.length > 0 && (
              <SideForm
                formId={_uid}
                {...{ heading, subheading, formComponent }}
              />
            )}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default SideImageForm;
