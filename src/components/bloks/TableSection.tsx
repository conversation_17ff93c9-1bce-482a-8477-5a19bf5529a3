import React from "react";
import { cn } from "@/common/utils";

interface TableCol {
  _uid: string;
  value: string;
  component: "_table_col";
  _editable?: string;
}

interface TableRow {
  _uid: string;
  component: "_table_row";
  body: TableCol[];
  _editable?: string;
}

interface TableHeader {
  value: string;
}

interface TableData {
  _uid: string;
  plugin: string;
  thead: TableHeader[];
  tbody: TableRow[];
}

interface TableSectionBlok {
  _uid: string;
  table: TableData;
  tableTitle?: string;
  tableCaption?: string;
  tableColourTheme?: string;
  tableTextTheme?: string;
  tabletRenderType?: string;
  tableHeaderAlignment?: "left" | "center" | "right";
  tableContentAlignment?: "left" | "center" | "right";
  anchorId?: string;
  abTestType?: string;
  enableABTest?: boolean;
  component: "tableSection";
  _editable?: string;
}

interface Props {
  blok: TableSectionBlok;
}

const TableSection = ({ blok }: Props) => {
  const { tableTitle, table, tableCaption } = blok;

  return (
    <div className="my-8 w-full">
      {tableTitle && (
        <h3 className="mb-1 font-display-serif text-3xl font-light leading-loose text-black">
          {tableTitle}
        </h3>
      )}

      <div className="border-gray-200 overflow-y-scroll max-h-[70vh] overflow-x-auto rounded-md">
        <table className="min-w-full border-separate border-spacing-0">
          <thead className="sticky top-0 bg-neutral01-25 text-sm font-normal text-neutral01-75">
            {tableCaption && (
              <tr>
                <th
                  colSpan={table?.thead.length}
                  className="border-b border-white bg-neutral01-25 px-4 py-3 text-center font-body-single text-sm font-bold leading-tight text-neutral01-75"
                >
                  {tableCaption}
                </th>
              </tr>
            )}
            <tr>
              {table?.thead?.map((header, index) => (
                <th
                  key={`${header.value}-${index}`}
                  className={cn(
                    "border-r border-neutral01-25 px-4 py-3 font-body-single text-sm font-normal leading-none text-neutral01-75 last:border-r-0",
                    index === 0 ? "text-left" : "text-center",
                  )}
                >
                  {header.value}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {table?.tbody?.map((row, rowIndex) => (
              <tr
                key={row._uid}
                className={rowIndex % 2 === 0 ? "bg-white" : "bg-neutral01-0"}
              >
                {row.body.map((col, colIndex) => (
                  <td
                    key={col._uid}
                    className={cn(
                      "text-gray-900 border-r border-neutral01-25 px-4 py-3 align-middle font-body-single text-sm font-normal leading-none text-neutral01-75 last:border-r-0",
                      colIndex === 0 ? "text-left" : "text-center",
                    )}
                  >
                    {col.value}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TableSection;
