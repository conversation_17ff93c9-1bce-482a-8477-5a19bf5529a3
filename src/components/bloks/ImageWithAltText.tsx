import { storyblokEditable } from "@storyblok/react/rsc";
import ImageWrapper from "@/components/ui/ImageWrapper";

interface Props {
  blok: {
    image: string;
    altText: string;
  };
  flip?: boolean;
  fit?: "contain" | "cover";
  size?: "normal" | "full";
  sizes?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  className?: string;
}

const ImageWithAltText = ({
  blok,
  flip = false,
  fit,
  size,
  width,
  height,
  priority,
  className,
  sizes,
}: Props) => {
  const imageProps = {
    sizes,
    width,
    height,
    flip,
    fit,
    size,
    priority,
    className,
  };
  return (
    <ImageWrapper
      src={blok.image}
      alt={blok.altText}
      {...imageProps}
      {...storyblokEditable(blok)}
    />
  );
};

export default ImageWithAltText;
