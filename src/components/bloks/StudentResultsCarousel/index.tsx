import { Suspense, lazy } from "react";
import Container from "@/components/ui/Container";
import { storyblokEditable } from "@storyblok/react/rsc";
import { cn } from "@/common/utils";
import { IStudentResultsCarouselProps } from "./types";
import { fetchDataSource } from "@/common/storyblok";
import { ISbDataSourceEntry, ISbDataSourceResult } from "@/common/types";
import ServerBasedRows from "./ServerBasedRows";

const MarqueeRows = lazy(() => import("./MarqueeRows"));

interface Props {
  blok: IStudentResultsCarouselProps;
  className?: string;
}

export default async function StudentResultsCarousel({
  blok,
  className,
}: Props) {
  try {
    const {
      anchorId,
      numberOfRows = "1",
      helpText = "{COUNT} Offers to {COLLEGE}",
      language = "en",
    } = blok;

    const data: ISbDataSourceResult = await fetchDataSource(
      "crimson-student-results",
      false,
      language ? language : "short-name",
    );

    const globalCrimsonStudentResults =
      data?.data?.datasource_entries ?? ([] as ISbDataSourceEntry[]);

    return (
      <div
        id={anchorId ?? "student-results-carousel"}
        className={cn(
          "pb-[0.3125rem] pt-[1.5625rem] md:pb-0 md:pt-[1.88rem]",
          className,
        )}
        {...storyblokEditable({ blok })}
      >
        <Container className="max-w-full p-0 md:p-0" size="full">
          <Suspense
            fallback={
              <ServerBasedRows
                globalCrimsonStudentResults={globalCrimsonStudentResults}
                numberOfRows={numberOfRows}
                helpText={helpText}
              />
            }
          >
            <MarqueeRows
              globalCrimsonStudentResults={globalCrimsonStudentResults}
              numberOfRows={numberOfRows}
              helpText={helpText}
            />
          </Suspense>
        </Container>
      </div>
    );
  } catch (error) {
    console.error("error in StudentResultsCarousel", error);
    return null;
  }
}
