import { Marquee as Magic<PERSON><PERSON>arque<PERSON> } from "@/components/magicui/marquee";
import { INormalizedStudentResults } from "./types";
import { getItemsPerRow } from "./utils";
import StudentResultItem from "./StudentResultItem";
import CarouselItemDividerIcon from "./CarouselItemDividerIcon";
import { ISbDataSourceEntry } from "@/common/types";

type Props = {
  globalCrimsonStudentResults: ISbDataSourceEntry[];
  numberOfRows: string;
  helpText: string;
};

const ServerBasedRows = (props: Props) => {
  const { globalCrimsonStudentResults, numberOfRows, helpText } = props;
  let rowCount = parseInt(numberOfRows, 10);
  rowCount = rowCount > 3 ? 3 : rowCount;

  const normalizedStudentResults = globalCrimsonStudentResults.map(
    ({ name, value, dimension_value }) => {
      return {
        localisedName: dimension_value,
        heading: value,
        subheading: name,
      };
    },
  ) as INormalizedStudentResults[];

  const itemsInRows = getItemsPerRow(normalizedStudentResults, rowCount);
  return (
    <div>
      {itemsInRows.map((row, index) => (
        <MagicUIMarquee
          key={`${row[0]?.subheading}-${row[0]?.heading}`}
          reverse={index % 2 === 0 ? true : false}
          className="p-0 [--duration:75s]"
        >
          {row.map((item) => (
            <div
              className="relative flex justify-between"
              key={`${item.subheading}-${item.heading}-${index}`}
            >
              <StudentResultItem helpText={helpText} {...item} />
              <span className="absolute right-0 top-[7px] flex h-[32px] w-[2px] items-center justify-center">
                <CarouselItemDividerIcon />
              </span>
            </div>
          ))}
        </MagicUIMarquee>
      ))}
    </div>
  );
};

export default ServerBasedRows;
