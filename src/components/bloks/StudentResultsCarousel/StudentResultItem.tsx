import { formatHelpText } from "./utils";

const StudentResultItem = (props: {
  localisedName?: string;
  heading: string;
  subheading: string;
  helpText: string;
}) => {
  const { beforeCount, count, afterCount } = formatHelpText(
    props.helpText,
    props.heading,
    props.localisedName ?? props.subheading,
  );

  const isDefaultHelpTextVariant =
    props?.helpText === "{COUNT} Offers to {COLLEGE}" ? true : false;

  const [beforeOffers, afterOffers] = isDefaultHelpTextVariant
    ? (afterCount?.split("Offers to") ?? [null, null])
    : [null, null];

  return (
    <div className="px-xl pb-xl md:px-[1.93rem] md:pb-[1.88rem]">
      <p className="flex items-center justify-between font-display-sans text-sans-base text-neutral01-100 md:text-sans-xl">
        {beforeCount && <span className="">{beforeCount}</span>}
        <span className="mx-[12px] text-sans-3xl font-bold text-primary01-50 md:text-sans-5xl">
          {count}
        </span>
        {isDefaultHelpTextVariant && (
          <span className="">
            {beforeOffers}Offers to
            <br className="md:hidden" />
            {afterOffers}
          </span>
        )}
        {!isDefaultHelpTextVariant && <span className="">{afterCount}</span>}
      </p>
    </div>
  );
};

export default StudentResultItem;
