import { INormalizedStudentResults } from "./types";

export const formatHelpText = (
  helpText: string,
  heading: string,
  subheading: string,
) => {
  if (
    !helpText.includes("{COLLEGE}") ||
    !helpText.includes("{COUNT}") ||
    !helpText
  ) {
    helpText = "{COUNT} Offers to {COLLEGE}";
  }

  const textWithCollege = helpText.replace("{COLLEGE}", subheading);
  const [beforeCount, afterCount] = textWithCollege.split("{COUNT}");

  return {
    beforeCount,
    count: heading,
    afterCount,
  };
};

export const getItemsPerRow = (
  normalizedStudentResults: INormalizedStudentResults[],
  rowCount: number,
) => {
  const quotient = Math.floor(normalizedStudentResults.length / rowCount);
  let remainder = normalizedStudentResults.length % rowCount;
  const itemCountPerRow = [];
  for (let i = 0; i < rowCount; i++, remainder--) {
    itemCountPerRow[i] = quotient + (remainder > 0 ? 1 : 0);
  }

  const allItems = [...normalizedStudentResults];
  const itemsInRows = [];
  for (const count of itemCountPerRow) {
    itemsInRows.push(allItems.splice(0, count));
  }
  return itemsInRows;
};
