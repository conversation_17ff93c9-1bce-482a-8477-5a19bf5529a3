"use client";

import { useEffect, useState } from "react";
import { Marquee as MagicUIMarquee } from "@/components/magicui/marquee";
import { INormalizedStudentResults } from "./types";
import { getItemsPerRow } from "./utils";
import StudentResultItem from "./StudentResultItem";
import CarouselItemDividerIcon from "./CarouselItemDividerIcon";
import { ISbDataSourceEntry } from "@/common/types";

type Props = {
  globalCrimsonStudentResults: ISbDataSourceEntry[];
  numberOfRows: string;
  helpText: string;
};

function useIsSmallDevice() {
  const [isSmallDevice, setIsSmallDevice] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width: 768px)");

    // Set initial value
    setIsSmallDevice(mediaQuery.matches);

    // Create event listener
    const handleResize = (e: MediaQueryListEvent) => {
      setIsSmallDevice(e.matches);
    };

    // Add listener
    mediaQuery.addEventListener("change", handleResize);

    // Cleanup
    return () => mediaQuery.removeEventListener("change", handleResize);
  }, []);

  return isSmallDevice;
}

const MarqueeRows = (props: Props) => {
  const { globalCrimsonStudentResults, numberOfRows, helpText } = props;

  const isSmallDevice = useIsSmallDevice();

  const normalizedStudentResults = globalCrimsonStudentResults.map(
    ({ name, value, dimension_value }) => {
      return {
        localisedName: dimension_value,
        heading: value,
        subheading: name,
      };
    },
  ) as INormalizedStudentResults[];

  const parsedNumOfRows = parseInt(numberOfRows, 10);
  let rowCount = isSmallDevice && parsedNumOfRows > 1 ? 2 : parsedNumOfRows;
  rowCount = rowCount > 3 ? 3 : rowCount;

  const itemsInRows = getItemsPerRow(normalizedStudentResults, rowCount);

  return (
    <div>
      {itemsInRows.map((row, index) => (
          <MagicUIMarquee
            key={`${row[0]?.subheading}-${row[0]?.heading}`}
            reverse={index % 2 === 0 ? true : false}
            className="p-0 [--duration:75s]"
          >
            {row.map((item) => (
              <div
                className="relative flex justify-between"
                key={`${item.subheading}-${item.heading}-${index}`}
              >
                <StudentResultItem helpText={helpText} {...item} />
                <span className="absolute right-0 top-[7px] flex h-[32px] w-[2px] items-center justify-center">
                  <CarouselItemDividerIcon />
                </span>
              </div>
            ))}
          </MagicUIMarquee>
      ))}
    </div>
  );
};

export default MarqueeRows;
