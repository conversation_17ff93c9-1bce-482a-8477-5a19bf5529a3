import CaseStudyCard from "@/components/ui/CaseStudy/CaseStudyCard";
import SlideCarousel from "@/components/ui/SlideCarousel";
import { ICaseStudyProps, ICaseStudySection } from "./types";
import ComponentTitleHeading from "../ComponentTitleHeading";
import { fetchStoriesByUuids } from "@/common/storyblok";

const CaseStudiesSection = async (props: ICaseStudyProps) => {
  try {
    const { section, transparent = false } = props;

    const draftMode =
      process.env.NEXT_PUBLIC_BUILD_ENV === "prod" ? false : true;
    const {
      testimonials,
      nameLabel,
      majorLabel,
      acceptToLabel,
      readTestimonialStoryLabel,
      title,
    } = section;

    const header = title && title.length > 0 ? title[0] : null;

    const testimonialUUIDList = testimonials.map(
      (testimonial) => testimonial.testimonialPage,
    );

    const { data } = await fetchStoriesByUuids(testimonialUUIDList, draftMode);

    testimonials.forEach((t) => {
      const testimonial = data.stories.find(
        (s) => s.uuid === t.testimonialPage,
      );
      t.testimonialPageSlug = testimonial?.full_slug;
    });

    return (
      <div>
        {header && <ComponentTitleHeading blok={header} />}
        <SlideCarousel transparent={transparent} wrapperClassName="!pt-0">
          {testimonials.map((testimonial, index) => {
            return (
              <CaseStudyCard
                testimonial={testimonial}
                key={index}
                nameLabel={nameLabel}
                majorLabel={majorLabel}
                acceptToLabel={acceptToLabel}
                readTestimonialStoryLabel={readTestimonialStoryLabel}
              />
            );
          })}
        </SlideCarousel>
      </div>
    );
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const WrappedCaseStudiesSection = async ({
  blok,
}: {
  blok: ICaseStudySection;
}) => {
  return <CaseStudiesSection section={blok} />;
};

export default CaseStudiesSection;
