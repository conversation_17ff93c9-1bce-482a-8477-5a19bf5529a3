export interface ICaseStudySection {
  heading: string;
  preHeading: string;
  buttonLabel: string;
  nameLabel: string;
  majorLabel: string;
  acceptToLabel: string;
  readTestimonialStoryLabel: string;
  testimonials: ITestimonial[];
  title: any[];
}

export interface ITestimonial {
  image: {
    filename: string;
  };
  typedName: string;
  typedMajor: string;
  testimonialPage: string;
  college: string;
  testimonialSummary: string;
  testimonialPageSlug?: string;
}

export interface ICaseStudyProps {
  section: ICaseStudySection;
  transparent?: boolean;
}
