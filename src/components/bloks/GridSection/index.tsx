import React from "react";
import { IGridSectionComponentProps } from "./types";
import GridSection from "./GridSection";
import { fetchStoriesByUuids } from "@/common/storyblok";

export default async function GridSectionServer(
  props: IGridSectionComponentProps,
) {
  const { gridItems } = props.blok;

  const results: Record<string, string> = {};

  const urls = await fetchStoriesByUuids(
    gridItems
      .map((item) => item.learnMoreLink)
      .filter((link) => link && typeof link === "string"),
  );

  const transformedGridItems = gridItems.map((gridItem) => {
    const learnMoreLink = gridItem.learnMoreLink;

    if (!learnMoreLink || typeof learnMoreLink !== "string") {
      gridItem.learnMoreLink = "";
      return gridItem;
    }

    if (!results[learnMoreLink]) {
      const story = urls.data.stories.find(
        (url) => `${url.uuid}` === learnMoreLink,
      );

      if (story) {
        results[learnMoreLink] = `${story.full_slug}`;
      }
    }

    gridItem.learnMoreLink = results[learnMoreLink] || "";

    return gridItem;
  });

  return (
    <GridSection blok={{ ...props.blok, gridItems: transformedGridItems }} />
  );
}
