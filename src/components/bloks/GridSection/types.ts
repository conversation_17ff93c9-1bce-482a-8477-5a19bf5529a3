import { IStoryblokAssetProps } from "@/common/types";

export interface IGridItem {
  spotlightText: string;
  image: IStoryblokAssetProps;
  collegeOffers: string;
  heading: string;
  bodyContent: string;
  learnMoreLink: string;
}
export interface IGridSectionComponentProps {
  blok: {
    title: Title[];
    gridItems: IGridItem[];
    displayGridNumericIndex: boolean;
    gridItemLinkLabel: string;
  };
}
export interface Title {
  preHeading?: string;
  heading: string;
  bodyContent?: string;
  component: string;
}
