import Text from "@/components/ui/Text";
import React from "react";
import { cva } from "class-variance-authority";
import { IStoryblokLinkProps } from "@/common/types";
import Button from "@/components/ui/Button";

interface Title {
  preHeading?: string;
  heading: string;
  bodyContent?: string;
  component: string;
  useCTA?: boolean;
  link?: IStoryblokLinkProps[];
  buttonLabel?: string;
}

interface TitleProps {
  title: Title;
}

export default function Header(props: TitleProps) {
  const { preHeading, heading, bodyContent, component, useCTA, buttonLabel } =
    props.title;

  const link = props.title.link?.[0];
  const tag =
    component === "componentTitleH2"
      ? "h2"
      : component === "componentTitleH4"
        ? "h4"
        : "h3";

  return (
    <div className="mb-[52px] flex w-full flex-col justify-between gap-10 lg:flex-row lg:items-end">
      <div className="md:max-w-[75%] lg:max-w-[60%] xl:max-w-[55%] 2xl:max-w-[50%]">
        {preHeading && (
          <Text
            tag="h3"
            style="sh5"
            mdStyle="ph1"
            className="mb-2 italic text-primary01-50"
          >
            {preHeading}
          </Text>
        )}
        {heading && (
          <Text
            tag={tag}
            style="h1"
            className={headingVariants({
              component: component as any,
              hasBodyContent: !!bodyContent,
            })}
          >
            {heading}
          </Text>
        )}
        {bodyContent && (
          <Text tag="p" style="mb1" mdStyle="b1" className="text-neutral01-75">
            {bodyContent}
          </Text>
        )}
      </div>
      {useCTA && link && (
        <Button
          theme="secondary"
          colour="maroon"
          className="bg-transparent"
          link={link.link}
          targetAnchorId={link.targetAnchorId}
        >
          {buttonLabel}
        </Button>
      )}
    </div>
  );
}

const headingVariants = cva("font-display-sans", {
  variants: {
    component: {
      componentTitleH2: "text-primary01-75 text-mh1.5 md:text-h2 mb-4",
      componentTitleH3: "text-grays-G1 text-mh1.5 md:text-h3 mb-4",
      componentTitleH4: "text-grays-G1 text-h4 mb-6",
    },
    hasBodyContent: {
      true: "",
      false: "mb-0",
    },
  },
  defaultVariants: {
    hasBodyContent: false,
    component: "componentTitleH2",
  },
});
