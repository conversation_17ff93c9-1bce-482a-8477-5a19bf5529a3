"use client";

import React, { useMemo } from "react";
import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import ReviewCard from "./ReviewCard";
import SlideCarousel from "@/components/ui/SlideCarousel";

type Props = {
  heading: string;
  bodyContent: string;
  reviews: {
    _uid: string;
    name: string;
    role: string;
    bodyContent: string;
  }[];
};

const ReviewCarousel = ({ reviews, heading, bodyContent }: Props) => {
  const header = useMemo(
    () => (
      <div className="w-full md:w-[79%] lg:w-[64%] xl:w-1/2">
        <Text tag="h2" style="h3" className="text-grays-G1">{heading}</Text>
        <Text tag="p" style="b2" className="text-neutral01-75 mt-2 md:mt-4">{bodyContent}</Text>
      </div>
    ),
    [heading, bodyContent],
  );

  if (!reviews || reviews.length === 0) return null;


  return (
    <SlideCarousel
      header={header}
      transparent
      autoCardWrapper={false}
    >
      {reviews.map((item, idx) => {
        return (
          <ReviewCard
            key={item._uid}
            name={item.name}
            role={item.role}
            bodyContent={item.bodyContent}
            className={cn(
              "shrink-0",
              "w-full",
              "md:w-[calc((100%-16px)/2)]",
              "lg:w-[calc((100%-48px)/3)]",
              idx !== reviews.length - 1
              && "mr-4 lg:mr-6"
            )}
          />
        );
      })}
    </SlideCarousel>
  );
};

export default ReviewCarousel;
