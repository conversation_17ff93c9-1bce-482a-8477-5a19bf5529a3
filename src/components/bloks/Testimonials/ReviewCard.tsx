"use client";

import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import { highlightText } from "@/common/renderHighlight";
import { useLabelTranslation } from "@/common/hooks/useTranslation";
import StarIcon from "@/components/icons/StarIcon";

type Props = {
    className?: string;
    name: string;
    role: string;
    bodyContent: string;
};

const ArticlesCard = ({ name, role, bodyContent, className }: Props) => {
    const { t } = useLabelTranslation();
    return (
        <div
            className={cn(
                "block rounded bg-white flex-none shadow-[0px_2px_20px_0px_rgba(86,65,46,0.10)]  w-full p-5",
                className
            )}
        >
            <div className="flex w-full justify-between items-center mb-[22px]">
                <div className="border-l border-neutral01-100 pl-[10px]">
                    <Text tag="h6" style="h6" className="text-primary01-100 line-clamp-1">
                        {name}
                    </Text>
                    <Text tag="p" style="b3" className="text-neutral01-75">
                        {t(role)}
                    </Text>
                </div>
                <div className="flex">
                    {Array.from({ length: 5 }).map((_, index) => (
                        <StarIcon key={index} className="size-[19px] text-[#EFB784]" />
                    ))}
                </div>
            </div>
            <Text tag="p" style="q6" className="text-black">
                {highlightText(bodyContent, "italic text-primary01-50")}
            </Text>

        </div>
    );
};

export default ArticlesCard;