import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import Image from "next/image";
import React from "react";
import AWARDS_ICON from "@/images/awards-icon.svg";
import SlideCarousel from "@/components/ui/SlideCarousel";
import ShuffleCarousel from "./ShuffleCarousel";
import { getDotBackgroundStyle } from "@/common/getDotBackgroundStyle";

interface StudentAwardsSectionProps {
  blok: {
    heading: string;
    awardsLabel: string;
    studentLabel: string;
    admittedToLabel: string;
    students: Student[];
  };
}

interface Student {
  name: string;
  byline: string;
  schools: string;
  programLogos: string[];
  awardsSummary: string;
}

export default function StudentAwardsSection({
  blok,
}: StudentAwardsSectionProps) {
  const { heading, awardsLabel, studentLabel, admittedToLabel, students } =
    blok;
  return (
    <section className="w-full bg-grays-G6">
      {/* eslint-disable-next-line tailwindcss/enforces-shorthand  -- make use of specificity override */}
      <Container className="pl-0 pr-0 md:pl-0 md:pr-0 lg:px-[30px]">
        <div className="relative z-0 w-full pt-[2.87rem] md:pt-[3.88rem] lg:pt-[2.41rem] xl:pt-16">
          <Text tag="h4" style="h4" className="text-center">
            {heading}
          </Text>
          <div className="hidden pt-4xl md:pt-[4.69rem] lg:block">
            <ShuffleCarousel>
              {students.map((student, i) => (
                <StudentCard
                  key={i}
                  {...student}
                  awardsLabel={awardsLabel}
                  studentLabel={studentLabel}
                  admittedToLabel={admittedToLabel}
                />
              ))}
            </ShuffleCarousel>
          </div>
          <div className="lg:hidden">
            <SlideCarousel transparent>
              {students.map((student, i) => (
                <StudentCard
                  key={i}
                  {...student}
                  awardsLabel={awardsLabel}
                  studentLabel={studentLabel}
                  admittedToLabel={admittedToLabel}
                />
              ))}
            </SlideCarousel>
          </div>
          <div
            className="absolute inset-x-8 bottom-24 top-0 -z-10 md:bottom-28 lg:inset-x-0 lg:bottom-12"
            style={getDotBackgroundStyle()}
          />
        </div>
      </Container>
    </section>
  );
}

interface StudentCardProps extends Student {
  awardsLabel: string;
  studentLabel: string;
  admittedToLabel: string;
}

const StudentCard = ({
  name,
  byline,
  schools,
  programLogos,
  awardsSummary,
  awardsLabel,
  studentLabel,
  admittedToLabel,
}: StudentCardProps) => {
  return (
    <div className="flex h-full rounded-lg bg-white shadow-md lg:size-full">
      <div className="flex w-1/5 min-w-[120px] flex-col justify-between rounded-l-lg bg-[linear-gradient(150deg,_rgb(221_149_48_/_50%)_5%,#F1E3C8_15%,#F1E3C8_45%,#9b5a0094_60%,#9B5A00_70%)] p-3.5 text-white bg-blend-overlay">
        <Image
          src={AWARDS_ICON}
          alt="Awards"
          height={300}
          width={350}
          className="object-contain"
        />
        <div className="flex flex-col gap-y-2">
          {programLogos.map((logo, i) => (
            <Image
              key={`${logo} ${i}`}
              src={logo}
              alt="logo"
              height={150}
              width={230}
              className="block h-7 w-auto object-contain"
            />
          ))}
        </div>
      </div>
      <div className="flex flex-1 flex-col gap-y-2xl p-xl md:p-[34px] lg:gap-y-4xl lg:p-[44px]">
        <div>
          <StudentDetailsLabel>{studentLabel}</StudentDetailsLabel>
          <Text
            tag="p"
            style="hw4"
            mdStyle="hw2"
            className="w-full border-b border-dotted border-b-neutral01-50 text-grays-G2"
          >
            {name}
          </Text>
          <Text tag="span" style="b5" lgStyle="b4" className="text-grays-G3">
            {byline}
          </Text>
        </div>
        <div>
          <StudentDetailsLabel>{admittedToLabel}</StudentDetailsLabel>
          <Text
            tag="p"
            style="n2"
            mdStyle="sh5"
            lgStyle="sh4"
            className="w-full border-b border-dotted border-b-neutral01-50 text-grays-G2"
          >
            {schools}
          </Text>
        </div>
        <div>
          <StudentDetailsLabel>{awardsLabel}</StudentDetailsLabel>
          <Text
            tag="p"
            style="b4"
            lgStyle="b1"
            className="mt-[0.39rem] w-full whitespace-pre text-grays-G2 md:text-[0.902rem]"
          >
            {awardsSummary}
          </Text>
        </div>
      </div>
    </div>
  );
};

const StudentDetailsLabel = ({ children }: { children: React.ReactNode }) => (
  <Text
    tag="label"
    style="b4"
    lgStyle="b3"
    className="mb-2xs block text-primary01-75"
  >
    {children}
  </Text>
);
