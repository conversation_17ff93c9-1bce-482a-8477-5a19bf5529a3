"use client";

import IconButton from "@/components/ui/IconButton";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/16/solid";
import React, { useLayoutEffect, useRef, useState } from "react";

export default function ShuffleCarousel({ children }: { children: React.ReactNode[] }) {
  const wrapper = useRef<HTMLDivElement>(null);
  const cardRefs = useRef<HTMLDivElement[]>([]);
  const cardsArray = React.Children.toArray(children);
  const totalCards = cardsArray.length;

  const order = useRef<number[]>(Array.from({ length: totalCards }, (_, i) => i));
  const animating = useRef(false);

  const [minHeight, setMinHeight] = useState<number>();
  const [currentIndex, setCurrentIndex] = useState(0);

  useLayoutEffect(() => {
    cardRefs.current = cardRefs.current.slice(0, totalCards);
    if (!wrapper.current) return;
    const tallest = Math.max(
      ...Array.from(wrapper.current.children).map(
        (c) => (c as HTMLDivElement).getBoundingClientRect().height
      )
    );
    setMinHeight(tallest);
    requestAnimationFrame(() => applyStack(order.current, false));
  }, [totalCards]);

  const applyStack = (currentOrder: number[], smooth: boolean) => {
    currentOrder.forEach((cardIndex, stackIndex) => {
      const el = cardRefs.current[cardIndex];
      if (!el) return;
      el.style.transition = smooth ? "transform 0.3s ease" : "none";
      if (stackIndex === 0) {
        el.style.zIndex = `${totalCards}`;
        el.style.transform = "translateX(0) rotate(0deg) scale(1)";
      } else {
        const offsetIndex = stackIndex - 1;
        el.style.zIndex = `${totalCards - stackIndex}`;
        el.style.transform = `translate(${stackIndex * 20}px, ${offsetIndex * 10}px)
                               rotate(${offsetIndex * 1.5}deg)
                               scale(${1 - offsetIndex * 0.025})`;
      }
    });
  };

  const animate = (forward: boolean) => {
    if (animating.current) return;
    animating.current = true;
  
    const leavingCardIndex = forward ? order.current[0] : order.current[order.current.length - 1];
    const leavingCard = leavingCardIndex !== undefined ? cardRefs.current[leavingCardIndex] : null;
    if (!leavingCard) return;
  
    leavingCard.style.transition = "transform 0.5s ease";
    leavingCard.style.transform = forward
      ? "translateX(100%) rotate(5deg)"
      : "translateX(-100%) rotate(-5deg)";
  
    setTimeout(() => {
      leavingCard.style.transition = "none";
  
      if (forward) {
        order.current.push(order.current.shift()!);
        setCurrentIndex((prev) => Math.min(prev + 1, totalCards - 1));
      } else {
        order.current.unshift(order.current.pop()!);
        setCurrentIndex((prev) => Math.max(prev - 1, 0));
      }
  
      requestAnimationFrame(() => {
        applyStack(order.current, true);
        animating.current = false;
      });
    }, 500);
  };

  const handleNext = () => !animating.current && animate(true);
  const handleBack = () => !animating.current && animate(false);

  return (
    <>
      <div
        ref={wrapper}
        className="relative mx-auto flex justify-stretch"
        style={{ width: 800, minHeight, opacity: Number(!!minHeight) }}
      >
        {cardsArray.map((card, i) => (
          <div
            key={i}
            ref={(el) => {
              if (el) {
                cardRefs.current[i] = el;
              }
            }}
            className="absolute w-full rounded-[12px] bg-white flex items-center justify-center"
            style={{ height: minHeight }}
          >
            {card}
          </div>
        ))}
      </div>
      <div className="mx-auto mt-16 flex w-[800px] justify-end gap-4">
        <IconButton
          colour="maroon"
          onClick={handleBack}
          Icon={ChevronLeftIcon}
          disabled={currentIndex === 0}
        />
        <IconButton
          colour="maroon"
          onClick={handleNext}
          Icon={ChevronRightIcon}
          disabled={currentIndex === totalCards - 1}
        />
      </div>
    </>
  );
}