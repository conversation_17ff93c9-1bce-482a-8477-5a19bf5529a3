import type { TBackgroundColour, Theme } from "@/common/types";
import { storyblokEditable } from "@storyblok/react/rsc";
import { cn } from "@/common/utils";
import SideForm from "@/components/ui/SideForm";
import Container from "@/components/ui/Container";
import GridItems from "@/components/bloks/H2TextGridImageCards/GridItems";
import Text from "@/components/ui/Text";
import { getDotBackgroundStyle } from "@/common/getDotBackgroundStyle";

interface GridItemProps {
  _uid: string;
  index: number;
  subheading?: string;
}

interface Props {
  blok: {
    backgroundColour: TBackgroundColour;
    bodyImage: [{ _uid: string }];
    bodyHeader?: string;
    bodyText?: string;
    bodySubheading?: string;
    bodyContent?: string;
    bodyLayout: string;
    flipLayout: boolean;
    flipMobileLayout: boolean;
    dataTestId: string;
    sectionIndex: number;
    anchorId: string;
    _uid: string;
    formComponent: any[];
    heading: string;
    subheading: string;
    theme: Theme;
    bodyGrid: GridItemProps[];
  };
  sectionIndex: number;
}

const SideTextImageGridForm = ({ blok }: Props) => {
  const {
    flipLayout,
    flipMobileLayout,
    anchorId,
    _uid,
    formComponent = [],
    heading = "",
    subheading = "",
    bodyHeader,
    bodyGrid,
    bodyText,
    bodySubheading,
  } = blok;

  const normalisedGridItems = bodyGrid.map((item, index) => ({
    _uid: item._uid,
    spotlightText: `0${index + 1}`,
    bodyContent: item.subheading,
  }));

  return (
    <section id={anchorId} {...storyblokEditable(blok)} className="bg-grays-G6">
      <Container>
        <div
          id="standard-web-lead-form"
          className={cn(
            "flex items-center gap-y-[46px] lg:flex-row lg:items-start lg:justify-between",
            flipLayout ? "lg:flex-row-reverse" : "lg:flex-row",
            flipMobileLayout ? "flex-col-reverse" : "flex-col",
          )}
        >
          <div className="flex max-w-[740px] flex-col gap-4xl px-5 py-8 lg:basis-[45%] lg:p-0 2xl:basis-[47%]">
            <Text
              tag="h2"
              style="mh1.5"
              mdStyle="h3"
              className={cn("text-primary01-75")}
            >
              {bodyHeader}
            </Text>

            {bodySubheading && (
              <Text style="h5" tag="p" className="text-grays-G1">
                {bodySubheading}
              </Text>
            )}

            {bodyText && (
              <Text
                style="mb1"
                mdStyle="b1"
                tag="p"
                className="text-neutral01-75"
              >
                {bodyText}
              </Text>
            )}

            {bodyGrid?.length > 0 && (
              <div className="inline-grid grid-cols-1 gap-x-16 gap-y-8 pb-8 pt-3 md:grid-cols-2">
                <GridItems gridItems={normalisedGridItems} />
              </div>
            )}
          </div>

          <div
            className="w-full py-[3.22rem] lg:basis-[45%] lg:rounded-2xl lg:!bg-none lg:py-0 2xl:basis-[44%]"
            style={getDotBackgroundStyle()}
          >
            {formComponent?.length > 0 && (
              <SideForm
                formId={_uid}
                {...{ heading, subheading, formComponent }}
              />
            )}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default SideTextImageGridForm;
