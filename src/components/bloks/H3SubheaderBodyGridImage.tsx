import React from "react";
import Image from "next/image";

import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import { generateImageAltFromFilename } from "@/common/utils";

interface GridItem {
  _uid: string;
  component: string;
  bodyContent: string;
  _editable?: string;
}

interface H3SubheaderBodyGridImageProps {
  blok: {
    _uid: string;
    Image: string;
    heading: string;
    bodyText: string;
    component: string;
    gridItems: GridItem[];
    subHeading: string;
    _editable?: string;
  };
}

const H3SubheaderBodyGridImage: React.FC<H3SubheaderBodyGridImageProps> = ({
  blok: { Image: imageData, heading, bodyText, gridItems, subHeading },
}) => {
  const formatIndex = (index: number): string => {
    return (index + 1).toString().padStart(2, "0");
  };

  const altAttrOfImage = generateImageAltFromFilename(imageData);

  return (
    <Container>
      <div className="hidden lg:block">
        <div className="flex items-start justify-between">
          <div className="flex w-[49%] flex-col gap-4xl">
            <Text tag="h2" style="h2" className="text-primary01-75">
              {heading}
            </Text>
            {!!subHeading && (
              <Text tag="h5" style="h5" className="text-grays-G1">
                {subHeading}
              </Text>
            )}
            <Text tag="p" style="b1" className="text-neutral01-75">
              {bodyText}
            </Text>
            <div className="grid grid-cols-1 gap-6 gap-y-[74px] md:grid-cols-2 xl:grid-cols-2">
              {gridItems.map((item, index) => (
                <div key={item._uid}>
                  <div className="font-display-serif text-[50px] font-light text-primary01-50">
                    {formatIndex(index)}
                  </div>

                  <Text tag="p" style="b1" className="text-neutral01-75">
                    {item.bodyContent}
                  </Text>
                </div>
              ))}
            </div>
          </div>
          <div className="w-2/5">
            <div className="relative h-auto w-full">
              <Image
                src={imageData}
                alt={altAttrOfImage}
                className="rounded-lg object-cover"
                width={528}
                height={597}
                sizes="(max-width: 375px) 326px, (max-width: 768px) 348px, (max-width: 1025px) 394px, (max-width: 1225px) 440px, 528px"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="block md:block lg:hidden">
        <div className="flex flex-col gap-[40px]">
          <Text tag="h2" style="h2" className="text-primary01-75">
            {heading}
          </Text>
          {!!subHeading && (
            <Text tag="h5" style="h5" className="text-grays-G1">
              {subHeading}
            </Text>
          )}
          <Text tag="p" style="b1" className="text-neutral01-75">
            {bodyText}
          </Text>
          <div className="grid grid-cols-1 gap-[48px] md:grid-cols-2 md:gap-[74px]">
            {gridItems.map((item, index) => (
              <div key={item._uid} className="">
                <div className="font-display-serif text-[50px] font-light text-primary01-50">
                  {formatIndex(index)}
                </div>

                <Text tag="p" style="b1" className="text-neutral01-75">
                  {item.bodyContent}
                </Text>
              </div>
            ))}
          </div>
          <div className="flex justify-center">
            <div className="relative w-full max-w-md">
              <Image
                src={imageData}
                alt={altAttrOfImage}
                width={528}
                height={597}
                className="rounded-lg object-cover"
                sizes="(max-width: 375px) 326px, (max-width: 768px) 348px, (max-width: 1025px) 394px, (max-width: 1225px) 440px, 528px"
              />
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default H3SubheaderBodyGridImage;
