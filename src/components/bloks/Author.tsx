import { replaceEnWithUs } from "@/common/utils";
import Image from "next/image";
import Text from "@/components/ui/Text";

interface Props {
  variant?: "primary" | "compact"; // use variant to determine the UI of the author box
  mappedPersona?: string;
  full_slug?: string;
  blok: {
    name?: string;
    Name?: string;
    school?: string;
    Byline?: string;
    image: {
      image: string;
      altText: string;
    }[];
    ProfilePicture?: {
      filename: string;
      alt: string;
    };
  };
}

const Author = ({
  blok,
  variant = "primary",
  mappedPersona,
  full_slug,
}: Props) => {
  const name = blok?.name ?? blok?.Name
  const school = blok?.school ?? blok?.Byline;
  const image = blok?.image
    ? {
      image: blok?.image?.[0]?.image ?? blok?.ProfilePicture?.filename ?? "",
      altText: blok?.image?.[0]?.altText ?? name ?? "Author's profile picture",
    }
    : {
      image: blok?.ProfilePicture?.filename ?? "",
      altText: name ?? "Author's profile picture",
    };


  if (!blok) {
    return null;
  }

  const imageNode = (
    <span className="size-[4.125rem] md:size-16 relative rounded-full overflow-hidden bg-[#9F6F4E]">
      <Image
        className="object-cover object-top "
        src={image.image}
        alt={image.altText}
        width={66}
        height={66}
        sizes="128px"
        style={{ width: "100%", height: "100%" }}
      />
    </span>
  );
  const renderPrimary = () => (
    <a
      className="group mt-6 flex h-16 w-fit cursor-pointer items-center gap-4"
      href={`https://www.crimsoneducation.org/${replaceEnWithUs(full_slug)}`}
      target="_blank"
    >
      {imageNode}
      <span className="flex h-full w-auto flex-col items-start justify-center">
        <Text
          tag="p"
          style="h5"
          className="text-primary01-100 transition-colors duration-200 group-hover:text-primary01-50"
        >
          {name}
        </Text>
        {school && (
          <Text
            tag="p"
            style="b2"
            className="text-black md:h-4"
          >
            {school}
          </Text>
        )}
      </span>
    </a>
  );
  const renderCompact = () => (
    <a
      className="group flex w-fit cursor-pointer items-center gap-4 overflow-hidden"
      href={`https://www.crimsoneducation.org/${replaceEnWithUs(full_slug)}`}
      target="_blank"
    >
      {imageNode}
      <div className="flex h-full flex-col items-start justify-center pt-2">
        {mappedPersona && (
          <Text
            tag="p"
            style="b3"
            className="text-black"
          >
            {mappedPersona}
          </Text>
        )}
        <Text
          tag="p"
          style="h4"
          className="text-primary01-100 transition-colors duration-200 group-hover:text-primary01-50 mt-1 md:mt-2"
        >
          {name}
        </Text>
      </div>
    </a>
  );

  return variant === "compact" ? renderCompact() : renderPrimary();
};

export default Author;
