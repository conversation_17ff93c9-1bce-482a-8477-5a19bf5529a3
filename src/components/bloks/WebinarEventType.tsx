"use client";

import { storyblokEditable } from "@storyblok/react/rsc";
import Text from "@/components/ui/Text";
import { useLabelTranslation } from "@/common/hooks/useTranslation";
import Skeleton from "../ui/Skeleton";

interface Props {
  blok: {
    eventType: "In Person" | "Online";
    textOverride?: string;
  };
}

const WebinarEventType = ({ blok }: Props) => {
  const { t, isLoading } = useLabelTranslation();
  const eventTypeLabel = t(blok.eventType);

  return (
    <>
      {isLoading && (
        <div className="flex justify-center">
          <Skeleton className="h-10 w-32" theme="transparent" />
        </div>
      )}
      {!isLoading && (
        <Text
          tag="p"
          style="mph1"
          mdStyle="ph1"
          {...storyblokEditable(blok)}
          className="mx-auto mb-2 inline-block italic text-white"
        >
          {eventTypeLabel}
        </Text>
      )}
    </>
  );
};

export default WebinarEventType;
