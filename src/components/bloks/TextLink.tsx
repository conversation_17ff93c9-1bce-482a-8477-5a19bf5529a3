import { IStoryblokLinkProps } from "@/common/types";
import GeneralLink from "@/components/bloks/GeneralLink";
import { cn } from "@/common/utils";

interface Props {
  blok: {
    _uid: string;
    link: IStoryblokLinkProps[];
    text: string;
  };
  className?: string;
}

const TextLink = ({ blok, className }: Props) => {
  const { link, text } = blok;

  return (
    link[0] && (
      <GeneralLink
        blok={link[0]}
        className={cn("my-1 inline-block font-bold underline", className)}
      >
        {text}
      </GeneralLink>
    )
  );
};

export default TextLink;
