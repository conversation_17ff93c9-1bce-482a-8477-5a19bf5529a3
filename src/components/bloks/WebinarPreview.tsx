"use client";

import { ICtaButtonProps } from "@/common/types";
import { cn, httpsPath } from "@/common/utils";
import Typography from "@/components/ui/Typography";
import { formatDateTime } from "@/common/date";
import ImageWrapper from "@/components/ui/ImageWrapper";
import Button from "@/components/ui/Button";
import { useState, useEffect } from "react";

interface Props {
  dateTime: string;
  type: [
    {
      _uid: string;
      eventType: "In Person" | "Online";
      textOverride?: string;
    },
  ];
  ctaButton: ICtaButtonProps[];
  title?: string;
  description?: string;
  backgroundImage?: [{ _uid: string; image: string; altText: string }];
  thumbnailImage?: [{ _uid: string; image: string; altText: string }];
  dateOverrideText?: string;
  timeOverrideText?: string;
}

/**
 * @deprecated - Use the EventCard component instead
 */
const WebinarPreview = (props: Props) => {
  const {
    dateTime,
    type,
    ctaButton,
    thumbnailImage,
    title,
    description,
    dateOverrideText,
    timeOverrideText,
  } = props;

  return (
    <div className="flex h-[169px] w-full min-w-[200px] max-w-[400px] flex-col rounded-md shadow-[0px_2.24px_5.22px_rgba(0,0,0,0.05)] md:h-[318px]">
      <ImageSection
        backgroundImage={thumbnailImage}
        title={title}
        eventType={type?.[0]?.eventType}
      />
      <FooterSection
        description={description ?? ""}
        ctaButton={ctaButton}
        dateTime={dateTime}
        dateOverrideText={dateOverrideText}
        timeOverrideText={timeOverrideText}
      />
    </div>
  );
};

interface ImageSectionProps {
  backgroundImage?: { image: string; altText: string; _uid: string }[];
  title?: string;
  eventType: "In Person" | "Online";
  textOverride?: string;
}

const ImageSection = ({
  backgroundImage,
  title,
  eventType,
}: ImageSectionProps) => {
  const imageContent = backgroundImage?.[0];
  const imagePath = httpsPath(imageContent?.image);

  return (
    <div className="relative flex h-full max-h-[169px] min-h-[149px] flex-col justify-between p-[17px]">
      {imagePath && (
        <div className="absolute inset-0 z-[-1] flex overflow-hidden">
          <ImageWrapper
            src={imagePath}
            width={400}
            height={200}
            fit="cover"
            className="size-full rounded-t-[6px]"
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black to-transparent to-85%" />
        </div>
      )}
      <div
        className={cn(
          "inline-flex h-fit w-fit items-center justify-center rounded-full bg-primary px-[10px] py-[2px] font-bold uppercase text-white",
          eventType === "In Person" ? "bg-blue-500" : "bg-green-700",
        )}
      >
        <p className="leading-0 text-[10px] tracking-[1px]">{eventType}</p>
      </div>
      <Typography
        className="max-w-[300px] font-bold leading-[20px] text-white md:text-h4 md:leading-[20px]"
        tag="h2"
        style="body1"
      >
        {title}
      </Typography>
    </div>
  );
};

interface FooterSectionProps {
  dateTime: string;
  description: string;
  ctaButton: ICtaButtonProps[];
  dateOverrideText?: string;
  timeOverrideText?: string;
}

const FooterSection = ({
  dateTime,
  description,
  ctaButton,
  dateOverrideText,
  timeOverrideText,
}: FooterSectionProps) => {
  const ctaButtonContent = ctaButton?.[0];

  return (
    <div className="flex flex-col justify-between p-[16px] md:min-h-[146px]">
      <span className="hidden md:block">
        <Typography
          tag="p"
          style="body3"
          className="line-clamp-3 leading-[13.8px]"
        >
          {description}
        </Typography>
      </span>

      <div className="flex items-center justify-between">
        <span className="hidden md:block">
          <Button>{ctaButtonContent?.text ?? ""}</Button>
        </span>

        <PreviewDateFormatted
          dateTime={dateTime}
          dateOverrideText={dateOverrideText}
          timeOverrideText={timeOverrideText}
        />
      </div>
    </div>
  );
};

const DateTimeText = ({ children }: { children: React.ReactNode }) => (
  <p className="pr-[5px] text-body3 font-semibold leading-[13.8px] md:max-w-[120px] md:pr-0 md:text-right md:font-bold">
    {children}
  </p>
);

const PreviewDateFormatted = ({
  dateTime,
  dateOverrideText,
}: {
  dateTime: string;
  dateOverrideText?: string;
  timeOverrideText?: string;
}) => {
  const [date, setDate] = useState<string[]>([]);
  const [dateFormatted] = date;

  useEffect(() => {
    formatDateTime({
      dateTime,
      dateFormat: "ddd, DD MMM",
      timeFormat: "hh:mma",
    })
      .then((formattedDate) => setDate(formattedDate))
      .catch(() => setDate([]));
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="flex md:w-[130px] md:flex-col">
      <DateTimeText>{dateOverrideText ?? dateFormatted}</DateTimeText>
      <DateTimeText>{dateOverrideText ?? dateFormatted}</DateTimeText>
    </div>
  );
};

export default WebinarPreview;
