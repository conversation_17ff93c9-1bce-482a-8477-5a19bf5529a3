import { storyblokEditable } from "@storyblok/react/rsc";
import Container from "@/components/ui/Container";

import Image from "next/image";
import StatCard from "./StatCard";
import QuotesText from "./QuotesText";

import us_quoteImage1 from "./Images/us-Images/1.webp";
import us_stat1ToCard from "./Images/us-Images/stat1.webp";
import us_stat2ToCard from "./Images/us-Images/stat2.webp";
import us_quote3Top from "./Images/us-Images/3-top.webp";
import us_quote3Bottom from "./Images/us-Images/3-bottom.webp";
import us_quote4Top from "./Images/us-Images/4-top.webp";
import us_quote4Bottom from "./Images/us-Images/4-bottom.webp";
import us_quote6Top from "./Images/us-Images/6-top.webp";
import us_quote6Bottom from "./Images/us-Images/6-bottom.webp";

import uk_quoteImage1 from "./Images/uk-Images/1.webp";
import uk_stat1ToCard from "./Images/uk-Images/stat1.webp";
import uk_stat2ToCard from "./Images/uk-Images/stat2.webp";
import uk_quote3Top from "./Images/uk-Images/3-top.webp";
import uk_quote3Bottom from "./Images/uk-Images/3-bottom.webp";
import uk_quote4Top from "./Images/uk-Images/4-top.webp";
import uk_quote4Bottom from "./Images/uk-Images/4-bottom.webp";
import uk_quote6Top from "./Images/uk-Images/6-top.webp";
import uk_quote6Bottom from "./Images/uk-Images/6-bottom.webp";

const IMAGE_DATASETS = {
  "us-images": {
    quoteImage1: us_quoteImage1,
    stat1ToCard: us_stat1ToCard,
    stat2ToCard: us_stat2ToCard,
    quote3Top: us_quote3Top,
    quote3Bottom: us_quote3Bottom,
    quote4Top: us_quote4Top,
    quote4Bottom: us_quote4Bottom,
    quote6Top: us_quote6Top,
    quote6Bottom: us_quote6Bottom,
  },
  "uk-images": {
    quoteImage1: uk_quoteImage1,
    stat1ToCard: uk_stat1ToCard,
    stat2ToCard: uk_stat2ToCard,
    quote3Top: uk_quote3Top,
    quote3Bottom: uk_quote3Bottom,
    quote4Top: uk_quote4Top,
    quote4Bottom: uk_quote4Bottom,
    quote6Top: uk_quote6Top,
    quote6Bottom: uk_quote6Bottom,
  },
} as const;

interface IStatisticCard {
  heading: string;
  bodyContent: string;
}

interface Props {
  blok: {
    _uid: string;
    component: string;
    attribution: string;
    featuredQuote: string;
    statisticCards: IStatisticCard[];
    _editable: string;
    imageDataset: keyof typeof IMAGE_DATASETS;
  };
  sectionIndex: number;
}
;

const FeaturedQuoteSection = (props: Props) => {
  const { blok } = props;
  const { attribution, featuredQuote, statisticCards, imageDataset } = blok;
  const images = IMAGE_DATASETS[imageDataset] ?? IMAGE_DATASETS["us-images"];

  return (
    <section className="bg-grays-G6" {...storyblokEditable(blok)}>
      <Container className="pb-28">
        <QuotesText featuredQuote={featuredQuote} attribution={attribution} />
        <div className="relative z-[2] mx-auto max-w-[475px] pt-4xl md:mx-0 md:max-w-none md:pt-0 xl:pt-[450px]">
          <Image
            src={images.quoteImage1}
            alt="Quote Icon"
            width={390}
            height={510}
            className="w-full max-w-[6.82rem] rounded md:max-w-[17.65rem] lg:max-w-[24.1875rem]"
          />
          <div className="relative bottom-16 flex justify-end md:h-[21rem] lg:bottom-28 lg:h-[25rem]">
            <Image
              src={images.stat1ToCard}
              alt="Stat 1"
              width={100}
              height={100}
              className="absolute right-[90px] top-[-20px] w-full max-w-[2.961rem] rounded md:right-60 md:top-[-65px] md:max-w-[8.7873rem] lg:-top-24 lg:right-[13.5rem] lg:max-w-[10.5rem]"
            />
            <StatCard
              stat={statisticCards[0]?.heading ?? ""}
              bodyContent={statisticCards[0]?.bodyContent ?? ""}
            />
          </div>
          <div className="relative hidden max-w-[50%] md:left-8 md:flex lg:left-32 xl:left-[17rem]">
            <Image
              src={images.quote3Top}
              alt="Quote 3 Top"
              width={100}
              height={100}
              className="absolute -left-24 -top-28 hidden w-full max-w-[9.12rem] rounded lg:block"
            />
            <Image
              src={images.quote3Bottom}
              alt="Quote 3 Bottom"
              width={230}
              height={300}
              className="relative hidden w-full max-w-[13.89rem] rounded md:block"
            />
          </div>
          <div className="relative hidden md:flex md:justify-end lg:-top-36 lg:right-20 lg:h-80 xl:right-40">
            <Image
              src={images.quote4Top}
              alt="Quote 4 Top"
              width={400}
              height={600}
              className="relative right-8 w-full max-w-[17.65rem] rounded lg:h-[31.7757rem] lg:max-w-[24.1875rem]"
            />
            <Image
              src={images.quote4Bottom}
              alt="Quote 4 Bottom"
              width={100}
              height={100}
              className="absolute -bottom-12 right-60 z-[-1] w-full max-w-[8.42rem] rounded lg:bottom-[-5.5rem] lg:right-[19rem] lg:top-[23.5rem] lg:max-w-[10.5rem]"
            />
          </div>
          <div className="absolute left-5xl top-64 flex md:relative md:left-0 md:top-0">
            {/* Stat 2 and card 2 */}
            <StatCard
              stat={statisticCards[1]?.heading ?? ""}
              bodyContent={statisticCards[1]?.bodyContent ?? ""}
            />

            <Image
              src={images.stat2ToCard}
              alt="Stat 2"
              width={150}
              height={180}
              className="absolute hidden w-full max-w-[6.8209rem] rounded lg:left-56 lg:top-[23.5rem] lg:block lg:max-w-[8.9375rem]"
            />
          </div>
          <div className="relative hidden md:flex md:justify-end lg:bottom-16 lg:right-12">
            <Image
              src={images.quote6Top}
              alt="Quote 6 Top"
              width={100}
              height={100}
              className="absolute -right-8 -top-20 hidden w-full max-w-[6.8209rem] rounded lg:block"
            />
            <Image
              src={images.quote6Bottom}
              alt="Quote 6 Bottom"
              width={223}
              height={300}
              className="relative w-full max-w-[13.89rem] rounded"
            />
          </div>
        </div>
      </Container>
    </section>
  );
};

export default FeaturedQuoteSection;
