import Text from "@/components/ui/Text";
import React from "react";

type Props = {
  stat: string;
  bodyContent: string;
};

const StatCard = (props: Props) => {
  return (
    <div className="z-[2] flex min-h-[11.25rem] max-w-32 flex-col justify-end break-words rounded bg-white p-[0.56rem] md:min-h-[25rem] md:max-w-[19.03rem] md:p-8">
      <Text tag="p" style="mh1" mdStyle="h1" className="text-primary01-75">
        {props.stat}
      </Text>
      <Text tag="p" style="b4" mdStyle="b1" className="text-primary01-75">
        {props.bodyContent}
      </Text>
    </div>
  );
};

export default StatCard;
