"use client";

import React from "react";
import { motion, useScroll, useTransform } from "motion/react";
import Text from "@/components/ui/Text";
import {
  OpenQuotation,
  CloseQuotation,
} from "@/components/ui/OpenCloseQuotation";
import { highlightText } from "@/common/renderHighlight";

type Props = {
  featuredQuote: string;
  attribution: string;
};

const QuotesText = ({ featuredQuote, attribution }: Props) => {
  const { scrollYProgress } = useScroll();

  const scale = useTransform(scrollYProgress, [0, 0.2], [0.875, 1]);

  return (
    <motion.div
      className="md:z-1 text-center md:sticky md:top-[40%] md:mb-[200px] md:mt-[110px]"
      style={{ scale }}
    >
      <Text
        tag="blockquote"
        style="q5"
        mdStyle="q1"
        className="whitespace-pre-line pb-[24px] text-grays-G1 md:mx-auto md:max-w-[44.25rem] lg:max-w-[45.1875rem] lg:pb-[32px]"
      >
        <OpenQuotation />
        {highlightText(featuredQuote, "italic text-primary01-50")}
        <CloseQuotation />
      </Text>
      <Text
        tag="p"
        style="b3"
        className="whitespace-pre-line text-neutral01-75 lg:whitespace-normal"
      >
        {attribution}
      </Text>
    </motion.div>
  );
};

export default QuotesText;
