import React from "react";
import ReactMarkdown from "react-markdown";
import Information from "../icons/Information";

interface Props {
  blok: {
    Heading: string;
    content: string;
  };
}

const CalloutBox = ({ blok }: Props) => {
  const { Heading, content } = blok;

  return (
    <div className="my-8 w-full items-center rounded bg-neutral01-0 px-6 pb-6 pt-7 md:mb-11 lg:mt-12">
      <div className="mb-2 flex w-full items-center justify-start font-display-sans text-xl font-semibold leading-normal text-primary01-100">
        <Information className="mr-2 size-5" />
        {Heading}
      </div>
      <ReactMarkdown className="font-body-single text-base font-normal leading-normal text-primary01-100">
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default CalloutBox;
