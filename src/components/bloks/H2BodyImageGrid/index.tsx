"use client";

import { IStoryblokAssetProps } from "@/common/types";
import { SbBlokData } from "@storyblok/react/rsc";
import Container from "@/components/ui/Container";
import Text from "@/components/ui/Text";
import Image from "next/image";
import { motion } from "motion/react";
import { getDotBackgroundStyle } from "@/common/getDotBackgroundStyle";

// public images
import bottomFrameImage from "./Images/us-images/bottom-frame.webp";
import topFrameImage from "./Images/us-images/top-frame.webp";

import us_leftImage from "./Images/us-images/left.webp";
import us_rightImage from "./Images/us-images/right.webp";

import uk_leftImage from "./Images/uk-images/left.webp";
import uk_rightImage from "./Images/uk-images/right.webp";

import eu_leftImage from "./Images/eu-images/left.webp";
import eu_rightImage from "./Images/eu-images/right.webp";

const IMAGE_DATASETS = {
    "us-images": {
        leftImage: us_leftImage,
        rightImage: us_rightImage,
        bottomFrameImage,
        topFrameImage,
    },
    "uk-images": {
        leftImage: uk_leftImage,
        rightImage: uk_rightImage,
        bottomFrameImage,
        topFrameImage,
    },
    "eu-images": {
        leftImage: eu_leftImage,
        rightImage: eu_rightImage,
        bottomFrameImage,
        topFrameImage,
    },
} as const;


export interface IH2BodyImageGrid extends SbBlokData {
    heading: string;
    bodyContent: string;
    studentImage: IStoryblokAssetProps;
    textItems: {
        _uid: string;
        bodyContent: string;
    }[];
    imageDataset: keyof typeof IMAGE_DATASETS;
}

interface Props {
    blok: IH2BodyImageGrid;
    pageType: string;
}

const H2BodyImageGrid = (props: Props) => {
    const { blok } = props;

    const { heading, bodyContent, studentImage, textItems, imageDataset } = blok;

    const images = IMAGE_DATASETS[imageDataset] ?? IMAGE_DATASETS["us-images"];

    const textItemsToShow =
        textItems.length === 5 ? textItems.slice(0, 4) : textItems;

    const fourFloatingCards = [
        { top: "0", left: "5rem", zIndex: 2 },
        { top: "1rem", right: "5.5rem", zIndex: 2 },
        { bottom: "-5rem", left: "4rem", zIndex: 2 },
        { bottom: "-4rem", right: "5rem", zIndex: 2 },
    ];
    const sixFloatingCards = [
        { top: "0", left: "5rem", zIndex: 2 },
        { top: "1rem", right: "5.5rem", zIndex: 2 },
        { top: "50%", left: "4rem", zIndex: 2 },
        { top: "50%", right: "4rem", zIndex: 2 },
        { bottom: "-5rem", left: "4rem", zIndex: 2 },
        { bottom: "-5rem", right: "5rem", zIndex: 2 },
    ];

    return (
        <section className="bg-grays-G6">
            <Container>
                <div
                    className="grid grid-cols-1 gap-10 pb-[5.25rem] pt-[2.87rem] md:gap-20 md:pb-[4.16rem] md:pt-[3.88rem] lg:flex lg:flex-col lg:justify-center lg:gap-10 lg:pb-[10.25rem] lg:pt-[2.41rem] xl:pt-16"
                    style={getDotBackgroundStyle()}
                >
                    <div className="mx-auto text-center lg:max-w-[39.9rem] xl:max-w-[44.25rem]">
                        <Text
                            tag="h2"
                            style="mh1.5"
                            mdStyle="h2"
                            className="pb-6 text-primary01-75"
                        >
                            {heading}
                        </Text>
                        <Text
                            tag="p"
                            style="mb1"
                            mdStyle="b1"
                            className="text-neutral01-75"
                        >
                            {bodyContent}
                        </Text>
                    </div>

                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:hidden">
                        {textItemsToShow.map((item, index) => {
                            return (
                                <motion.div
                                    key={item._uid}
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    viewport={{ once: true }}
                                    transition={{ duration: 0.5, delay: index * 0.2 }}
                                    className="rounded bg-white p-3 md:p-8"
                                >
                                    <Text
                                        tag="p"
                                        style="mb1"
                                        mdStyle="b1"
                                        className="text-neutral01-75"
                                    >
                                        {item.bodyContent}
                                    </Text>
                                </motion.div>
                            );
                        })}
                    </div>

                    <div className="relative mx-auto md:mt-3 lg:mx-0 lg:flex lg:justify-center xl:mx-auto xl:w-full xl:max-w-[1190px]">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6, delay: 0.6 }}
                            className="absolute -right-8 top-0 max-w-[3.67rem] md:-right-12 md:-top-12 md:max-w-[6.85rem] lg:bottom-1 lg:left-8 lg:right-[unset] lg:top-[unset] lg:max-w-[8.9rem]"
                        >
                            <Image
                                width={120}
                                height={120}
                                src={images.leftImage}
                                alt="Student from NYU"
                                className="w-full"
                            />
                        </motion.div>
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                            className="absolute -top-4 left-12 hidden max-w-[6.6875rem] lg:block"
                        >
                            <Image
                                width={120}
                                height={120}
                                src={images.topFrameImage}
                                alt="Text Frame 01"
                                className="w-full"
                            />
                        </motion.div>
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6, delay: 0.6 }}
                            className="absolute -bottom-28 right-12 hidden max-w-[9.25rem] lg:block"
                        >
                            <Image
                                width={120}
                                height={120}
                                src={images.bottomFrameImage}
                                alt="Text Frame 02"
                                className="w-full"
                            />
                        </motion.div>

                        {studentImage.filename && (
                            <motion.div
                                initial={{ opacity: 0 }}
                                whileInView={{ opacity: 1 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6 }}
                                className="h-[546px] max-h-[12.67rem] w-[483px] max-w-[11.21rem] md:max-h-[23.58rem] md:max-w-[20.87rem] lg:max-h-[34.125rem] lg:max-w-[30.19rem]"
                                style={{
                                    backgroundImage: `url(${studentImage.filename})`,
                                    backgroundSize: "cover",
                                    backgroundPosition: "center",
                                    backgroundRepeat: "no-repeat",
                                }}
                            />
                        )}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6, delay: 0.6 }}
                            className="absolute -bottom-8 -left-8 max-w-[3.67rem] md:-left-12 md:max-w-[6.85rem] lg:left-[unset] lg:right-14 lg:top-[9.25rem] lg:max-w-[8.5rem]"
                        >
                            <Image
                                width={120}
                                height={120}
                                src={images.rightImage}
                                alt="Student from Duke"
                                className="w-full"
                            />
                        </motion.div>

                        {textItemsToShow.map((item, index) => {
                            const isTopBox = index < 2;
                            const delay = isTopBox ? 0.3 : 0.6;

                            return (
                                <motion.div
                                    key={item._uid}
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    viewport={{ once: true }}
                                    transition={{ duration: 0.6, delay }}
                                    className="absolute hidden max-w-[18.875rem] rounded bg-white p-3 md:p-8 lg:block xl:max-w-[21.875rem]"
                                    style={
                                        textItemsToShow.length === 4
                                            ? fourFloatingCards[index]
                                            : sixFloatingCards[index]
                                    }
                                >
                                    <Text
                                        tag="p"
                                        style="mb1"
                                        mdStyle="b1"
                                        className="text-neutral01-75"
                                    >
                                        {item.bodyContent}
                                    </Text>
                                </motion.div>
                            );
                        })}
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default H2BodyImageGrid;
