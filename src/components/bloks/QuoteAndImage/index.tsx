import Container from "@/components/ui/Container";
import { QuoteTextBlock } from "./QuoteTextBlock";
import { TestimonialCard } from "./TestimonialCard";
import { generateImageAltFromFilename } from "@/common/utils";
import { getDotBackgroundStyle } from "@/common/getDotBackgroundStyle";

interface QuoteAndImageProps {
    blok: {
        quote?: string;
        attribution?: string;
        studentTestimonial?: {
            AcceptedToLabel?: string;
            College?: string;
            Image?: {
                filename: string;
                alt?: string;
            };
            NameLabel?: string;
            Name: string;
        }[];
    };
}

const QuoteAndImage = ({ blok }: QuoteAndImageProps) => {
    const { quote, attribution, studentTestimonial } = blok;
    const testimonial = studentTestimonial?.[0];
    const {
        AcceptedToLabel,
        College,
        Image: imageObj,
        NameLabel,
        Name,
    } = testimonial ?? {};

    const altText = imageObj?.filename
        ? generateImageAltFromFilename(imageObj.filename)
        : "Student testimonial image";

    if (!quote && !imageObj) return null;

    return (
        <section className="bg-grays-G6">
            <Container className="container">
                <div
                    className="w-full py-[2.1rem] md:py-[6.3rem] lg:py-[3.4375rem]"
                    style={getDotBackgroundStyle()}
                >
                    <div className="md:items-center items-start px-0 xl:px-[1.65rem] 2xl:px-[6.8rem] w-auto mx-auto flex flex-col justify-between lg:flex-row max-w-[1290px]">
                        {imageObj?.filename && (
                            <TestimonialCard
                                acceptedToLabel={AcceptedToLabel}
                                college={College}
                                image={imageObj.filename}
                                alt={altText}
                                name={Name}
                                nameLabel={NameLabel}
                            />
                        )}

                        <div className="w-full pt-20 md:pt-0 lg:w-[49%] xl:w-[49.3%] 2xl:w-[51%]">
                            <QuoteTextBlock quote={quote} attribution={attribution} />
                        </div>
                    </div>
                </div>
            </Container>
        </section>
    );
};

export default QuoteAndImage;