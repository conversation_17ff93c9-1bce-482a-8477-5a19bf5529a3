import Text from "@/components/ui/Text";
import Image from "next/image";

export const TestimonialCard = ({
    acceptedToLabel,
    college,
    image,
    alt,
    name,
    nameLabel,
}: {
    acceptedToLabel?: string;
    college?: string;
    image?: string;
    alt: string;
    name?: string;
    nameLabel?: string;
}) => {

    return (
        <div className="bg-white w-[202px] md:w-[400px] drop-shadow-lg -rotate-2 px-5 py-[1.875rem] text-center mx-auto lg:mx-0">
            {acceptedToLabel && (
                <Text tag="p" style="c1" mdStyle="q4" className="text-primary01-50 font-display-serif line-clamp-1">
                    {acceptedToLabel}
                </Text>
            )}
            {college && (
                <Text tag="p" style="t1" mdStyle="h3" className="text-primary01-75 line-clamp-1">
                    {college}
                </Text>
            )}
            <div className="my-2 md:my-4 w-full aspect-[3/4] relative">
                {image && <Image
                    src={image}
                    alt={alt}
                    fill
                    className="object-cover"
                    sizes="400px"
                />}
            </div>
            {name && (
                <div className="flex items-center gap-3">
                    <Text tag="p" style="b4" mdStyle="b3" className="text-black w-auto min-w-10">
                        {nameLabel}
                    </Text>
                    <div className="border-b border-neutral01-50 border-dashed flex-1 text-left pl-2">
                        <Text tag="p" style="hw5" mdStyle="hw3" className="text-black">
                            {name}
                        </Text>
                    </div>
                </div>
            )}
        </div>
    );
};