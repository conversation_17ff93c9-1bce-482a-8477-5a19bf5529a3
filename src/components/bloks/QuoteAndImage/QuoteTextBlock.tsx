import Text from "@/components/ui/Text";
import { highlightText } from "@/common/renderHighlight";

export const QuoteTextBlock = ({
    quote,
    attribution,
}: {
    quote?: string;
    attribution?: string;
}) => {
    if (!quote) return null;

    const lines = quote.split("\n");

    const quoteParagraphs = lines.map((line, index) => {
        const isFirst = index === 0;
        const isLast = index === lines.length - 1;

        return (
            <p key={index} className="text-black mb-[40px] last:mb-0">
                {isFirst && "“ "}
                {highlightText(line, "italic text-primary01-50")}
                {isLast && " ”"}
            </p>
        );
    });

    return (
        <>
            <Text tag="span" style="q4" mdStyle="q2" xlStyle="q1">
                {quoteParagraphs}
            </Text>
            {attribution && (
                <Text
                    tag="p"
                    style="b3"
                    className="text-neutral01-75 mt-8 line-clamp-2 md:line-clamp-1"
                >
                    {attribution}
                </Text>
            )}
        </>
    );
};