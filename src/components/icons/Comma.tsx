import React from "react";

const Comma: React.FC<React.HTMLAttributes<HTMLOrSVGElement>> = (
  args,
) => {
  return (
    <svg
      {...args}
      width="22"
      height="18"
      viewBox="0 0 22 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.88689 0.599577L8.7289 2.31002C7.83764 2.67616 7.02365 3.13854 6.28898 3.69828L6.28889 3.69817L6.27965 3.70557C5.3877 4.41913 4.64662 5.22622 4.06078 6.12751L4.05588 6.13504L4.05125 6.14275C3.51502 7.03648 3.13171 7.95852 2.9056 8.90819L2.81376 9.2939L3.1684 9.47122L3.4244 9.59922L3.70079 9.73741L3.948 9.552C4.06602 9.46349 4.22823 9.37897 4.44612 9.30634C4.62394 9.24707 4.94259 9.204 5.44 9.204C5.85228 9.204 6.30456 9.32388 6.80295 9.59224C7.26772 9.8425 7.65053 10.2202 7.94988 10.7441L7.9606 10.7628L7.9729 10.7806C8.28038 11.2247 8.46 11.8605 8.46 12.736C8.46 13.4161 8.29123 14.0308 7.95525 14.5908L7.94876 14.6016L7.94282 14.6127C7.64442 15.1722 7.21522 15.6428 6.64459 16.0253C6.08736 16.3573 5.47602 16.524 4.8 16.524C3.91418 16.524 3.16661 16.2956 2.5361 15.853C1.89257 15.3586 1.39158 14.6891 1.03877 13.8267C0.68332 12.9578 0.5 11.9571 0.5 10.816C0.5 9.4219 0.725072 8.09332 1.17373 6.8274C1.66293 5.52446 2.39725 4.3197 3.3817 3.21218C4.29097 2.18926 5.45592 1.3167 6.88689 0.599577ZM18.9189 0.599577L20.7609 2.31002C19.8696 2.67616 19.0557 3.13854 18.321 3.69828L18.3209 3.69817L18.3117 3.70557C17.4197 4.41913 16.6786 5.22622 16.0928 6.12751L16.0879 6.13505L16.0833 6.14275C15.547 7.03648 15.1637 7.95852 14.9376 8.90819L14.8458 9.2939L15.2004 9.47122L15.4564 9.59922L15.7328 9.73741L15.98 9.552C16.098 9.46349 16.2602 9.37897 16.4781 9.30634C16.6559 9.24707 16.9746 9.204 17.472 9.204C17.8843 9.204 18.3366 9.32388 18.835 9.59224C19.2997 9.84249 19.6825 10.2202 19.9819 10.7441L19.9926 10.7628L20.0049 10.7806C20.3124 11.2247 20.492 11.8605 20.492 12.736C20.492 13.4161 20.3232 14.0308 19.9873 14.5908L19.9808 14.6016L19.9748 14.6127C19.6764 15.1722 19.2472 15.6428 18.6766 16.0253C18.1194 16.3573 17.508 16.524 16.832 16.524C15.9462 16.524 15.1986 16.2956 14.5681 15.853C13.9246 15.3586 13.4236 14.6891 13.0708 13.8267C12.7153 12.9578 12.532 11.9571 12.532 10.816C12.532 9.4219 12.7571 8.09331 13.2057 6.82739C13.6949 5.52446 14.4292 4.31969 15.4137 3.21218C16.323 2.18926 17.4879 1.3167 18.9189 0.599577Z"
        fill="#F5170D"
        stroke="#F5170D"
      />
    </svg>
  );
};

export default Comma;
