import React from "react";

const Forward15S: React.FC<React.HTMLAttributes<HTMLOrSVGElement>> = (args) => {
  return (
    <svg
      width="30"
      height="33"
      viewBox="0 0 30 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...args}
    >
      <path
        d="M17.2907 16.9484C17.4857 16.9094 17.672 16.8812 17.8497 16.8639C18.0317 16.8465 18.2072 16.8379 18.3762 16.8379C18.8702 16.8379 19.3057 16.9137 19.6827 17.0654C20.064 17.2127 20.3825 17.4164 20.6382 17.6764C20.8982 17.9364 21.0932 18.2419 21.2232 18.5929C21.3532 18.9439 21.4182 19.3209 21.4182 19.7239C21.4182 20.2265 21.3293 20.6859 21.1517 21.1019C20.974 21.5135 20.727 21.8689 20.4107 22.1679C20.0987 22.4625 19.7238 22.6922 19.2862 22.8569C18.8528 23.0215 18.3783 23.1039 17.8627 23.1039C17.5637 23.1039 17.2777 23.0714 17.0047 23.0064C16.7317 22.9457 16.476 22.8634 16.2377 22.7594C16.0037 22.651 15.7848 22.5275 15.5812 22.3889C15.3775 22.2502 15.1955 22.105 15.0352 21.9534L15.5877 21.1994C15.7047 21.039 15.8563 20.9589 16.0427 20.9589C16.1597 20.9589 16.2745 20.9935 16.3872 21.0629C16.4998 21.1322 16.6277 21.2102 16.7707 21.2969C16.9137 21.3792 17.0805 21.455 17.2712 21.5244C17.4618 21.5937 17.6937 21.6284 17.9667 21.6284C18.2483 21.6284 18.4932 21.5829 18.7012 21.4919C18.9092 21.3965 19.0803 21.2687 19.2147 21.1084C19.3533 20.9437 19.4552 20.7509 19.5202 20.5299C19.5895 20.3045 19.6242 20.064 19.6242 19.8084C19.6242 19.3187 19.4833 18.9417 19.2017 18.6774C18.9243 18.4087 18.5192 18.2744 17.9862 18.2744C17.5485 18.2744 17.1087 18.3567 16.6667 18.5214L15.5552 18.2094L16.3482 13.5684H21.0802V14.3224C21.0802 14.448 21.0607 14.5629 21.0217 14.6669C20.987 14.7665 20.9263 14.8554 20.8397 14.9334C20.7573 15.007 20.649 15.0655 20.5147 15.1089C20.3847 15.1479 20.2243 15.1674 20.0337 15.1674H17.5962L17.2907 16.9484Z"
        fill="#949494"
      />
      <path
        d="M13.7736 21.681V23.0005H8.48258V21.681H10.3546V16.52C10.3546 16.4073 10.3567 16.2925 10.3611 16.1755C10.3654 16.0585 10.3719 15.9393 10.3806 15.818L9.14558 16.845C9.05891 16.91 8.97441 16.9512 8.89208 16.9685C8.80974 16.9858 8.73174 16.988 8.65808 16.975C8.58441 16.9577 8.51941 16.9317 8.46308 16.897C8.40674 16.858 8.36341 16.819 8.33308 16.78L7.76758 16.0195L10.6666 13.5625H12.1421V21.681H13.7736Z"
        fill="#949494"
      />
      <path
        d="M14.9414 32.7254C12.8809 32.7254 10.9473 32.3348 9.14062 31.5535C7.33398 30.782 5.74707 29.7127 4.37988 28.3455C3.0127 26.9783 1.93848 25.3914 1.15723 23.5848C0.385742 21.7781 0 19.8445 0 17.784C0 15.9578 0.307617 14.2293 0.922852 12.5984C1.53809 10.9676 2.39258 9.49784 3.48633 8.18925C4.58984 6.88065 5.87402 5.79179 7.33887 4.92265C8.81348 4.04374 10.4053 3.44315 12.1143 3.12089V1.04081C12.1143 0.689247 12.1826 0.425576 12.3193 0.249794C12.4658 0.0740132 12.6562 -0.00899465 12.8906 0.00077097C13.125 0.0105366 13.3789 0.108193 13.6523 0.29374L18.3105 3.57499C18.6523 3.81913 18.8184 4.09745 18.8086 4.40995C18.8086 4.72245 18.6426 4.99589 18.3105 5.23026L13.6377 8.51151C13.3643 8.69706 13.1104 8.79472 12.876 8.80448C12.6514 8.80448 12.4658 8.71659 12.3193 8.54081C12.1826 8.36503 12.1143 8.10624 12.1143 7.76444V5.65507C10.7373 5.97733 9.46289 6.50956 8.29102 7.25175C7.12891 7.99394 6.11328 8.90214 5.24414 9.97636C4.375 11.0408 3.69629 12.2371 3.20801 13.5652C2.72949 14.8933 2.49023 16.2996 2.49023 17.784C2.49023 19.5027 2.8125 21.1141 3.45703 22.618C4.10156 24.1219 4.99512 25.4451 6.1377 26.5877C7.28027 27.7303 8.60352 28.6238 10.1074 29.2683C11.6113 29.9129 13.2227 30.2351 14.9414 30.2351C16.6602 30.2351 18.2715 29.9129 19.7754 29.2683C21.2793 28.6238 22.6025 27.7303 23.7451 26.5877C24.8877 25.4451 25.7812 24.1219 26.4258 22.618C27.0703 21.1141 27.3926 19.5027 27.3926 17.784C27.3926 16.3875 27.1777 15.0642 26.748 13.8142C26.3184 12.5642 25.7178 11.4217 24.9463 10.3865C24.1748 9.34159 23.2715 8.44804 22.2363 7.70585C21.9336 7.48124 21.7383 7.21269 21.6504 6.90019C21.5723 6.57792 21.626 6.26054 21.8115 5.94804C21.9971 5.65507 22.2705 5.4744 22.6318 5.40604C22.9932 5.33769 23.335 5.43046 23.6572 5.68436C24.917 6.59257 26.0107 7.67167 26.9385 8.92167C27.876 10.1619 28.5986 11.534 29.1064 13.0379C29.624 14.532 29.8828 16.1141 29.8828 17.784C29.8828 19.8445 29.4922 21.7781 28.7109 23.5848C27.9395 25.3914 26.8701 26.9783 25.5029 28.3455C24.1357 29.7127 22.5488 30.782 20.7422 31.5535C18.9355 32.3348 17.002 32.7254 14.9414 32.7254Z"
        fill="#949494"
      />
    </svg>
  );
};

export default Forward15S;
