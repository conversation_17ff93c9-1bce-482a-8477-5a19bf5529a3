import React from "react";

const Back15S: React.FC<React.HTMLAttributes<HTMLOrSVGElement>> = (args) => {
  return (
    <svg
      width="30"
      height="33"
      viewBox="0 0 30 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...args}
    >
      <path
        d="M17.2907 16.9484C17.4857 16.9094 17.672 16.8812 17.8497 16.8639C18.0317 16.8465 18.2072 16.8379 18.3762 16.8379C18.8702 16.8379 19.3057 16.9137 19.6827 17.0654C20.064 17.2127 20.3825 17.4164 20.6382 17.6764C20.8982 17.9364 21.0932 18.2419 21.2232 18.5929C21.3532 18.9439 21.4182 19.3209 21.4182 19.7239C21.4182 20.2265 21.3293 20.6859 21.1517 21.1019C20.974 21.5135 20.727 21.8689 20.4107 22.1679C20.0987 22.4625 19.7238 22.6922 19.2862 22.8569C18.8528 23.0215 18.3783 23.1039 17.8627 23.1039C17.5637 23.1039 17.2777 23.0714 17.0047 23.0064C16.7317 22.9457 16.476 22.8634 16.2377 22.7594C16.0037 22.651 15.7848 22.5275 15.5812 22.3889C15.3775 22.2502 15.1955 22.105 15.0352 21.9534L15.5877 21.1994C15.7047 21.039 15.8563 20.9589 16.0427 20.9589C16.1597 20.9589 16.2745 20.9935 16.3872 21.0629C16.4998 21.1322 16.6277 21.2102 16.7707 21.2969C16.9137 21.3792 17.0805 21.455 17.2712 21.5244C17.4618 21.5937 17.6937 21.6284 17.9667 21.6284C18.2483 21.6284 18.4932 21.5829 18.7012 21.4919C18.9092 21.3965 19.0803 21.2687 19.2147 21.1084C19.3533 20.9437 19.4552 20.7509 19.5202 20.5299C19.5895 20.3045 19.6242 20.064 19.6242 19.8084C19.6242 19.3187 19.4833 18.9417 19.2017 18.6774C18.9243 18.4087 18.5192 18.2744 17.9862 18.2744C17.5485 18.2744 17.1087 18.3567 16.6667 18.5214L15.5552 18.2094L16.3482 13.5684H21.0802V14.3224C21.0802 14.448 21.0607 14.5629 21.0217 14.6669C20.987 14.7665 20.9263 14.8554 20.8397 14.9334C20.7573 15.007 20.649 15.0655 20.5147 15.1089C20.3847 15.1479 20.2243 15.1674 20.0337 15.1674H17.5962L17.2907 16.9484Z"
        fill="#949494"
      />
      <path
        d="M13.7736 21.681V23.0005H8.48258V21.681H10.3546V16.52C10.3546 16.4073 10.3567 16.2925 10.3611 16.1755C10.3654 16.0585 10.3719 15.9393 10.3806 15.818L9.14558 16.845C9.05891 16.91 8.97441 16.9512 8.89208 16.9685C8.80974 16.9858 8.73174 16.988 8.65808 16.975C8.58441 16.9577 8.51941 16.9317 8.46308 16.897C8.40674 16.858 8.36341 16.819 8.33308 16.78L7.76758 16.0195L10.6666 13.5625H12.1421V21.681H13.7736Z"
        fill="#949494"
      />
      <path
        d="M14.9414 32.7254C17.002 32.7254 18.9355 32.3348 20.7422 31.5535C22.5488 30.782 24.1357 29.7127 25.5029 28.3455C26.8701 26.9783 27.9443 25.3914 28.7256 23.5848C29.4971 21.7781 29.8828 19.8445 29.8828 17.784C29.8828 15.9578 29.5752 14.2293 28.96 12.5984C28.3447 10.9676 27.4902 9.49784 26.3965 8.18925C25.293 6.88065 24.0088 5.79179 22.5439 4.92265C21.0693 4.04374 19.4775 3.44315 17.7686 3.12089V1.04081C17.7686 0.689247 17.7002 0.425576 17.5635 0.249794C17.417 0.0740132 17.2266 -0.00899465 16.9922 0.00077097C16.7578 0.0105366 16.5039 0.108193 16.2305 0.29374L11.5723 3.57499C11.2305 3.81913 11.0645 4.09745 11.0742 4.40995C11.0742 4.72245 11.2402 4.99589 11.5723 5.23026L16.2451 8.51151C16.5186 8.69706 16.7725 8.79472 17.0068 8.80448C17.2314 8.80448 17.417 8.71659 17.5635 8.54081C17.7002 8.36503 17.7686 8.10624 17.7686 7.76444V5.65507C19.1455 5.97733 20.4199 6.50956 21.5918 7.25175C22.7539 7.99394 23.7695 8.90214 24.6387 9.97636C25.5078 11.0408 26.1865 12.2371 26.6748 13.5652C27.1533 14.8933 27.3926 16.2996 27.3926 17.784C27.3926 19.5027 27.0703 21.1141 26.4258 22.618C25.7812 24.1219 24.8877 25.4451 23.7451 26.5877C22.6025 27.7303 21.2793 28.6238 19.7754 29.2683C18.2715 29.9129 16.6602 30.2351 14.9414 30.2351C13.2227 30.2351 11.6113 29.9129 10.1074 29.2683C8.60352 28.6238 7.28027 27.7303 6.1377 26.5877C4.99512 25.4451 4.10156 24.1219 3.45703 22.618C2.8125 21.1141 2.49023 19.5027 2.49023 17.784C2.49023 16.3875 2.70508 15.0642 3.13477 13.8142C3.56445 12.5642 4.16504 11.4217 4.93652 10.3865C5.70801 9.34159 6.61133 8.44804 7.64648 7.70585C7.94922 7.48124 8.14453 7.21269 8.23242 6.90019C8.31055 6.57792 8.25684 6.26054 8.07129 5.94804C7.88574 5.65507 7.6123 5.4744 7.25098 5.40604C6.88965 5.33769 6.54785 5.43046 6.22559 5.68436C4.96582 6.59257 3.87207 7.67167 2.94434 8.92167C2.00684 10.1619 1.28418 11.534 0.776367 13.0379C0.258789 14.532 0 16.1141 0 17.784C0 19.8445 0.390625 21.7781 1.17188 23.5848C1.94336 25.3914 3.0127 26.9783 4.37988 28.3455C5.74707 29.7127 7.33398 30.782 9.14062 31.5535C10.9473 32.3348 12.8809 32.7254 14.9414 32.7254Z"
        fill="#949494"
      />
    </svg>
  );
};

export default Back15S;
