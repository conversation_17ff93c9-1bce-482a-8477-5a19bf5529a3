import * as React from "react";

function RedTriangleWarning(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.40087 3.50315C10.5554 1.50204 13.4435 1.50204 14.598 3.50315L21.9523 16.2507C23.1062 18.2507 21.6627 20.7498 19.3538 20.7498H4.6451C2.33612 20.7498 0.892695 18.2507 2.04654 16.2507L9.40087 3.50315ZM11.9996 8.74976C12.4138 8.74976 12.7496 9.08554 12.7496 9.49976V13.2498C12.7496 13.664 12.4138 13.9998 11.9996 13.9998C11.5854 13.9998 11.2496 13.664 11.2496 13.2498V9.49976C11.2496 9.08554 11.5854 8.74976 11.9996 8.74976ZM11.9996 16.9998C12.4138 16.9998 12.7496 16.664 12.7496 16.2498C12.7496 15.8355 12.4138 15.4998 11.9996 15.4998C11.5854 15.4998 11.2496 15.8355 11.2496 16.2498C11.2496 16.664 11.5854 16.9998 11.9996 16.9998Z"
        fill="#F5170D"
      />
    </svg>
  );
}

export default RedTriangleWarning;
