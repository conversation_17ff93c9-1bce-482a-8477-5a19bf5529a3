import React from "react";

const GroupIcon: React.FC<
  React.HTMLAttributes<HTMLOrSVGElement> & {
    width?: number;
    height?: number;
  }
> = (args) => {
  return (
    <svg
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...args}
    >
      <path
        d="M11.911 12.4543H0.519226C0.381519 12.4543 0.249451 12.3996 0.152078 12.3022C0.0547039 12.2048 0 12.0727 0 11.935V6.13788C0 6.00017 0.0547039 5.8681 0.152078 5.77073C0.249451 5.67336 0.381519 5.61865 0.519226 5.61865H11.911C12.0488 5.61865 12.1808 5.67336 12.2782 5.77073C12.3756 5.8681 12.4303 6.00017 12.4303 6.13788V11.935C12.4303 12.0727 12.3756 12.2048 12.2782 12.3022C12.1808 12.3996 12.0488 12.4543 11.911 12.4543ZM1.03845 11.4158H11.3918V6.6571H1.03845V11.4158Z"
        fill="white"
      />
      <path
        d="M10.872 6.65699H1.55707C1.41936 6.65699 1.28729 6.60229 1.18992 6.50492C1.09255 6.40754 1.03784 6.27548 1.03784 6.13777V3.34174C1.03784 3.20403 1.09255 3.07196 1.18992 2.97459C1.28729 2.87721 1.41936 2.82251 1.55707 2.82251H10.872C11.0097 2.82251 11.1418 2.87721 11.2391 2.97459C11.3365 3.07196 11.3912 3.20403 11.3912 3.34174V6.13777C11.3912 6.27548 11.3365 6.40754 11.2391 6.50492C11.1418 6.60229 11.0097 6.65699 10.872 6.65699ZM2.07629 5.61854H10.3528V3.86096H2.07629V5.61854Z"
        fill="white"
      />
      <path
        d="M9.74623 3.86116H2.68475C2.54705 3.86116 2.41498 3.80645 2.31761 3.70908C2.22023 3.6117 2.16553 3.47964 2.16553 3.34193V1.06512C2.16553 0.927417 2.22023 0.79535 2.31761 0.697976C2.41498 0.600602 2.54705 0.545898 2.68475 0.545898H9.74623C9.88393 0.545898 10.016 0.600602 10.1134 0.697976C10.2107 0.79535 10.2655 0.927417 10.2655 1.06512V3.34193C10.2655 3.47964 10.2107 3.6117 10.1134 3.70908C10.016 3.80645 9.88393 3.86116 9.74623 3.86116ZM3.20398 2.8227H9.227V1.58435H3.20398V2.8227Z"
        fill="white"
      />
    </svg>
  );
};

export default GroupIcon;
