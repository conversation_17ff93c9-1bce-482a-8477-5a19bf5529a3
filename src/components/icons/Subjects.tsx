import React from "react";
export const AddSubjectIcon: React.FC<
  React.HTMLAttributes<HTMLOrSVGElement>
> = (props) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
  >
    <path
      d="M0 8.81738C0 4.3991 3.58172 0.817383 8 0.817383C12.4183 0.817383 16 4.3991 16 8.81738C16 13.2357 12.4183 16.8174 8 16.8174C3.58172 16.8174 0 13.2357 0 8.81738Z"
      fill="#74070E"
    />
    <path d="M5 8.81738H11" stroke="white" strokeLinecap="round" />
    <path d="M8 5.81738L8 11.8174" stroke="white" strokeLinecap="round" />
  </svg>
);

export const RemoveSubjectIcon: React.FC<
  React.HTMLAttributes<HTMLOrSVGElement>
> = (props) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.5001 5.29557V5.52236C17.4548 5.60976 18.4017 5.72469 19.3398 5.86636C19.6871 5.91881 20.0332 5.97493 20.3781 6.03468C20.7863 6.10538 21.0598 6.49355 20.9891 6.90168C20.9184 7.30982 20.5302 7.58337 20.1221 7.51267C20.0525 7.50061 19.9829 7.48871 19.9131 7.47695L18.9077 20.5475C18.7875 22.1105 17.4842 23.3174 15.9166 23.3174H8.08369C6.51608 23.3174 5.21276 22.1105 5.09253 20.5475L4.0871 7.47695C4.0174 7.48871 3.94774 7.50061 3.87813 7.51267C3.47 7.58337 3.08183 7.30982 3.01113 6.90168C2.94043 6.49355 3.21398 6.10538 3.62211 6.03468C3.96701 5.97493 4.31315 5.91881 4.66048 5.86636C5.59858 5.72469 6.5454 5.60976 7.50012 5.52236V5.29557C7.50012 3.7311 8.71265 2.39557 10.3156 2.34429C10.8749 2.3264 11.4365 2.31738 12.0001 2.31738C12.5638 2.31738 13.1253 2.3264 13.6847 2.34429C15.2876 2.39557 16.5001 3.7311 16.5001 5.29557ZM10.3635 3.84352C10.9069 3.82614 11.4525 3.81738 12.0001 3.81738C12.5478 3.81738 13.0934 3.82614 13.6367 3.84352C14.3913 3.86766 15.0001 4.50132 15.0001 5.29557V5.40821C14.0078 5.34795 13.0075 5.31738 12.0001 5.31738C10.9928 5.31738 9.99249 5.34795 9.00012 5.40821V5.29557C9.00012 4.50132 9.6089 3.86766 10.3635 3.84352ZM10.0087 9.78856C9.9928 9.37465 9.64436 9.05202 9.23045 9.06794C8.81654 9.08386 8.49391 9.4323 8.50983 9.84621L8.85599 18.8462C8.8719 19.2601 9.22035 19.5827 9.63426 19.5668C10.0482 19.5509 10.3708 19.2025 10.3549 18.7886L10.0087 9.78856ZM15.4895 9.84621C15.5054 9.4323 15.1828 9.08386 14.7689 9.06794C14.355 9.05202 14.0065 9.37465 13.9906 9.78856L13.6444 18.7886C13.6285 19.2025 13.9512 19.5509 14.3651 19.5668C14.779 19.5827 15.1274 19.2601 15.1433 18.8462L15.4895 9.84621Z"
      fill="#74070E"
    />
  </svg>
);
