import React from "react";

const Awards: React.FC<React.HTMLAttributes<HTMLOrSVGElement>> = (
    args,
) => {
    return (
        <svg
            {...args}
            width="13" height="17" viewBox="0 0 13 17" fill="none" xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M6.49978 0.000114353C6.06233 0.000114353 5.62488 0.269987 5.47404 0.809734C5.42492 0.985748 5.33591 1.14645 5.21473 1.27791C5.09354 1.40936 4.94384 1.50761 4.7786 1.56413C4.61336 1.62065 4.43757 1.63374 4.26646 1.60226C4.09535 1.57078 3.93409 1.49568 3.79666 1.38348C2.95193 0.69711 1.77937 1.5981 2.13737 2.65847C2.38878 3.4054 1.85077 4.18846 1.10057 4.16721C0.0346089 4.13534 -0.411889 5.59095 0.467028 6.22845C0.610141 6.3322 0.72719 6.47121 0.808062 6.63347C0.888934 6.79572 0.931196 6.97634 0.931196 7.15972C0.931196 7.3431 0.888934 7.52372 0.808062 7.68598C0.72719 7.84823 0.610141 7.98724 0.467028 8.091C-0.411889 8.72743 0.0346089 10.1841 1.10057 10.1522C1.85177 10.131 2.38777 10.9119 2.13637 11.661C1.82865 12.5747 2.65829 13.3631 3.43362 13.124V15.8897C3.43362 16.5856 3.79163 16.8417 3.94649 16.9203C4.09935 16.9947 4.51467 17.1296 5.02654 16.694L6.0382 15.8249C6.28055 15.6177 6.72102 15.6198 6.96136 15.827L7.97302 16.6919C8.25258 16.9331 8.50399 17 8.69808 17C8.85898 17 8.98066 16.9543 9.05206 16.9192C9.20491 16.8417 9.56493 16.5856 9.56493 15.8897V13.124C10.3403 13.3631 11.1709 12.5747 10.8622 11.6599C10.804 11.4869 10.7865 11.3016 10.8113 11.1199C10.8361 10.9382 10.9024 10.7656 11.0045 10.6171C11.1066 10.4685 11.2414 10.3485 11.3972 10.2673C11.5531 10.1861 11.7253 10.1463 11.899 10.1512C12.9649 10.183 13.4125 8.72743 12.5325 8.08993C12.3894 7.98618 12.2724 7.84717 12.1915 7.68491C12.1106 7.52266 12.0684 7.34204 12.0684 7.15866C12.0684 6.97528 12.1106 6.79466 12.1915 6.6324C12.2724 6.47015 12.3894 6.33114 12.5325 6.22738C13.4114 5.59201 12.9649 4.13534 11.899 4.16615C11.7256 4.17119 11.5536 4.13154 11.398 4.0506C11.2423 3.96967 11.1077 3.8499 11.0057 3.70165C10.9037 3.5534 10.8374 3.38113 10.8126 3.19974C10.7878 3.01836 10.8051 2.83331 10.8632 2.6606C11.2202 1.60023 10.0486 0.699234 9.20391 1.3856C9.06631 1.49753 8.90499 1.57239 8.73388 1.60372C8.56277 1.63505 8.38702 1.62191 8.22179 1.56543C8.05657 1.50895 7.90684 1.41083 7.78553 1.27954C7.66422 1.14825 7.57498 0.987735 7.52552 0.811859C7.46351 0.575793 7.32922 0.368088 7.14363 0.221219C6.95805 0.0743498 6.73163 -0.00340392 6.49978 0.000114353ZM6.48972 2.84016C7.57401 2.841 8.61367 3.29639 9.38047 4.10635C10.1473 4.91631 10.5785 6.01465 10.5796 7.16025C10.5788 8.30604 10.1476 9.40465 9.38082 10.2148C8.61399 11.025 7.57418 11.4806 6.48972 11.4814C5.40544 11.4803 4.36588 11.0246 3.59927 10.2145C2.83266 9.4043 2.40165 8.30585 2.40085 7.16025C2.40191 6.01484 2.83304 4.91666 3.59963 4.10673C4.36621 3.2968 5.40561 2.84128 6.48972 2.84016ZM8.56634 12.0817V15.8376L7.59189 15.0036C6.97946 14.4776 6.02211 14.4776 5.40767 15.0036L4.43221 15.8376V12.0945C5.07991 12.3991 5.78075 12.5573 6.48972 12.5588C7.20609 12.5569 7.91392 12.3943 8.56634 12.0817Z" fill="#74070E" />
        </svg>
    );
};

export default Awards;
