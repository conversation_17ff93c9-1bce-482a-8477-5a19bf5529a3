import React from "react";

const Loading: React.FC<React.HTMLAttributes<HTMLOrSVGElement>> = (args) => {
  return (
    <svg
      {...args}
      width="33"
      height="33"
      viewBox="0 0 33 33"
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
    >
      <circle cx="16.5" cy="16.5" r="6">
        <animate
          attributeName="r"
          values="6;10;6"
          dur="1s"
          repeatCount="indefinite"
        />
      </circle>
    </svg>
  );
};

export default Loading;
