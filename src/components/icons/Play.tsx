import React, { MutableRefObject } from "react";

const PlayIcon: React.FC<
  React.HTMLAttributes<HTMLOrSVGElement> & {
    width?: number;
    height?: number;
    ref?: MutableRefObject<null>;
  }
> = (args) => {
  return (
    <svg
      width="26"
      height="26"
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...args}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13 24.5C19.3513 24.5 24.5 19.3513 24.5 13C24.5 6.64873 19.3513 1.5 13 1.5C6.64873 1.5 1.5 6.64873 1.5 13C1.5 19.3513 6.64873 24.5 13 24.5ZM13 26C20.1797 26 26 20.1797 26 13C26 5.8203 20.1797 0 13 0C5.8203 0 0 5.8203 0 13C0 20.1797 5.8203 26 13 26Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9 9.25559C9 8.30194 9.99996 7.69741 10.8172 8.15699L18.3647 12.4014C19.2118 12.8778 19.2118 14.1222 18.3647 14.5986L10.8172 18.843C9.99996 19.3026 9 18.6981 9 17.7444V9.25559Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default PlayIcon;
