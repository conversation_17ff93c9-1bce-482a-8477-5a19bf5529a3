import React from "react";

const LinkedInOutline: React.FC<React.HTMLAttributes<HTMLOrSVGElement>> = (args) => {
    return (

        <svg  {...args} width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 2.4C0 1.07452 1.07452 0 2.4 0H29.6C30.9255 0 32 1.07452 32 2.4V29.6C32 30.9255 30.9255 32 29.6 32H2.4C1.07452 32 0 30.9255 0 29.6V2.4Z" fill="black" fill-opacity="0.6" />
            <path d="M5 7.55556C5 6.8148 5.24775 6.20369 5.74324 5.72222C6.23873 5.24073 6.88289 5 7.67568 5C8.45432 5 9.08429 5.23702 9.56564 5.71111C10.0611 6.2 10.3089 6.83702 10.3089 7.62222C10.3089 8.33333 10.0682 8.92591 9.58687 9.4C9.09138 9.88889 8.44015 10.1333 7.6332 10.1333H7.61197C6.83333 10.1333 6.20335 9.88889 5.72201 9.4C5.24066 8.91111 5 8.29629 5 7.55556ZM5.27606 27V12.1556H9.99035V27H5.27606ZM12.6023 27H17.3166V18.7111C17.3166 18.1926 17.3732 17.7926 17.4865 17.5111C17.6847 17.0074 17.9855 16.5815 18.389 16.2333C18.7925 15.8852 19.2986 15.7111 19.9073 15.7111C21.4929 15.7111 22.2857 16.8296 22.2857 19.0667V27H27V18.4889C27 16.2963 26.5045 14.6333 25.5135 13.5C24.5225 12.3667 23.213 11.8 21.5849 11.8C19.7587 11.8 18.3359 12.6222 17.3166 14.2667V14.3111H17.2954L17.3166 14.2667V12.1556H12.6023C12.6306 12.6296 12.6448 14.1037 12.6448 16.5778C12.6448 19.0518 12.6306 22.5259 12.6023 27Z" fill="white" />
        </svg>


    );
};

export default LinkedInOutline;
