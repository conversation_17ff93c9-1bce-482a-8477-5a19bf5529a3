"use client";

import { generateImageAltFromFilename } from "@/common/utils";

interface Props {
    height?: string;
    width?: string;
    url: string;
    colorFilter?: string;
}

export default function ColorFilteredLogo({
    url,
    colorFilter = "brightness(0%)",
    width = "100px",
    height = "50px",
}: Props) {
    const alt = url ?? generateImageAltFromFilename(url) ?? 'publisher logo';
    return (
        <img
            src={url}
            alt={alt}
            style={{
                height,
                width,
                objectFit: "contain",
                filter: colorFilter,
                display: "inline-block",
            }}
        />
    );
}