/* eslint-disable @typescript-eslint/no-unsafe-member-access */
"use client";
import Image from "next/image";
import { generateImageAltFromFilename } from "@/common/utils";
import SideForm from "@/components/ui/SideForm";
import Alert from "@/components/ui/Alert";
import FormErrorMessage from "@/components/ui/FormErrorMessage";
import { usePageContext } from "@/components/context/PageContext";

interface Props {
    imageUrl: string;
    formHeading: string;
    formSubheading: string;
    formComponent: any[];
}


const ImageAndFormSection = ({ imageUrl, formHeading, formSubheading, formComponent }: Props) => {
    const { isVisualComposer } = usePageContext();

    const heading = formHeading;
    const subheading = formSubheading;
    const formId = formComponent[0]?._uid
    const formType = formComponent[0]?.type;

    return (
        <div className="relative w-full lg:w-[42%] px-0 md:px-10 lg:px-0">
            {imageUrl && <>
                <div className="absolute inset-0 z-0 flex flex-col items-center pointer-events-none -top-5 h-[440px] md:h-[680px]">
                    <div className="w-full h-[70%] bg-[#FF453A] opacity-80 blur-[120px] rounded-full" />
                    <div className="w-full h-2/5 bg-[#007AFF] opacity-30 blur-[120px] rounded-full" />
                </div>

                <div className="relative z-[1] w-[90%] md:w-[82%] m-auto aspect-[544/764]">
                    <Image
                        src={imageUrl}
                        alt={generateImageAltFromFilename(imageUrl) ?? "Ebook cover image"}
                        fill
                        className="w-full object-cover rounded-sm"
                    />
                </div>
            </>}

            <div className="h-0" id="standard-web-lead-form" />
            {formComponent && formComponent.length > 0 && (<div className="sticky top-[180px] 2xl:top-[220px] z-[1] w-full mt-10 pb-10 lg:pb-0 ">
                <div className="w-full md:translate-y-[-216px] lg:translate-y-[-150px] xl:translate-y-[-166px] 2xl:translate-y-[-200px]">
                    {(isVisualComposer && formType !== "dynamic") ? <div className="min-h-[200px] bg-white p-8 shadow-[0px_4px_32.6px_0px_rgba(0,0,0,0.15)] rounded-xl w-full">
                        <Alert theme="error">
                            ERROR: The following errors need to be resolved for the form to be
                            visible:
                        </Alert>
                        <div className="mt-7">
                            <FormErrorMessage>
                                Must Choose the <strong>Multi-Page</strong> option in the Form Layout
                            </FormErrorMessage>
                        </div>
                    </div> : <SideForm
                        formId={formId}
                        {...{ heading, subheading, formComponent }}
                    />}
                </div>
            </div>)}
        </div>
    );
}

export default ImageAndFormSection;