import { CheckCircleIcon } from "@heroicons/react/24/solid";
import Text from "@/components/ui/Text";

interface Props {
    heading: string;
    bodyContent: string;
    ebookContentLabel?: string;
    ebookContents?: { bodyContent: string }[];
}

const HeaderSection = ({ heading, bodyContent, ebookContentLabel, ebookContents }: Props) => (
    <>
        <Text tag="h1" style="mh1.5" mdStyle="h1" className="text-primary01-75 mb-10 md:mb-20">
            {heading}
        </Text>
        <Text tag="p" style="mb1" mdStyle="b1" className="text-neutral01-75">
            {bodyContent}
        </Text>

        {ebookContents && ebookContents?.length > 0 && (
            <div className="mt-20">
                <Text tag="h3" style="h5" className="text-primary01-75">
                    {ebookContentLabel}
                </Text>
                <ul className="list-none mb-10">
                    {ebookContents.map((content, index) => (
                        <li key={index} className="flex items-center mt-7">
                            <CheckCircleIcon className="size-6 text-primary01-50 mr-6 shrink-0" />
                            <Text tag="span" style="b1" className="text-black flex-1">
                                {content.bodyContent}
                            </Text>
                        </li>
                    ))}
                </ul>
            </div>
        )}
    </>
);

export default HeaderSection;