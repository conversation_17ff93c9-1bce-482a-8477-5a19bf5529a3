import PageDivider from "@/components/bloks/PageDivider";
import { StoryblokComponent } from "@storyblok/react/rsc";

interface Props {
    ebookPreview?: any[];
}

const PreviewSection = ({ ebookPreview }: Props) => {
    if (!ebookPreview || ebookPreview.length < 1) return null;

    return (
        <>
            <PageDivider />
            <StoryblokComponent blok={ebookPreview[0]} />
        </>
    );
};

export default PreviewSection;