"use client";

import { useEffect } from "react";
import { usePageContext } from "@/components/context/PageContext";
import CRIM<PERSON><PERSON>OGO from "@/images/crimson-education.svg";
const CRIMSONLOGO_STRING: string = CRIMSONLOGO as unknown as string;

type Props = {
    stickyCTALabel: string;
    stickyNavigationMessage?: string;
};

const EbookPageContextBridge = ({
    stickyCTALabel,
    stickyNavigationMessage,
}: Props) => {
    const {
        hideNavFooterActions,
        stickyBannerActions,
        navLogoOverrideActions,
    } = usePageContext();

    useEffect(() => {
        hideNavFooterActions?.update(true);
        navLogoOverrideActions?.update(CRIMSONLOGO_STRING);
        stickyBannerActions?.update(
            stickyNavigationMessage ?? "",
            stickyCTALabel ?? ""
        );

        return () => {
            hideNavFooterActions?.reset?.();
            navLogoOverrideActions?.reset?.();
            stickyBannerActions?.reset?.();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [stickyCTALabel, stickyNavigationMessage]);

    return null;
};

export default EbookPageContextBridge;