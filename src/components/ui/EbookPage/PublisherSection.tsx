import Text from "@/components/ui/Text";
import ColorFilteredLogo from "./ColorFilteredLogo";

interface Props {
    publisherLabel: string;
    publisherLogos: string[];
}

const PublisherSection = ({ publisherLabel, publisherLogos }: Props) => {
    if (!publisherLogos?.length) return null;

    return (
        <div className="pt-10 border-t border-neutral01-0">
            <Text tag="p" style="h5" className="text-primary01-75 mb-2">
                {publisherLabel}
            </Text>
            <div className="flex items-center -ml-5">
                {publisherLogos.map((url, index) => (
                    <ColorFilteredLogo key={index} url={url} colorFilter="brightness(0%)" width="auto" height="50px" />
                ))}
            </div>
        </div>
    );
};

export default PublisherSection;