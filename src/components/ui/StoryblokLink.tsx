/**
 * This component is very specifically used to manage the `Link` field data type from storyblok.
 * Not to be confused with the `GeneralLink` blok component.
 */

import { IStoryblokLink } from "@/common/types";
import Link from "next/link";

interface Props {
  link: IStoryblokLink;
  children: React.ReactNode;
  className?: string;
}

const StoryblokLink = ({ link, children, className }: Props) => {
  const href = link?.story?.url ?? link?.url ?? link?.cached_url;

  return (
    <Link href={href} className={className} target={link?.target ?? "_self"}>
      {children}
    </Link>
  );
};

export default StoryblokLink;
