"use client";

import { useState, useEffect, SyntheticEvent, useRef } from "react";
import DropdownHeadless from "@/components/ui/SimpleDropdown/DropdownHeadless";
import { ArrowUpOnSquareIcon } from "@heroicons/react/24/outline";
import { LinkIcon } from "@heroicons/react/24/solid";
import Twitter from "@/components/icons/Twitter";
import LinkedIn from "@/components/icons/LinkedIn";
import Facebook from "@/components/icons/Facebook";
import {
  FacebookShareButton,
  TwitterShareButton,
  LinkedinShareButton,
} from "react-share";
import { cn, copyToClipboard, getShareLink } from "@/common/utils";
import { useSupportTouch } from "@/common/hooks/useIsMobile";

interface ShareButtonProps {
  title: string;
  fullSlug: string;
  iconColor?: string;
}

const ShareButton = ({ title, fullSlug, iconColor = "" }: ShareButtonProps) => {
  const [mounted, setMounted] = useState<boolean>(false);
  const [dropdownPosition, setDropdownPosition] = useState<
    "left" | "center" | "right"
  >("center");
  const containerRef = useRef<HTMLDivElement>(null);

  const [isHovered, setIsHovered] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const timer = requestAnimationFrame(() => {
      setMounted(true);
    });
    return () => cancelAnimationFrame(timer);
  }, []);

  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  const calculateDropdownPosition = () => {
    if (!containerRef.current) return;
    const rect = containerRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const dropdownWidth = 224;

    if (rect.left < dropdownWidth / 2) {
      setDropdownPosition("left");
    } else if (rect.right + dropdownWidth / 2 > viewportWidth) {
      setDropdownPosition("right");
    } else {
      setDropdownPosition("center");
    }
  };

  const handleMouseEnter = () => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setIsHovered(true);
    calculateDropdownPosition();
  };

  const handleMouseLeave = () => {
    hoverTimeoutRef.current = setTimeout(() => {
      setIsHovered(false);
    }, 300);
  };

  const shareLink = getShareLink(fullSlug);
  const useTouch = useSupportTouch();

  if (!mounted) return null;

  const getDropdownClasses = () => {
    const baseClasses =
      "absolute z-10 w-56 min-w-32 rounded-lg border border-none bg-white shadow-lg transition-opacity duration-150";

    switch (dropdownPosition) {
      case "left":
        return `${baseClasses} mt-2 left-0`;
      case "right":
        return `${baseClasses} mt-2 right-0`;
      default:
        return `${baseClasses} mt-2 left-0`;
    }
  };

  const getArrowClasses = () => {
    switch (dropdownPosition) {
      case "left":
        return "absolute -top-sm left-4 size-0 border-x-8 border-b-8 border-transparent border-b-white";
      case "right":
        return "absolute -top-sm right-4 size-0 border-x-8 border-b-8 border-transparent border-b-white";
      default:
        return "absolute -top-sm left-4 size-0 border-x-8 border-b-8 border-transparent border-b-white";
    }
  };

  return (
    <>
      {useTouch ? (
        <div>
          <ArrowUpOnSquareIcon
            onClick={async (e: SyntheticEvent) => {
              e.stopPropagation();
              await navigator.share({
                text: title,
                title: title,
                url: shareLink,
              });
            }}
            className={cn(
              "hover:stroke-gray-200 size-7 cursor-pointer",
              iconColor,
            )}
          />
        </div>
      ) : (
        <DropdownHeadless>
          {({ anchorRef, dropdownRef }) => {
            const shouldShowDropdown = isHovered;
            return (
              <div
                ref={containerRef}
                className="relative w-fit"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <div ref={anchorRef}>
                  <ArrowUpOnSquareIcon
                    className={cn(
                      "size-7 cursor-pointer hover:stroke-primary01-50",
                      iconColor,
                    )}
                  />
                </div>
                {shouldShowDropdown && (
                  <div
                    ref={dropdownRef}
                    className={getDropdownClasses()}
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                  >
                    <div className={getArrowClasses()} />
                    <div className="rounded bg-white shadow-[0.5_0.5_1.25_rgba(0,0,0,0.05)]">
                      <div
                        className="flex h-5xl cursor-pointer items-center gap-2 px-5 hover:text-primary01-50"
                        onClick={async (e: SyntheticEvent) => {
                          e.stopPropagation();
                          await copyToClipboard(shareLink);
                        }}
                      >
                        <LinkIcon className="size-5" />
                        <span className="text-base">Copy link</span>
                      </div>
                      <div className="flex h-5xl items-center gap-2 px-5 hover:text-primary01-50">
                        <TwitterShareButton
                          className="flex items-center gap-2"
                          url={shareLink}
                        >
                          <Twitter className="size-5" />
                          <span className="text-base">Share on X</span>
                        </TwitterShareButton>
                      </div>
                      <div className="flex h-5xl items-center gap-2 px-5 hover:text-primary01-50">
                        <LinkedinShareButton
                          url={shareLink}
                          className="flex items-center gap-2"
                        >
                          <LinkedIn className="size-5" />
                          <span className="text-base">Share on LinkedIn</span>
                        </LinkedinShareButton>
                      </div>
                      <div className="flex h-5xl items-center gap-2 px-5 hover:text-primary01-50">
                        <FacebookShareButton
                          className="flex items-center gap-2"
                          url={shareLink}
                        >
                          <Facebook className="size-5" />
                          <span className="text-base">Share on Facebook</span>
                        </FacebookShareButton>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            );
          }}
        </DropdownHeadless>
      )}
    </>
  );
};

export default ShareButton;
