"use client";

import Button from "../Button";
import { ITestimonial } from "@/components/bloks/CaseStudiesSection/types";
import Image from "next/image";
import Text from "../Text";
import StoryblokLink from "../StoryblokLink";
import { getSlugWithoutLocale } from "@/common/utils";
import { usePageContext } from "@/components/context/PageContext";
export interface ICaseStudyCard {
  testimonial: ITestimonial;
  nameLabel: string;
  majorLabel: string;
  acceptToLabel: string;
  readTestimonialStoryLabel: string;
}

const CaseStudyCard = (props: ICaseStudyCard) => {
  const {
    testimonial,
    nameLabel,
    majorLabel,
    acceptToLabel,
    readTestimonialStoryLabel = "",
  } = props;
  const { testimonialPageSlug, testimonialPage } = testimonial;
  const { locale } = usePageContext();

  const fullTestimonialPageSlug = `${locale === "en" ? "" : `/${locale}/`}${getSlugWithoutLocale(testimonialPageSlug ?? "")}`;

  return (
    <div className="group relative flex size-full flex-none flex-col items-center gap-lg rounded-[4px] bg-white p-xl shadow-[0px_0px_10px_0px_rgba(86,65,46,0.10),4px_14px_25px_0px_rgba(86,65,46,0.20)] transition-all duration-300 lg:group-hover:bg-transparent lg:group-hover:shadow-none">
      <div className="flex h-full w-4/5 flex-col items-center gap-4 lg:w-full">
        <div className="flex flex-col items-center self-stretch">
          <Text
            tag="p"
            style="a2"
            lgStyle="q4"
            className="font-weight-400 text-center text-neutral01-100"
          >
            {acceptToLabel}
          </Text>
          <Text
            tag="p"
            style="h4"
            lgStyle="h3"
            className="text-nowrap text-center font-display-sans text-primary01-50"
          >
            {testimonial.college}
          </Text>
        </div>
        <div className="relative flex w-full flex-1 flex-col items-center gap-4">
          <div className="relative w-full self-stretch rounded-[4px] bg-white transition-all duration-300 lg:group-hover:bg-transparent lg:group-hover:opacity-0">
            <div className="aspect-[52/43] transition-opacity duration-300 lg:aspect-[5/6]">
              <Image
                src={testimonial?.image?.filename ?? undefined}
                alt="Testimonial"
                fill
                className="object-cover"
              />
            </div>
          </div>
          <div className="flex w-full flex-col items-start gap-[7px] transition-opacity duration-300 lg:group-hover:opacity-0">
            <div className="flex w-full items-center gap-md text-neutral01-100">
              <Text
                style="c1"
                className="!whitespace-nowrap !text-body-p-2xs"
                tag="span"
              >
                {nameLabel}
              </Text>
              <div className="flex w-full items-center border-b border-dashed border-neutral01-50 px-sm py-0">
                <Text tag="p" style="hw3" className="text-grays-G2">
                  {testimonial?.typedName}
                </Text>
              </div>
            </div>
            <div className="flex w-full items-center gap-md">
              <Text
                style="c1"
                className="!whitespace-nowrap !text-body-p-2xs"
                tag="span"
              >
                {majorLabel}
              </Text>
              <div className="flex w-full items-center border-b border-dashed border-neutral01-50 px-sm py-0">
                <Text tag="p" style="hw3" className="text-grays-G2">
                  {testimonial?.typedMajor}
                </Text>
              </div>
            </div>
          </div>

          {/* Summary for screens under 1024px */}
          <div className="flex w-full flex-1 flex-col items-center gap-4 lg:hidden">
            <Text
              style="q6"
              tag="p"
              className="text-center font-display-serif text-body-single-md text-neutral01-100"
            >
              {`“${testimonial.testimonialSummary}”`}
            </Text>
          </div>

          {/* Summary for screens over 1024px */}
          <div className="absolute inset-0 flex flex-col items-center justify-between px-2xl opacity-0 transition-opacity duration-300 lg:group-hover:opacity-100">
            <div className="flex flex-1 flex-col items-center justify-center gap-[21px]">
              <Text
                tag="p"
                style="q6"
                xlStyle="q4"
                className="text-center text-neutral01-100"
              >
                {`“${testimonial.testimonialSummary}”`}
              </Text>
              <div className="relative text-center">
                <Text tag="p" style="hw3" className="text-primary01-50">
                  {testimonial?.typedName}
                </Text>
              </div>
            </div>
            {testimonialPage && (
              <StoryblokLink
                link={{
                  url: fullTestimonialPageSlug ?? "",
                  newTab: true,
                  linktype: "",
                  cached_url: "",
                }}
              >
                <Button colour="maroon" theme="secondary">
                  {readTestimonialStoryLabel.replace(
                    "{}",
                    testimonial?.typedName ?? "",
                  )}
                </Button>
              </StoryblokLink>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseStudyCard;
