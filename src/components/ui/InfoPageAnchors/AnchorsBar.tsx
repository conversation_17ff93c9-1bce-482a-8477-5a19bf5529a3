"use client";

import Container from "@/components/ui/Container";
import { AnchorData } from "@/common/anchorsBar/anchorsBar";
import { useAnchorsBarSticky } from "@/common/anchorsBar/useAnchorsBarSticky";
import { cn } from "@/common/utils";
import { useActiveAnchor } from "@/common/anchorsBar/useActiveAnchor";
import { motion } from "motion/react";

interface AnchorBarProps {
    anchors: AnchorData[];
    className?: string;
}

const AnchorsBar = ({ anchors, className }: AnchorBarProps) => {
    const { stickyTop, direction } = useAnchorsBarSticky();
    const activeId = useActiveAnchor(anchors);

    const handleClick = (id: string) => {
        const el = document.getElementById(id);
        if (el) {
            el.scrollIntoView({ behavior: "smooth", block: "start" });
        }
    };

    return (
        <motion.div
            animate={{ top: direction === "up" ? stickyTop : 0 }}
            transition={{ type: "tween", duration: 0.3, ease: "easeInOut" }}
            className={cn(
                "sticky z-10 w-full bg-white",
                direction === "up" && "shadow-[0_-4px_6px_-4px_rgba(0,0,0,0.1)]",
                className
            )}
            style={{ position: "sticky" }}
        >
            <Container className="!py-0">
                <ul className="flex w-full justify-evenly">
                    {anchors.map(({ id, title }) => (
                        <li
                            key={id}
                            onClick={() => handleClick(id)}
                            className={cn(
                                " py-[10px] relative flex items-center 2xl:px-4 px-1 hover:text-primary01-75 transition-colors duration-300 ease-in-out font-display-sans font-medium border-b-2 cursor-pointer",
                                activeId === id
                                    ? "text-primary01-75 border-primary01-75"
                                    : "text-grays-G4 border-transparent hover:border-primary01-75"
                            )}
                        >
                            <span className="block text-sm w-full text-center">{title}</span>
                        </li>
                    ))}
                </ul>
            </Container>
        </motion.div>
    );
};

export default AnchorsBar;