"use client";

import { useState, useRef, useEffect } from "react";
import { EllipsisHorizontalIcon, XMarkIcon } from "@heroicons/react/16/solid";
import { AnchorData } from "@/common/anchorsBar/anchorsBar";
import { cn } from "@/common/utils";
import { useActiveAnchor } from "@/common/anchorsBar/useActiveAnchor";

interface Props {
    anchors: AnchorData[];
    className?: string;
}

const FloatingAnchorsMenu = ({ anchors, className }: Props) => {
    const [isOpen, setIsOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const activeId = useActiveAnchor(anchors);

    const toggleMenu = () => setIsOpen((prev) => !prev);
    const closeMenu = () => setIsOpen(false);

    useEffect(() => {
        if (!isOpen) return;

        const initialScrollY = window.scrollY;
        const pageHeight = window.innerHeight;
        const scrollThreshold = pageHeight * 0.1;

        const handlePointerDown = (event: PointerEvent) => {
            const target = event.target as Node;
            const isClickInsideMenu = menuRef.current?.contains(target);
            const isClickOnButton = buttonRef.current?.contains(target);
            if (!isClickInsideMenu && !isClickOnButton) {
                closeMenu();
            }
        };

        const handleScroll = () => {
            const currentScrollY = window.scrollY;
            if (Math.abs(currentScrollY - initialScrollY) > scrollThreshold) {
                closeMenu();
            }
        };

        document.addEventListener("pointerdown", handlePointerDown);
        window.addEventListener("scroll", handleScroll);

        return () => {
            document.removeEventListener("pointerdown", handlePointerDown);
            window.removeEventListener("scroll", handleScroll);
        };
    }, [isOpen]);

    const handleAnchorClick = (id: string) => {
        const el = document.getElementById(id);
        if (el) {
            el.scrollIntoView({ behavior: "smooth", block: "start" });
        }
        closeMenu();
    };

    return (
        <div className={cn(
            "fixed z-40 right-[30px] translate-x-0 bottom-[133px] md:bottom-[200px] lg:bottom-[100px]",
            className
        )}>
            <button
                ref={buttonRef}
                className="w-[40px] h-[24px] md:h-[40px] rounded-[46px] md:rounded-full bg-white shadow-[0px_4px_14.899999618530273px_0px_rgba(0,0,0,0.05)] flex items-center justify-center "
                onClick={toggleMenu}
            >
                {isOpen ? (
                    <XMarkIcon className="size-5 text-black" />
                ) : (
                    <EllipsisHorizontalIcon className="size-5 text-black" />
                )}
            </button>

            <div
                ref={menuRef}
                className={cn(
                    "absolute right-0 translate-x-0  bottom-[36px] md:bottom-[52px] w-fit rounded-3xl bg-white shadow-lg border-neutral01-30",
                    "transition-all duration-200 ease-[cubic-bezier(0.68,-0.55,0.265,1.55)]",
                    isOpen
                        ? "opacity-100 visible translate-y-0"
                        : "opacity-0 invisible translate-y-3"
                )}
            >
                <ul className="py-3 w-fit min-w-32">
                    {anchors.map(({ id, title }) => (
                        <li
                            key={id}
                            className={cn(
                                "px-6 py-2 text-sm cursor-pointer transition-colors duration-200 hover:text-primary01-75",
                                activeId === id
                                    ? "text-primary01-75"
                                    : "text-grays-G4"
                            )}
                            onClick={() => handleAnchorClick(id)}
                        >
                            {title}
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default FloatingAnchorsMenu;