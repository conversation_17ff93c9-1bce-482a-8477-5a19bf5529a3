"use client";

import React from "react";
import { AnchorData } from "@/common/anchorsBar/anchorsBar";

interface Props {
    duplicateItems: AnchorData[];
}

const DuplicateAnchorWarning = ({ duplicateItems }: Props) => {
    if (typeof window === "undefined") return null;

    const isVisualComposer = window.location.pathname === "/visual-composer";
    if (!isVisualComposer) return null;

    if (duplicateItems?.length === 0) return null;


    return (
        <div className="fixed top-12 left-1/3 z-50 -translate-x-1/2 bg-[#a9f7d9] rounded-lg">
            <div className="max-w-xl p-6 shadow-lg">
                <div className="text-lg font-semibold text-red-600">
                    ⚠️ Duplicate anchor IDs detected
                </div>
                <div className="mb-4 text-lg font-semibold text-red-600">
                    Must clear them and save the page to see changes
                </div>
                <ul className="max-h-[300px] overflow-auto text-sm list-disc pl-5">
                    {duplicateItems.map((d, i) => (
                        <li key={`${d.id}-${i}`}>
                            TOC Label: {d.title} <br />
                            Anchor ID: {d.id}
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default DuplicateAnchorWarning;