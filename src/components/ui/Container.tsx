import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";

type ContainerSize = "large" | "full" | "caseStudy";

type Props = {
  children: React.ReactNode;
  size?: ContainerSize;
  className?: string;
  style?: React.CSSProperties;
};

const containerVariants = cva("mx-auto py-4xl md:py-[4.69rem]", {
  variants: {
    size: {
      large:
        "w-full max-w-screen-2xl mx-auto px-[25px] md:px-[30px] xl:px-[75px] 4xl:px-0",
      full: "w-full max-w-full px-0 mx-0",
      caseStudy:
        "w-full max-w-screen-2xl mx-auto px-[25px] md:px-[30px] lg:px-[75px] xl:px-[75px] 2xl:px-[166px] 4xl:px-0",
    },
  },
  defaultVariants: {
    size: "large",
  },
});

const Container = ({ children, size = "large", className, style }: Props) => {
  return (
    <div className={cn(containerVariants({ size }), className)} style={style}>
      {children}
    </div>
  );
};

export default Container;
