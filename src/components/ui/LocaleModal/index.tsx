"use client";

import { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON>alog, DialogPanel } from "@headlessui/react";
import full_countries from "@/common/data/full_countries.json";
import { getLocation } from "./getUserLocation";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { fetchDataSource, fetchStoryBySlug } from "@/common/storyblok";
import Text from "../Text";
import { cn } from "@/common/utils";
import { getCurrentSite, getTranslatedCountry } from "./LocalePicker";
import Transalations from "./localePickerTranslations.json";
import { Language } from "./types";

interface LocaleRecommendation {
  currentLocale: string;
  correctLocale: string;
  correctLocaleLabel: string;
  correctUrl: string;
  alternateLocales: string[];
}

interface SbDataValue {
  data: {
    datasource_entries?: {
      dimension_value?: string;
      id: number;
      name: string;
      value: string;
    }[];
  };
}

type StoriesData = {
  stories: {
    content: {
      localePopupTitle: string;
      localePopupLeaveLabel: string;
      localePopupStayLabel: string;
      localePopupMoreLabel: string;
    };
  }[];
};

type StoryBlokData = {
  story: { content: unknown };
};

const getLocaleFromSlug = (fullPageSlug: string) => {
  const Regex = /^\/?([a-z]{2}|[a-z]{2}-[a-z]{2}|global-content)(\/.*)*$/i;
  const matchedLocales = Regex.exec(fullPageSlug);

  return matchedLocales?.[1];
};

const isValidUrl = async (url: string) => {
  try {
    const { data } = await fetchStoryBySlug(url, true);
    const { story } = data as StoryBlokData;

    return !!story;
  } catch {
    return false;
  }
};

const getNewUrl = (
  fullSlug: string,
  currentLocale: string,
  targetLocale: string,
) => {
  if (currentLocale == "/") return `${targetLocale}${fullSlug}`;
  return fullSlug.replace(currentLocale, targetLocale);
};

async function recommendLocale(): Promise<LocaleRecommendation | null> {
  const geoCountryRedirects = (
    (await fetchDataSource("locale-redirects", false, "", 500)) as SbDataValue
  )?.data?.datasource_entries;
  const userLocation = await getLocation();
  const country = userLocation.country.toLowerCase();

  // a user is in a certain country is allowed to be in any country in the list
  const correctLocales = geoCountryRedirects
    ?.find(
      (locale: { name: string; value: string }) => locale?.name === country,
    )
    ?.value?.split(",");
  const correctLocale = correctLocales?.[0] ?? "us";
  const currentLocale = getLocaleFromSlug(window.location.pathname) ?? "";

  const isNot404Page = await isValidUrl(window.location.pathname);

  if (currentLocale !== correctLocale && isNot404Page) {
    const countryName = full_countries.find(
      (country) => country["alpha-2"].toLowerCase() === correctLocale,
    );

    const correctUrl = getNewUrl(
      window.location.pathname,
      `/${currentLocale}`,
      `/${correctLocale}`,
    );

    const isValid = await isValidUrl(correctUrl);

    if (!isValid) return null;

    return {
      currentLocale,
      correctLocale,
      correctLocaleLabel:
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        countryName?.[currentLocale as keyof typeof countryName] ||
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        countryName?.name ||
        correctLocale,
      correctUrl,
      alternateLocales: correctLocales?.slice(1) ?? [],
    };
  }

  return null;
}

const LOCALE_PREFERENCE = "locale_preference";

export default function LocalePopup() {
  const [preference, setPreference] = useState<string | undefined>();
  const [isOpen, setIsOpen] = useState(true);
  const [locale, setLocale] = useState<string>();
  const [language, setLanguage] = useState<string>();
  const [localeConfig, setLocaleConfig] = useState<
    LocaleRecommendation | undefined
  >(undefined);

  function close() {
    setIsOpen(false);
  }

  function closeAndSavePreferebce() {
    sessionStorage.setItem(
      LOCALE_PREFERENCE,
      localeConfig?.currentLocale ?? "",
    );
    close();
  }

  useEffect(() => {
    let time: NodeJS.Timeout;
    const timeOut = () =>
      new Promise((resolve) => {
        time = setTimeout(() => {
          resolve(true);
        }, 2000);
      });

    const fetchData = async () => {
      await timeOut();
      const recommendedLocale = await recommendLocale();
      const shouldOpenModal =
        !!recommendedLocale &&
        !recommendedLocale.alternateLocales?.includes(
          // check if we have exceptions
          recommendedLocale?.currentLocale,
        );
      if (shouldOpenModal) {
        setLocaleConfig(recommendedLocale);
        setPreference(sessionStorage?.getItem(LOCALE_PREFERENCE) ?? "");

        setIsOpen(true);
      } else if (isOpen) {
        setIsOpen(false);
      }
    };

    fetchData().catch(() => setIsOpen(false));

    try {
      const lang: string = window?.navigator?.language?.toLowerCase();
      const transformedLang = {
        zh: "zh-cn",
        "pt-br": "pt",
        "pt-pt": "pt",
        "zh-hk": "zh-tw",
      }[lang];

      setLanguage(transformedLang ?? lang);
    } catch {
      setLanguage("en");
    }

    try {
      const currentLocale = getLocaleFromSlug(window.location.pathname);
      setLocale(currentLocale ?? "");
    } catch {
      setLocale("en");
    }

    return () => {
      if (time) clearTimeout(time);
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const translations =
    Transalations[(language ?? "en") as Language] ?? Transalations.en;
  const destination = getCurrentSite(
    localeConfig?.correctLocale ?? locale ?? "us",
  );
  const currentSite = useMemo(() => getCurrentSite(locale ?? ""), [locale]);
  const decidedToStay = preference === localeConfig?.currentLocale;
  const isModalOpen = isOpen !== false && !!localeConfig && !decidedToStay;

  if (!destination) return;

  return (
    <>
      <Dialog
        open={isModalOpen}
        as="div"
        className="relative z-10 focus:outline-none"
        onClose={close}
        __demoMode
      >
        <div className="fixed inset-0 z-10 w-screen bg-black/75">
          <div className="flex min-h-full items-center justify-center">
            <DialogPanel
              transition
              className="data-[closed]:transform-[scale(95%)] relative w-full max-w-[620px] rounded-[5px] bg-white p-8 backdrop-blur-2xl duration-300 ease-out data-[closed]:opacity-0"
            >
              <div className="flex gap-4">
                <Text
                  tag="h2"
                  style="h4"
                  className="flex-1 font-bold text-neutral01-100"
                >
                  {translations.leaveHeading
                    ?.replace("{C}", destination?.country ?? "")
                    ?.replace("{L}", destination?.language ?? "")}
                </Text>
                <XMarkIcon
                  className={"mt-1 size-7 cursor-pointer fill-primary01-100"}
                  onClick={close}
                />
              </div>
              {currentSite && (
                <div className="mt-2.5 border-l-2 border-l-grays-G4 px-2">
                  <Text tag="p" style="b3">
                    {translations.currentSiteLabel?.replace("{}", " ")}
                    <Text
                      tag="span"
                      style="t2"
                      className="font-bold text-primary01-100"
                    >
                      {getTranslatedCountry(
                        currentSite.country,
                        language ?? "en",
                      )}
                      <span className="ml-1 border-b border-b-grays-G4 font-normal text-grays-G4">
                        {currentSite.language}
                      </span>
                    </Text>
                  </Text>
                </div>
              )}
              <div className="mt-8 flex w-full flex-col justify-end gap-4 md:flex-row">
                <ModalButton
                  className="shrink-0 border border-primary01-75 text-primary01-75"
                  onClick={closeAndSavePreferebce}
                >
                  {translations.noLabel}
                </ModalButton>
                <ModalButton
                  as="a"
                  href={localeConfig?.correctUrl}
                  className="bg-primary01-75 text-white shadow-inner shadow-white/10"
                  onClick={close}
                >
                  {translations.yesLabel
                    ?.replace(
                      "{C}",
                      getTranslatedCountry(
                        destination?.country ?? "",
                        language ?? "en",
                      ),
                    )
                    ?.replace("{L}", destination?.language ?? "")}
                </ModalButton>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  );
}

const ModalButton = ({
  as,
  href,
  onClick,
  children,
  className,
}: {
  children: React.ReactNode;
  className: string;
  as?: "a";
  href?: string;
  onClick?: () => void;
}) => {
  return (
    <Button
      as={as}
      href={href}
      onClick={onClick}
      className={cn(
        "inline-flex cursor-pointer items-center gap-2 rounded-sm px-[30px] py-[12.5px] text-button outline-none",
        className,
      )}
    >
      {children}
    </Button>
  );
};