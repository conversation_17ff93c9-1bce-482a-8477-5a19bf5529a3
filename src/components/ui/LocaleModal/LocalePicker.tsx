"use client";

import React, { useState, useEffect, useMemo } from "react";
import { Dialog, DialogPanel } from "@headlessui/react";
import full_countries from "@/common/data/full_countries.json";
import Text from "@/components/ui/Text";
import { usePageContext } from "@/components/context/PageContext";
import { GlobeAltIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Transalations from "./localePickerTranslations.json";
import { Language, SiteInfo } from "./types";
import { additionalTranslations, entries } from "./constants";
import Button from "../Button";

export default function LocalePicker() {
  const [isOpen, setIsOpen] = useState(false);
  const { locale } = usePageContext();
  const [language, setLanguage] = useState<string>();

  function close() {
    setIsOpen(false);
  }

  const translate = (country: string) =>
    getTranslatedCountry(country, language ?? "en");

  const currentSite: SiteInfo | null = useMemo(
    () => getCurrentSite(locale),
    [locale],
  );
  const translations =
    Transalations[(language ?? "en") as Language] ?? Transalations.en;

  useEffect(() => {
    try {
      const lang: string = window?.navigator?.language?.toLowerCase();
      const transformedLang = {
        zh: "zh-cn",
        "pt-br": "pt",
        "pt-pt": "pt",
        "zh-hk": "zh-tw",
      }[lang];

      setLanguage(transformedLang ?? lang);
    } catch {
      setLanguage("en");
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";

      return () => {
        document.body.style.overflow = "unset";
      };
    }
  }, [isOpen]);

  return (
    <>
      <Button colour="maroon" theme="secondary" onClick={() => setIsOpen(true)}>
        <span className="flex w-full items-center justify-center gap-[0.19rem]">
          <GlobeAltIcon className="mr-2 size-6" />
          {`${currentSite?.language} ${currentSite?.country && `(${translate(currentSite?.country)})`}`}
        </span>
      </Button>
      <Dialog
        open={isOpen}
        as="div"
        className="relative z-10 focus:outline-none"
        onClose={close}
        __demoMode
      >
        <div className="fixed inset-0 z-10 w-screen bg-black/75">
          <div className="flex min-h-full items-center justify-center">
            <DialogPanel
              transition
              className="data-[closed]:transform-[scale(95%)] relative max-h-screen w-full max-w-[780px] overflow-y-auto rounded-[5px] bg-white p-8 backdrop-blur-2xl duration-300 ease-out data-[closed]:opacity-0"
            >
              <div className="mb-10">
                <Text
                  tag="h2"
                  style="h4"
                  className="flex flex-1 justify-between font-bold text-neutral01-100"
                >
                  {translations.header}
                  <XMarkIcon
                    className={"mt-1 size-7 cursor-pointer fill-primary01-100"}
                    onClick={close}
                  />
                </Text>
                {currentSite && (
                  <div className="mt-2.5 border-l-2 border-l-grays-G4 px-2">
                    <Text tag="p" style="b3">
                      {translations.currentSiteLabel?.replace("{}", " ")}
                      <Text
                        tag="span"
                        style="t2"
                        className="font-bold text-primary01-100"
                      >
                        {translate(currentSite?.country)}
                        <span className="ml-1 border-b border-b-grays-G4 font-normal text-grays-G4">
                          {currentSite?.language}
                        </span>
                      </Text>
                    </Text>
                  </div>
                )}
              </div>
              <div className="grid gap-x-[50px] gap-y-[60px] md:grid-cols-3 md:grid-rows-[auto_1fr] md:[&>*:nth-child(2)]:col-start-2 md:[&>*:nth-child(2)]:row-span-2">
                {(Object.keys(entries) as EntryKey[]).map((region) => (
                  <div key={region}>
                    <Text tag="h3" style="h5" className="mb-4">
                      {translations[region]}
                    </Text>
                    <ul>
                      {entries[region].map(({ country, language }, index) => (
                        <Text
                          key={`cl_${country}_${index}`}
                          tag="li"
                          style="b3"
                          className="mt-2 text-b3 font-bold text-primary01-100"
                        >
                          <span className="mr-2.5">{translate(country)}</span>{" "}
                          {language.map(({ label, link }, index) => (
                            <React.Fragment key={`ll_${label}`}>
                              <a
                                href={link}
                                key={label}
                                className="relative inline-block cursor-pointer text-b4 font-normal text-grays-G4 underline hover:text-primary01-50"
                              >
                                {label}
                              </a>
                              {index < language.length - 1 && (
                                <span className="mx-[2px] text-b4 font-normal text-grays-G4">
                                  /
                                </span>
                              )}
                            </React.Fragment>
                          ))}
                        </Text>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  );
}

type EntryKey = keyof typeof entries;

export const getTranslatedCountry = (country: string, language: string) => {
  if (
    language?.startsWith("en") &&
    (country === "USA" || country === "UAE" || country === "China Mainland")
  )
    return country;

  const target = full_countries.find((c) => {
    const isChina = country === "China Mainland";
    const isUK = country === "UK";
    const currentCountry = c.en?.toLowerCase();
    let testCountry = country?.toLowerCase();

    if (isChina) testCountry = "china";
    if (isUK) testCountry = "united kingdom";

    return (
      currentCountry === testCountry ||
      (testCountry === "usa" &&
        currentCountry === "united states of america") ||
      (testCountry === "uae" && currentCountry === "united arab emirates")
    );
  });

  return (
    target?.[language as Country] ??
    additionalTranslations[country]?.[language as Country] ??
    target?.en ??
    country
  );
};

type Country = keyof (typeof full_countries)[number];

export const getCurrentSite = (locale: string): SiteInfo | null => {
  let currentSite: SiteInfo | null = null;
  const strippedLocale = locale?.replaceAll("/", "");

  // descend through the object to find current site based on url locale
  Object.entries(entries).forEach(([_, countries]) =>
    countries.forEach((country) =>
      country.language.forEach((language) => {
        if (language.link?.replaceAll("/", "") === strippedLocale) {
          currentSite = {
            country: country.country,
            language: language.label,
            languages: country.language,
          };
        }
      }),
    ),
  );

  if (!currentSite && locale === "en") {
    return {
      country: "USA",
      language: "English",
      languages: [
        {
          label: "English",
          link: "/",
        },
      ],
    };
  }

  return currentSite;
};

