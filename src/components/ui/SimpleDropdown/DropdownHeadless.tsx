import { useSupportTouch } from "@/common/hooks/useIsMobile";
import {
  ReactNode,
  useRef,
  useState,
  useCallback,
  useEffect,
  RefObject,
} from "react";

interface DropdownRenderProps {
  isOpen: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
  anchorRef: RefObject<HTMLDivElement>;
  dropdownRef: RefObject<HTMLDivElement>;
  eventHandlers: {
    onClick?: () => void;
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
  };
}

interface DropdownProps {
  children: (props: DropdownRenderProps) => ReactNode;
}

export default function DropdownHeadless({ children }: DropdownProps) {
  const anchorRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const supportTouch = useSupportTouch();

  const open = useCallback(() => setIsOpen(true), []);
  const close = useCallback(() => setIsOpen(false), []);
  const toggle = useCallback(() => setIsOpen((prev) => !prev), []);

  // Optional: Close dropdown if clicked outside (touch or mouse)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (
        anchorRef.current &&
        dropdownRef.current &&
        !anchorRef.current.contains(event.target as Node) &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        close();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("touchstart", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchstart", handleClickOutside);
    };
  }, [close]);

  const eventHandlers = supportTouch
    ? {
        onClick: toggle, // tap to toggle
      }
    : {
        onMouseEnter: open,
        onMouseLeave: close,
      };

  return (
    <>
      {children({
        isOpen,
        open,
        close,
        toggle,
        anchorRef,
        dropdownRef,
        eventHandlers,
      })}
    </>
  );
}
