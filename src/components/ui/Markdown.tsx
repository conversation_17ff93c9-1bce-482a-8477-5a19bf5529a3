import ReactMarkdown from "react-markdown";
import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";
import type { Theme } from "@/common/types";

interface Props {
  body?: string;
  theme?: Theme;
  style?: "body1" | "body2" | "body3" | "b1";
  className?: string;
  allowedElements?: string[];
  components?: Record<string, React.ComponentType<any>>;
}

const markdownVariants = cva(
  "prose prose-li:marker:content-['+'] prose-li:marker:font-bold prose-li:marker:text-base max-w-none",
  {
    variants: {
      theme: {
        light: "prose-light",
        dark: "prose-dark",
      },
      style: {
        body1: "text-body1 font-medium",
        body2: "text-body2 font-medium",
        body3: "text-body3 font-normal",
        b1: "font-body-p !text-mb1 md:!text-b1",
      },
    },
    defaultVariants: {
      theme: "light",
      style: "body1",
    },
  },
);

const defaultComponents = {
  hr: ({ className, ...props }: React.HTMLAttributes<HTMLHRElement>) => (
    <hr className={cn("my-10 border-grays-G5", className)} {...props} />
  ),
};

/**
 * @deprecated - Use the MarkdownSection component instead. It has all the updated design system styles
 */
const Markdown = ({
  theme,
  body,
  style,
  className,
  allowedElements,
  components,
}: Props) => (
  <ReactMarkdown
    className={cn(markdownVariants({ style, theme }), className)}
    allowedElements={allowedElements}
    components={{ ...defaultComponents, ...components }}
  >
    {body}
  </ReactMarkdown>
);

export default Markdown;
