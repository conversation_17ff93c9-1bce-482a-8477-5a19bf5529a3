import Text from "@/components/ui/Text";

interface HeadingSectionProps {
  preHeading?: string;
  heading?: string;
  bodyContent?: string;
}

const HeadingSection = ({
  preHeading,
  heading,
  bodyContent,
}: HeadingSectionProps) => {
  return (
    <div className="text-center">
      <Text tag="h3" style="ph1" className="mb-2 italic text-primary01-50">
        {preHeading}
      </Text>
      <Text tag="h2" style="h2" className="text-primary01-75">
        {heading}
      </Text>
      <Text
        tag="p"
        style="b1"
        className="mx-auto mt-6 w-full text-neutral01-75 lg:w-3/4 xl:w-[59%]"
      >
        {bodyContent}
      </Text>
    </div>
  );
};

export default HeadingSection;
