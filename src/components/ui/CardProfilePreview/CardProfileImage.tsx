

import Text from "@/components/ui/Text";
import Image from "next/image";
import type { StaticImageData } from "next/image";

interface CardProfileImageprops {
    ProfileBgc: StaticImageData;
    profileImage: string;
    name: string;
    byline: string;
}

const CardProfileImage = ({ ProfileBgc, profileImage, name, byline }: CardProfileImageprops) => {
    const hasImage = Boolean(profileImage);
    return (
        <div className="relative w-full aspect-[332/368] shrink-0 ">
            <div className="absolute bottom-0 left-0 w-full h-[94%] z-[1]">
                <Image
                    src={ProfileBgc}
                    alt="Profile background"
                    fill
                    sizes="400px"
                    className="object-cover rounded-t object-top h-auto"
                />
            </div>

            {
                hasImage && (<div className="absolute [top:-3%] left-1/2 -translate-x-1/2 w-full h-[103%] z-[2]  min-h-full">
                    <Image
                        src={profileImage}
                        alt={name}
                        fill
                        priority
                        sizes="400px"
                        className="object-cover object-top rounded-t"
                    />
                </div>)
            }


            <div
                className="absolute bottom-0 left-0 w-full h-2/5 z-[3] pointer-events-none"
                style={{
                    background:
                        "linear-gradient(180deg, rgba(0,0,0,0.00) 0%, rgba(0,0,0,0.52) 40%, rgba(0,0,0,0.90) 100%)",
                }}
            >
                <div className="h-full flex flex-col justify-end pb-[1.875rem] px-[1.875rem]">
                    <Text tag="p" style="sh4" className="text-white leading-tight line-clamp-1">
                        {name}
                    </Text>
                    <Text tag="p" style="q6" className="text-white mt-1 leading-tight line-clamp-2">
                        {byline}
                    </Text>
                </div>
            </div>
        </div>
    );
};

export default CardProfileImage;


