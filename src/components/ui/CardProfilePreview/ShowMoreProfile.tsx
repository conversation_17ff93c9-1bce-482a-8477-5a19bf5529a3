"use client";

import { useState, useRef } from "react";
import { cn } from "@/common/utils";
import ShowMore from "@/components/ui/CardProfilePreview/labelSection/ShowMore";

interface ShowMoreProfilesProps<T> {
    items: T[];
    batchSize?: number;
    renderItem: (
        item: T,
        index: number,
        ref?: React.RefObject<HTMLDivElement>
    ) => React.ReactNode;
}

const ShowMoreProfiles = <T,>({
    items,
    batchSize = 6,
    renderItem,
}: ShowMoreProfilesProps<T>) => {
    const [visibleCount, setVisibleCount] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);
    const scrollTargetRef = useRef<HTMLDivElement>(null);
    const lastVisibleCountRef = useRef(0);

    const visibleItems = items.slice(0, visibleCount);
    const hasMore = visibleCount < items.length;

    const handleShowMore = () => {
        const nextCount = Math.min(visibleCount + batchSize, items.length);
        lastVisibleCountRef.current = visibleCount;
        setVisibleCount(nextCount);
        setTimeout(() => {
            const el = scrollTargetRef.current;
            if (el) {
                const rect = el.getBoundingClientRect();
                const offset = window.innerWidth < 1024 ? 100 : 20;
                const scrollTop = window.scrollY + rect.top - offset;
                window.scrollTo({ top: scrollTop, behavior: "smooth" });
            }
        }, 100);
    };

    return (
        <>
            <div
                ref={containerRef}
                className={cn(
                    "transition-[max-height,opacity] duration-700 ease-in-out overflow-hidden pb-8",
                    visibleCount > 0 ? "max-h-none opacity-100" : "max-h-0 opacity-0"
                )}
            >
                <div className="flex flex-wrap gap-x-[2.375rem] gap-y-16 mt-16 mx-auto">
                    {visibleItems.map((item, index) => {
                        const isFirstNewItem = index === lastVisibleCountRef.current;
                        return renderItem(
                            item,
                            index,
                            isFirstNewItem ? scrollTargetRef : undefined
                        );
                    })}
                </div>
            </div>

            {hasMore && (
                <ShowMore handleShowMore={handleShowMore} className="text-center mt-10" />
            )}
        </>
    );
};

export default ShowMoreProfiles;