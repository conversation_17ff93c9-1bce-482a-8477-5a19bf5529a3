"use client";

import { useLabelTranslation } from "@/common/hooks/useTranslation";
import { cn } from "@/common/utils";
import Button from "@/components/ui/Button";

const ShowMore = ({
  handleShowMore,
  className,
}: {
  className?: string;
  handleShowMore: () => void;
}) => {
  const { t } = useLabelTranslation();
  return (
    <div className={cn("relative", className)}>
      <Button colour="maroon" theme="secondary" onClick={handleShowMore}>
        {t("Show More")} →
      </Button>
    </div>
  );
};

export default ShowMore;
