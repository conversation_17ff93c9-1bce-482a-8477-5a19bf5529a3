import Image from "next/image";
import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";

interface Props {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  flip?: boolean;
  fit?: "contain" | "cover";
  size?: "normal" | "full";
  sizes?: string;
  priority?: boolean;
  className?: string;
  fetchPriority?: "low" | "high";
  quality?: number;
}

const imageVariants = cva("object-center", {
  variants: {
    flip: {
      true: "-scale-x-100",
      false: "",
    },
    fit: {
      contain: "object-contain",
      cover: "object-cover",
    },
    size: {
      full: "size-full",
      normal: "",
    },
  },
  defaultVariants: {
    flip: false,
    fit: "contain",
    size: "normal",
  },
});

const DEFAULT_WIDTH = 1280;
const DEFAULT_HEIGHT = 400;

const ImageWrapper = ({
  src,
  alt,
  width,
  height,
  flip,
  fit,
  size,
  sizes,
  priority = false,
  className,
  fetchPriority,
  quality = 75,
}: Props) => {
  return (
    <Image
      src={src}
      alt={alt ?? "generic image"}
      width={width ?? DEFAULT_WIDTH}
      height={height ?? DEFAULT_HEIGHT}
      className={cn(imageVariants({ fit, flip, size }), className)}
      priority={priority}
      sizes={sizes}
      fetchPriority={fetchPriority}
      quality={quality}
    />
  );
};

export default ImageWrapper;
