import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";
import React from "react";
import Button from "./Button";

const iconVariants = cva("size-7", {
  variants: {
    disabled: {
      true: "fill-grays-G3",
      false: undefined,
    },
    colour: {
      white: "fill-white",
      maroon: "fill-neutral01-75",
    },
  },
  compoundVariants: [
    {
      disabled: true,
      colour: "white",
      className: "fill-grays-G3",
    },
    {
      disabled: true,
      colour: "maroon",
      className: "fill-grays-G3",
    },
  ],
});

interface Props {
  colour?: "maroon" | "white";
  disabled?: boolean;
  className?: string;
  Icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  iconClassName?: string;
  onClick?: () => void;
}

const IconButton = ({
  disabled,
  className,
  colour,
  Icon,
  onClick,
  iconClassName = "",
}: Props) => {
  return (
    <Button
      className={className}
      onClick={onClick}
      theme="icon"
      colour={colour}
      disabled={disabled}
    >
      <Icon className={cn(iconVariants({ disabled, colour }), iconClassName)} />
    </Button>
  );
};

export default IconButton;
