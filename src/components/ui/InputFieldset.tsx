import { Theme } from "@/common/types";
import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";
import React from "react";
import FormErrorMessage from "./FormErrorMessage";

import Text from "@/components/ui/Text";
import RedTriangleWarning from "../icons/RedTriangleWarning";
import MarkdownSection from "@/components/bloks/MarkdownSection";

interface Props {
  label?: string;
  theme?: Theme;
  children: React.ReactNode;
  size?: "block" | "inline";
  className?: string;
  hasValidationError?: boolean;
  errorMessage?: string;
  renderMarkdown?: boolean;
  flow?: "default" | "reverse";
  errorMessageIsIndependent?: boolean;
  nameOfField?: string;
  hideLabel?: boolean;
  hideErrorMessage?: boolean;
  removeLabelPadding?: boolean;
  isCheckbox?: boolean;
}

const inputFieldsetVariants = cva("flex flex-col rounded-md font-body-p", {
  variants: {
    size: {
      block: "",
      inline: "gap-x-[0.56rem]",
    },
    theme: {
      light: ["text-neutral01-100"],
      dark: ["text-white"],
    },
    flow: {
      default: "",
      reverse: "",
    },
  },
  defaultVariants: {
    size: "block",
    flow: "default",
    theme: "light",
  },
  compoundVariants: [
    {
      size: "inline",
      flow: "reverse",
      className: "flex-row-reverse justify-end items-center",
    },
    {
      size: "inline",
      flow: "default",
      className: "flex-row",
    },
    {
      size: "block",
      flow: "reverse",
      className: "flex-col-reverse",
    },
    {
      size: "block",
      flow: "default",
      className: "flex-col",
    },
  ],
});

const InputFieldset = (props: Props) => {
  const {
    label,
    theme,
    children,
    size,
    className,
    hasValidationError,
    errorMessage,
    renderMarkdown,
    flow,
    errorMessageIsIndependent,
    nameOfField,
    hideLabel,
    hideErrorMessage,
    isCheckbox = false,
  } = props;

  if (hideLabel) {
    return (
      <div className="w-full">
        <div
          className={cn(
            inputFieldsetVariants({ size, theme, flow }),
            className,
          )}
        >
          {children}
        </div>
        {hasValidationError && !hideErrorMessage && (
          <FormErrorMessage isIndependent={errorMessageIsIndependent}>
            {errorMessage}
          </FormErrorMessage>
        )}
      </div>
    );
  }

  return (
    <div className="w-full">
      <div
        className={cn(inputFieldsetVariants({ size, theme, flow }), className)}
        // className={cn(
        //   inputFieldsetVariants({ size, theme, flow }),
        //   isCheckbox &&
        //     size === "inline" &&
        //     flow === "reverse" &&
        //     "items-start",
        // className,
        // )}
      >
        {renderMarkdown && (
          <>
            {hasValidationError && isCheckbox && (
              <RedTriangleWarning className="ml-4" />
            )}
            <MarkdownSection
              blok={{
                bodyContent: label,
                theme,
              }}
              className={cn(
                "cursor-pointer !text-b1",
                theme === "light" ? "text-neutral01-100" : "text-white",
              )}
            />
          </>
        )}

        {!renderMarkdown && (
          <Text
            tag="label"
            style="mb1"
            mdStyle="b1"
            className={cn("cursor-pointer", isCheckbox ? "" : "pb-[0.3125rem]")}
            htmlFor={nameOfField}
          >
            {label}
          </Text>
        )}
        {children}
      </div>
      {hasValidationError && (
        <FormErrorMessage isIndependent={errorMessageIsIndependent}>
          {errorMessage}
        </FormErrorMessage>
      )}
    </div>
  );
};

export default InputFieldset;
