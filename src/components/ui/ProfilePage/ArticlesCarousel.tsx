"use client";

import React, { useEffect, useState } from "react";
import { formatLocalTime, cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import ArticlesCard from "./ArticlesCard";
import SlideCarousel from "@/components/ui/SlideCarousel";
import { IIndexHandlerProps } from "@/components/ui/SlideCarousel";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";

type Props = {
  articleList: IBlogStory[];
};

const ArticlesCarousel = ({ articleList }: Props) => {
  const [cardsPerView, setCardsPerView] = useState(1);

  useEffect(() => {
    const calculateCardsPerView = () => {
      const isMobile = window.innerWidth < 768;
      setCardsPerView(isMobile ? 1 : 3);
    };

    calculateCardsPerView();
    window.addEventListener("resize", calculateCardsPerView);
    return () => window.removeEventListener("resize", calculateCardsPerView);
  }, []);

  if (!articleList || articleList.length === 0) return null;

  const handleNext = ({
    currentIndex,
    setCurrentIndex,
    maxIndex,
  }: IIndexHandlerProps) => {
    const nextIndex = currentIndex + cardsPerView;
    setCurrentIndex(Math.min(nextIndex, maxIndex));
  };

  const handlePrev = ({
    currentIndex,
    setCurrentIndex,
  }: IIndexHandlerProps) => {
    const prevIndex = currentIndex - cardsPerView;
    setCurrentIndex(Math.max(prevIndex, 0));
  };

  return (
    <div className="w-full select-none overflow-hidden">
      <SlideCarousel
        transparent
        paginateByPage
        wrapperClassName="!px-0 !py-0"
        innerWrapperClassName="!px-0 !py-0"
        buttonsWrapperClassName="mt-9 md:mt-10"
        autoCardWrapper={false}
        header={
          <Text tag="h3" style="ph1" className="italic text-primary01-75">
            Articles
          </Text>
        }
        cardsPerViewGetter={() => cardsPerView}
        handleNext={handleNext}
        handlePrev={handlePrev}
      >
        {articleList.map((item, idx) => {
          const title = item.content.title;
          const publishAt = formatLocalTime(item.published_at, item.full_slug);
          const shareImage = item.content.shareImage;
          const full_slug = item.full_slug;

          return (
            <ArticlesCard
              key={item.uuid}
              title={title}
              publishAt={publishAt}
              shareImage={shareImage}
              full_slug={full_slug}
              className={cn(
                "shrink-0",
                "w-full",
                "md:w-[calc((100%-48px)/3)]",
                idx !== articleList.length - 1 ? "mr-6" : "",
              )}
            />
          );
        })}
      </SlideCarousel>
    </div>
  );
};

export default ArticlesCarousel;
