"use client";

import Link from "next/link";
import Image from "next/image";
import { httpsPath, cn } from "@/common/utils";
import Text from "@/components/ui/Text";

type Props = {
    className?: string;
    title: string;
    publishAt?: string;
    shareImage: string;
    full_slug: string;
    style?: React.CSSProperties;
};

const ArticlesCard = ({ title, publishAt, shareImage, full_slug, className, style }: Props) => {
    const href = `/${full_slug}`;

    return (
        <Link
            href={href}
            style={style}
            className={cn(
                "block rounded-md bg-white flex-none  transition-shadow duration-200  w-full",
                className
            )}
        >
            <div className="relative w-full aspect-[1/1]">
                <Image
                    src={httpsPath(typeof shareImage === "string" ? shareImage : undefined)}
                    alt={title}
                    fill
                    className="object-cover rounded"
                />
            </div>
            <div className="flex flex-col w-full px-2 pb-3">
                <Text tag="p" style="b4" className="text-grays-G4 mt-3">
                    {publishAt}
                </Text>
                <Text tag="h4" style="sh7" className="text-black mt-1 line-clamp-4">
                    {title}
                </Text>
            </div>
        </Link>
    );
};

export default ArticlesCard;