"use client";

import React, { useState } from "react";
import Text from "@/components/ui/Text";
import { useLabelTranslation } from "@/common/hooks/useTranslation";

type Props = {
    LargeBiography: string;
};

const Biography = ({ LargeBiography }: Props) => {
    const CHARACTER_LIMIT = 505;
    const isOverLimit = LargeBiography.length > CHARACTER_LIMIT;
    const { t } = useLabelTranslation();

    const [isExpanded, setIsExpanded] = useState(false);

    const displayedText = isExpanded
        ? LargeBiography
        : LargeBiography.slice(0, CHARACTER_LIMIT);

    return (
        <div className="mt-8">
            <Text tag="p" style="b1">
                {displayedText}
                {!isExpanded && isOverLimit && "..."}
            </Text>

            {isOverLimit && (
                <div onClick={() => setIsExpanded((prev) => !prev)} className="cursor-pointer w-fit mt-6">
                    <Text tag="p" style="b1"
                        className="mt-3 text-primary01-75 text-lg font-bold hover:text-primary01-50 transition-colors duration-200"
                    >
                        {isExpanded ? t("Show Less") : t("Show More")}
                    </Text>
                </div>
            )}
        </div>
    );
};

export default Biography;