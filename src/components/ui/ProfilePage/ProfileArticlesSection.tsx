import ArticlesCarousel from "./ArticlesCarousel";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";

interface Props {
  articleList: IBlogStory[];
  visibility?: "desktop" | "mobile";
}

const ProfileArticlesSection = ({
  articleList,
  visibility = "desktop",
}: Props) => {
  if (!articleList || articleList.length === 0) {
    return null;
  }
  const visibilityClass =
    visibility === "desktop" ? "hidden lg:block" : "block lg:hidden";

  return (
    <div className={`${visibilityClass} mt-10 md:mt-[4.6875rem]`}>
      <div className="flex items-center justify-between">
        {/* <Button colour="maroon" theme="secondary">
                    View All
                </Button> */}
      </div>
      <div className="py-8">
        <ArticlesCarousel articleList={articleList} />
      </div>
    </div>
  );
};

export default ProfileArticlesSection;
