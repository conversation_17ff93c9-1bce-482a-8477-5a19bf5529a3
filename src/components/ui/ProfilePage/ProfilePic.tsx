import Image from "next/image";
import ProfileBgc from "@/images/Profile-background.webp";
import { cn } from "@/common/utils";
import LinkedIn from "@/components/icons/LinkedIn";
import LinkedInOutline from "@/components/icons/LinkedInOutLine";

interface Props {
    src: string;
    Name: string;
    className?: string;
    LinkedinProfileURL: string | null;
}

const ProfilePic = ({ src, Name, LinkedinProfileURL, className }: Props) => {
    return (
        <div className={cn("relative w-full aspect-[332/368] shrink-0", className)}>
            <div className="absolute bottom-0 left-0 w-full h-[94%] z-[1]">
                <Image
                    src={ProfileBgc}
                    alt="Profile background"
                    fill
                    className="object-cover rounded object-top"
                />
            </div>

            <div className="absolute top-0 left-1/2 -translate-x-1/2 size-full z-[2]">
                <Image
                    src={src}
                    alt={Name}
                    fill
                    className="object-cover object-top rounded-t"
                />
            </div>

            {LinkedinProfileURL && (
                <a
                    href={LinkedinProfileURL}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="absolute bottom-[1.875rem] right-[1.875rem] z-[4] size-12 group"
                >
                    <div className="relative size-full">
                        <LinkedInOutline
                            className="absolute inset-0 size-full transition-opacity duration-300 opacity-100 group-hover:opacity-0"
                        />
                        <LinkedIn
                            className="absolute inset-0 size-full transition-opacity duration-300 opacity-0 group-hover:opacity-100 text-white"
                        />
                    </div>
                </a>
            )}

            <div
                className="absolute bottom-0 left-0 w-full h-[17%] z-[3] pointer-events-none rounded-b-sm"
                style={{
                    background: `linear-gradient(180deg, rgba(0, 0, 0, 0.00) 3.73%, rgba(0, 0, 0, 0.52) 41.7%, rgba(0, 0, 0, 0.90) 80.44%)`,
                }}
            />
        </div>
    );
};

export default ProfilePic;