import Text from "@/components/ui/Text";
import Biography from "./Biography";
import { cn } from "@/common/utils";

interface Props {
    name: string;
    role: string;
    biography: string;
    className?: string;
}

const ProfileHeaderSection = ({ name, role, biography, className }: Props) => {
    return (
        <div className={cn(
            "border-b-0 border-grays-G5 pb-[4.6875rem] lg:border-b",
            className
        )}>
            <Text tag="h1" style="sh2">{name}</Text>
            <Text tag="p" style="h4" className="text-primary01-50 mt-3">{role}</Text>
            <Biography LargeBiography={biography} />
        </div>
    );
};

export default ProfileHeaderSection;