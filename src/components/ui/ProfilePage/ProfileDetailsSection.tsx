import { AcademicCapIcon } from "@heroicons/react/24/outline";
import AwardsIcon from "@/components/icons/Awards";
import Text from "@/components/ui/Text";
import { cn } from "@/common/utils";

interface Props {
    universities?: string;
    awards?: string;
    specialties?: string;
    className?: string;
}

const ProfileDetailsSection = ({
    universities = "",
    awards = "",
    specialties = "",
    className,
}: Props) => {
    const universityList = universities.split("\n").filter(Boolean);
    const awardList = awards.split("\n").filter(Boolean);
    const specialtyList = specialties.split("\n").filter(Boolean);

    return (
        <div className={cn("relative", className)}>
            {universityList.length > 0 && (
                <div className="border-b border-grays-G5 pb-[1.375rem] mt-5">
                    <Text tag="p" style="h4" className="text-primary01-75">
                        Education
                    </Text>
                    <div className="flex flex-wrap gap-4 mt-2">
                        {universityList.map((university, index) => (
                            <div key={index} className="flex items-center">
                                <AcademicCapIcon className="size-5 text-primary01-75 mr-1" />
                                <Text tag="p" style="b1">
                                    {university}
                                </Text>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {awardList.length > 0 && (
                <div className=" pb-[1.375rem] mt-5">
                    <Text tag="p" style="h4" className="text-primary01-75">
                        Awards
                    </Text>
                    <div className="flex flex-wrap gap-4 mt-2">
                        {awardList.map((award, index) => (
                            <div key={index} className="flex items-center">
                                <AwardsIcon className="size-5 text-primary01-75 mr-1" />
                                <Text tag="p" style="b1">
                                    {award}
                                </Text>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {specialtyList.length > 0 && (
                <div className="pb-[1.375rem] pt-5 border-t border-grays-G5">
                    <Text tag="p" style="h4" className="text-primary01-75">
                        Specialties
                    </Text>
                    <div className="flex flex-wrap gap-4 mt-2">
                        {specialtyList.map((specialty, index) => (
                            <div key={index} className="flex items-center">
                                <span className="text-primary01-75 mr-1">-</span>
                                <Text tag="p" style="b1">
                                    {specialty}
                                </Text>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default ProfileDetailsSection;