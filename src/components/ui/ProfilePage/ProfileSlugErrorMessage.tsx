"use client";

import { getEffectivePath } from "@/common/profile/validateProfileSlug";
import React from "react";

const TYPE_TO_PATH_PREFIX: Record<string, string> = {
    Consultant: "about-us/consultants/",
    FAO: "about-us/consultants/",
    Team: "about-us/team/",
    Editor: "editors/",
};

const PREFIX_TO_TYPES_MAP = Object.entries(TYPE_TO_PATH_PREFIX).reduce(
    (acc, [type, path]) => {
        acc[path] ??= [];
        acc[path].push(type);
        return acc;
    },
    {} as Record<string, string[]>,
);

function normalizeType(type: string): string {
    return type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();
}


export function ProfileSlugErrorMessage({
    typeList,
    reason,
    slug,
}: {
    typeList: string[];
    reason: "not-in-supported-folder" | "type-folder-mismatch";
    slug: string;
}) {
    const suggestedPath = typeList
        .map(normalizeType)
        .map((type) => TYPE_TO_PATH_PREFIX[type])
        .find(Boolean);

    const effectivePath = getEffectivePath(slug);

    const currentFolder = Object.keys(PREFIX_TO_TYPES_MAP).find((folder) =>
        effectivePath.startsWith(folder),
    );

    const expectedTypes = currentFolder
        ? PREFIX_TO_TYPES_MAP[currentFolder]
        : [];

    return (
        <div className="mx-auto w-3/4 px-4 py-24 text-center text-red-700">
            {reason === "not-in-supported-folder" && (
                <>
                    <h3 className="text-2xl">
                        This profile page is not placed in a supported folder!
                    </h3>

                    <div className="mt-10">
                        <p className="font-medium text-left">
                            Valid folders and their supported types:
                        </p>
                        <ul className="text-left mt-2 list-disc list-inside text-base">
                            {Object.entries(PREFIX_TO_TYPES_MAP).map(([path, types]) => (
                                <li key={path}>
                                    <span className="font-mono text-red-800">/{path}</span>{" "}
                                    <span className="text-sm">
                                        (for Type{types.length > 1 ? "s" : ""}: {types.join(", ")})
                                    </span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    <p className=" text-left mt-10 text-base">
                        {suggestedPath ? (
                            <>
                                Based on the current Type, please move this
                                page to:{" "}
                                <span className="font-mono text-red-800">
                                    /{suggestedPath}
                                </span>
                            </>
                        ) : (
                            <>
                                Please ensure the Type is set correctly so we
                                can determine the appropriate folder.
                            </>
                        )}
                    </p>
                </>
            )}

            {reason === "type-folder-mismatch" && (
                <p className="text-2xl">
                    The profile page must be a type{" "}
                    <span className="font-mono text-red-800">
                        {expectedTypes?.map((t) => `"${t}"`).join(" / ") ?? ""}
                    </span>{" "}
                    to be published in this folder.
                    <br />
                    Please recreate it in the correct folder or update the Type.
                </p>
            )}
        </div>
    );
}