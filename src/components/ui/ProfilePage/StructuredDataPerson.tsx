interface Props {
    name: string;
    universities?: string;
    awards?: string;
    jobTitle?: string;
    description: string;
    image?: string;
    sameAs?: any;
    followers?: number;
    dateCreated?: string;
    dateModified?: string;
}

const StructuredDataPerson = ({
    name,
    universities,
    awards,
    jobTitle,
    description,
    image,
    sameAs,
    followers,
    dateCreated,
    dateModified,
}: Props) => {
    const data = {
        "@context": "https://schema.org",
        "@type": "Person",
        name,
        alumniOf: universities?.split("\n"),
        award: awards?.split("\n"),
        jobTitle: jobTitle ?? undefined,
        description,
        image,
        sameAs,
        interactionStatistic: followers
            ? {
                "@type": "InteractionCounter",
                interactionType: "https://schema.org/FollowAction",
                userInteractionCount: followers,
            }
            : undefined,
        dateCreated,
        dateModified,
    };

    return (
        <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
                __html: JSON.stringify(data),
            }}
        />
    );
};

export default StructuredDataPerson;