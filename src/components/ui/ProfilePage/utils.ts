const SOCIAL_DOMAINS = ["linkedin.com"];

export const isValidSocialDomain = (url: string): boolean => {
  try {
    const u = new URL(url.startsWith("http") ? url : `https://${url}`);
    return SOCIAL_DOMAINS.some((domain) => u.hostname.endsWith(domain));
  } catch {
    return false;
  }
};

function normalizeUrl(urlField?: string): string | null {
  if (!urlField) return null;

  let url = urlField.trim();

  if (url.startsWith("//")) {
    url = `https:${url}`;
  } else if (!url.startsWith("http")) {
    url = `https://${url}`;
  }

  return isValidSocialDomain(url) ? url : null;
}

type StoryblokLink = {
  url?: string;
  cached_url?: string;
  linktype?: string;
  fieldtype?: string;
};

export function getValidSameAsUrl(
  ...links: (StoryblokLink | undefined | null)[]
): string | null {
  for (const link of links) {
    if (!link) continue;

    if (link.url) {
      const normalized = normalizeUrl(link.url);
      if (normalized) return normalized;

      continue;
    }

    if (link.cached_url) {
      return link.cached_url;
    }
  }

  return null;
}
