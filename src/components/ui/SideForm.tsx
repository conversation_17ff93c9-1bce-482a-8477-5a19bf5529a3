"use client";
import { IForm } from "@/common/types";
import React, { useState } from "react";
import Form from "./Forms/Form";
import { cn } from "@/common/utils";
import FormHeaders from "./Forms/FormHeaders";

interface Props {
  formComponent: IForm[];
  formId: string;
  heading?: string;
  subheading?: string;
  formDataTestId?: string;
}

const SideForm = (props: Props) => {
  const { formId, formComponent, heading = "", subheading = "" } = props;

  const theme = "light";
  const defaultTheme = "light";
  const anchorId = formComponent[0]?.anchorId;
  formComponent[0]!.theme = defaultTheme;
  const [showHeaders, setShowHeaders] = useState(true);

  return (
    <div
      className={cn(
        "mx-auto grid max-w-[39.6875rem] grid-cols-1 gap-3xl rounded-xl p-6 shadow-[0px_2px_20px_0px_rgba(86,65,46,0.10)] md:p-10",
        theme === "light"
          ? "bg-white text-neutral01-100"
          : "bg-primary01-75 text-white",
      )}
      id={anchorId ?? formId}
    >
      {showHeaders && (
        <div>
          <FormHeaders heading={heading} subheading={subheading} />
        </div>
      )}

      <Form
        form={formComponent}
        formId={formId}
        reason="Filled side form"
        setShowHeaders={setShowHeaders}
      />
    </div>
  );
};

export default SideForm;
