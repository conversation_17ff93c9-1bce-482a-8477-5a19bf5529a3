import { fetchStoryblokStories } from "@/common/storyblok";

/**
 * @deprecated - use fetchStoryblokData instead or something from storyblok.ts
 */
export async function fetchSettingsData(locale: string) {
  return fetchStoryblokStories({
    draftMode: false,
    starts_with: `${locale}/`, // Filters for stories in the specified folder
    filter_query: {
      component: {
        in: "settings", // Filters for stories with the component "settings"
      },
    },
  });
}
