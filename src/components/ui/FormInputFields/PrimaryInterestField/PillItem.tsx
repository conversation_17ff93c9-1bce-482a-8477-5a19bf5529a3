import { Theme } from "@/common/types";
import { cn } from "@/common/utils";
import { GreenCheckIcon } from "@/components/icons/GreenCheckIcon";
import { PlusIcon } from "@heroicons/react/24/solid";
import { cva } from "class-variance-authority";

interface IPillItem {
  handleOnClick: () => void;
  value: string;
  isSelected: boolean;
  theme: Theme;
}

const pillItemVariants = cva(
  "px-5 py-[0.81rem] rounded-full cursor-pointer w-fit border border-solid flex items-center justify-center gap-2 text-b1 font-body-p",
  {
    variants: {
      isSelected: {
        true: "text-green-500",
        false: "text-grey-500",
      },
      theme: {
        light: "border-grey-300 text-grey-600 bg-white hover:bg-grey-50",
        dark: "bg-blue-700 text-white border-grey-100 hover:bg-grey-600",
      },
    },
    compoundVariants: [
      {
        isSelected: true,
        theme: "light",
        class: "bg-blue-700 text-white hover:bg-blue-700",
      },
      {
        isSelected: true,
        theme: "dark",
        class: "bg-white text-blue-700 hover:bg-white",
      },
    ],
  },
);

const PillItem = ({ handleOnClick, value, isSelected, theme }: IPillItem) => {
  return (
    <div
      onClick={handleOnClick}
      className={cn(
        pillItemVariants({ isSelected, theme }),
        "group font-medium",
      )}
    >
      <div
        className={cn(
          "flex size-4 items-center justify-center group-hover:flex",
          !isSelected && "hidden",
        )}
      >
        <GreenCheckIcon
          className={cn(
            "size-3 text-green-500 group-hover:block",
            !isSelected && "hidden",
          )}
        />
      </div>
      <PlusIcon
        className={cn(
          "size-4 group-hover:hidden",
          isSelected && "hidden",
          theme === "light" ? "text-black" : "text-white",
        )}
      />
      {value}
    </div>
  );
};

export default PillItem;
