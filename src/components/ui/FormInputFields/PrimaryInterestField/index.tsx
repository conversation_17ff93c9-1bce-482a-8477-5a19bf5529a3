import { IDropdownOption, Theme } from "@/common/types";
import DropdownField from "../DropdownField";
import PillShaped from "./PillShaped";

interface Props {
  label: string;
  nameOfField: string;
  error: string;
  touched: boolean;
  theme: Theme;
  formId: string;
  stylePreset?: "pills" | "dropdown";
  pillOptions?: string[];

  // dynamic options
  [key: `option_${number}`]: IDropdownOption;
}
const PrimaryInterestField = (props: Props) => {
  const { stylePreset, pillOptions } = props;

  if (stylePreset === "pills" && pillOptions) {
    return <PillShaped {...props} />;
  }

  return <DropdownField {...props} />;
};

export default PrimaryInterestField;
