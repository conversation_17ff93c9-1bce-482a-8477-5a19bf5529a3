import { useEffect, useMemo } from "react";
import { useFormikContext } from "formik";
import { IFormValues } from "../../Forms/types";
import { normalisePrimaryInterestPillsValues } from "./utils";
import InputFieldset from "../../InputFieldset";
import { Theme } from "@/common/types";
import Typography from "../../Typography";
import PillItem from "./PillItem";
import Skeleton from "../../Skeleton";
import { usePageContext } from "@/components/context/PageContext";

interface IPillDataSource {
  name: string;
  value: string;
}

interface Props {
  stylePreset?: "pills" | "dropdown";
  label: string;
  theme: Theme;
  nameOfField: string;
  pillOptions?: string[];
  pillsPlaceholder?: string;
  error?: string;
  touched?: boolean;
  pillSelectionType?: string;
  preselectedPillOptions?: string[];
}

// TODO: Investigate functionality since form redesign and update

const PillShaped = (props: Props) => {
  const {
    theme,
    nameOfField,
    pillOptions,
    pillsPlaceholder,
    error,
    touched,
    pillSelectionType = "multiple",
    preselectedPillOptions,
    label,
  } = props;

  const { coursesInterestedIn } = usePageContext();

  const selectedDataSources = useMemo(() => {
    return coursesInterestedIn?.filter((source: IPillDataSource) =>
      pillOptions?.includes(source.value),
    );
  }, [coursesInterestedIn, pillOptions]);

  const { values, setFieldValue } = useFormikContext<IFormValues>();

  const handleMultiSelect = (value: string) => {
    const currentInterest = values[nameOfField] as string;

    const splitInterestList = currentInterest?.split(";");

    if (splitInterestList.includes(value)) {
      // remove
      const indexToRemove = splitInterestList.indexOf(value);
      if (indexToRemove > -1) {
        splitInterestList.splice(indexToRemove, 1);

        setFieldValue(
          nameOfField,
          normalisePrimaryInterestPillsValues(splitInterestList),
        ).catch(console.error);
      }
    } else {
      const newInterest = [...splitInterestList, value];
      setFieldValue(
        nameOfField,
        normalisePrimaryInterestPillsValues(newInterest),
      ).catch(console.error);
    }
  };

  const handleSingleSelect = (singleSelectValue: string) => {
    setFieldValue(nameOfField, singleSelectValue).catch(console.error);
  };

  const hasValidPillOptions =
    selectedDataSources &&
    selectedDataSources.length <= 5 &&
    selectedDataSources.length > 0;

  useEffect(() => {
    const updateFieldValue = async (optionsResult: string[]) =>
      setFieldValue(nameOfField, optionsResult.join(";"));

    if (hasValidPillOptions && preselectedPillOptions) {
      const optionsResult: string[] = [];
      selectedDataSources.forEach((source: IPillDataSource) => {
        if (preselectedPillOptions.includes(source.value)) {
          optionsResult.push(source.name);
        }
      });

      if (optionsResult.length > 0 && !values[nameOfField]) {
        updateFieldValue(optionsResult).catch(console.error);
      }
    }
  }, [nameOfField, preselectedPillOptions, hasValidPillOptions]); // eslint-disable-line react-hooks/exhaustive-deps

  const hasErrors = Boolean(touched && error);

  return (
    <InputFieldset
      hideLabel={true}
      theme={theme}
      hasValidationError={hasErrors}
      errorMessage={error}
      errorMessageIsIndependent={true}
      nameOfField={nameOfField}
    >
      <Typography
        tag="label"
        style="body2"
        className="cursor-pointer font-semibold"
        htmlFor={nameOfField}
      >
        {label}
      </Typography>
      {pillsPlaceholder && (
        <Typography tag="span" style="body2">
          {pillsPlaceholder}
        </Typography>
      )}
      <div className="flex flex-wrap gap-x-[10px] gap-y-4 pt-4">
        {!selectedDataSources?.length && (
          <>
            <Skeleton className="h-12 w-72 rounded-full" theme={theme} />
            <Skeleton className="h-12 w-48 rounded-full" theme={theme} />
            <Skeleton className="h-12 w-48 rounded-full" theme={theme} />
          </>
        )}

        {selectedDataSources?.map((option: IPillDataSource) => {
          const fieldValues = values[nameOfField] as string;
          const isSelected = fieldValues?.includes(option.name);
          return (
            <PillItem
              key={option.name}
              value={option.value}
              isSelected={isSelected}
              theme={theme}
              handleOnClick={() => {
                if (pillSelectionType === "single") {
                  handleSingleSelect(option.name);
                } else {
                  handleMultiSelect(option.name);
                }
              }}
            />
          );
        })}
      </div>
    </InputFieldset>
  );
};

export default PillShaped;
