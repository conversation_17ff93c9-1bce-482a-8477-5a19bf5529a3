import { showCommsOptIn } from "@/common/utils";
import CheckboxField from "./CheckboxField";
import { IFormValues } from "../Forms/types";
import { FormikErrors, FormikTouched } from "formik";
import { usePageContext } from "@/components/context/PageContext";
import { Theme } from "@/common/types";

interface Props {
  values: IFormValues;
  touched: FormikTouched<IFormValues>;
  errors: FormikErrors<IFormValues>;
  label: string;
  formId: string;
  theme: Theme;
}

const CommsOptInField = (props: Props) => {
  const { values, touched, errors, label, formId, theme } = props;

  const { locale } = usePageContext();
  const nameOfField = "commsOptIn";

  if (showCommsOptIn(locale, values?.country ?? "")) {
    return (
      <CheckboxField
        label={label}
        nameOfField={nameOfField}
        theme={theme}
        touched={!!touched[nameOfField]}
        value={!!values[nameOfField]}
        error={errors[nameOfField]}
        formId={formId}
        style="maroon"
      />
    );
  } else {
    return null;
  }
};

export default CommsOptInField;
