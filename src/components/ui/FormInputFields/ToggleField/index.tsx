import { IDropdownOption, Theme } from "@/common/types";
import InputFieldset from "../../InputFieldset";
import Alert from "@/components/ui/Alert";
import { FormikErrors, FormikTouched } from "formik";
import { IFormValues } from "../../Forms/types";
import ToggleButton from "./ToggleButton";
import RedTriangleWarning from "@/components/icons/RedTriangleWarning";

interface Props {
  label: string;
  nameOfField: string;
  value: string;
  error: string;
  touched: boolean;
  theme: Theme;
  formId: string;
  setFieldTouched?: (
    field: string,
    isTouched?: boolean,
    shouldValidate?: boolean,
  ) => Promise<void | FormikTouched<IFormValues>>;
  setFieldValue?: (
    field: string,
    value: any,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;

  [key: `option_${number}`]: IDropdownOption;
}

const ToggleField = (props: Props) => {
  const { label, nameOfField, error, touched, theme, formId, value } = props;

  const options = Object.keys(props)
    .filter((key) => key.startsWith("option_"))
    .map((key) => {
      const option = props[key as keyof typeof props] as IDropdownOption;
      if (!option.marketoField) {
        return null;
      }

      return {
        label: option.label,
        value: option.marketoField,
        _uid: option._uid,
      };
    })
    .filter((option) => option !== null);

  if (options.length < 1) {
    return (
      <Alert theme="error">
        No toggle options found for this field: {nameOfField}
      </Alert>
    );
  }

  const hasErrors = Boolean(touched && error);

  return (
    <InputFieldset
      nameOfField={nameOfField}
      label={label}
      theme={theme}
      hasValidationError={hasErrors}
      errorMessage={error}
      errorMessageIsIndependent={true}
    >
      <div className="flex items-center gap-sm">
        {options.map((option, index) => {
          return (
            <ToggleButton
              key={option.value + index}
              value={option.value}
              label={option.label}
              nameOfField={nameOfField}
              formId={formId}
              index={index}
              theme={theme}
              isSelected={option.value === value}
            />
          );
        })}
        {hasErrors && <RedTriangleWarning className="ml-4" />}
      </div>
    </InputFieldset>
  );
};

export default ToggleField;
