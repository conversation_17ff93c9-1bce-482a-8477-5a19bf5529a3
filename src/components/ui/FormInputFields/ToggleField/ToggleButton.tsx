import { Theme } from "@/common/types";
import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";
import { Field } from "formik";
import Text from "@/components/ui/Text";
import { CheckIcon } from "@heroicons/react/16/solid";

interface Props {
  value: string;
  label: string;
  nameOfField: string;
  formId: string;
  index: number;
  key: string;
  theme: Theme;
  className?: string;
  isSelected?: boolean;
}

const toggleButtonVariants = cva(
  "cursor-pointer font-bold px-lg py-sm transition-all border border-solid border-transparent rounded inline-flex justify-center items-center gap-[0.62rem] focus:outline-none focus:ring-0 text-b1 font-body-p relative",
  {
    variants: {
      theme: {
        dark: "text-white border-white hover:bg-white hover:text-primary01-75",
        light:
          "text-primary01-75 border-primary01-25 hover:bg-primary01-25 hover:text-primary01-75 bg-white",
      },
      isSelected: {
        true: "",
        false: "",
      },
    },
    compoundVariants: [
      {
        theme: "dark",
        isSelected: true,
        className: "bg-white text-blue-700",
      },
      {
        theme: "light",
        isSelected: true,
        className: "bg-primary01-25 text-primary01-75",
      },
    ],
  },
);

const ToggleButton = (props: Props) => {
  const {
    value,
    label,
    nameOfField,
    formId,
    index,
    theme,
    className,
    isSelected,
  } = props;

  return (
    <label
      htmlFor={`${nameOfField}-${formId}-${index}`}
      className={cn(toggleButtonVariants({ theme, isSelected }), className)}
    >
      <Field
        type="radio"
        name={nameOfField}
        id={`${nameOfField}-${formId}-${index}`}
        value={value}
        className="pointer-events-none absolute top-0 appearance-none [clip:rect(0,0,0,0)]"
      />
      <Text tag="span" style="t2" className="cursor-pointer">
        {label}
      </Text>
      {isSelected && (
        <div className="size-4">
          <CheckIcon className="size-full text-primary01-75" />
        </div>
      )}
    </label>
  );
};

export default ToggleButton;
