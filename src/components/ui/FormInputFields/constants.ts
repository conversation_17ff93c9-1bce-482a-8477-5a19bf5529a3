const SCHOOL_PRIORITISATION_MIN_LENGTH = 3;
const SCHOOL_PRIORITISATION_MAX_MATCHED = 25;
const AGE_FIELD_OPTIONS = Array.from({ length: 19 - 8 }, (_, i) => i + 8);

const LANGUAGE_FIELD_OPTIONS = [
  {
    label: "Аҧсуа",
    value: "Abkhazian",
  },
  {
    label: "Afaraf",
    value: "Afar",
  },
  {
    label: "Afrikaans",
    value: "Afrikaans",
  },
  {
    label: "Akan",
    value: "Akan",
  },
  {
    label: "Shqip",
    value: "Albanian",
  },
  {
    label: "አማርኛ",
    value: "Amharic",
  },
  {
    label: "العربية",
    value: "Arabic",
  },
  {
    label: "Aragonés",
    value: "Aragonese",
  },
  {
    label: "Հայերեն",
    value: "Armenian",
  },
  {
    label: "অসমীয়া",
    value: "Assamese",
  },
  {
    label: "Авар",
    value: "Avar",
  },
  {
    label: "<PERSON><PERSON><PERSON> aru",
    value: "<PERSON><PERSON><PERSON>",
  },
  {
    label: "Azərbaycan<PERSON>",
    value: "Azerbaijani",
  },
  {
    label: "Bamanankan",
    value: "Bambara",
  },
  {
    label: "Башҡортса",
    value: "Bashkir",
  },
  {
    label: "Euskara",
    value: "Basque",
  },
  {
    label: "Беларуская",
    value: "Belarusian",
  },
  {
    label: "বাংলা",
    value: "Bengali",
  },
  {
    label: "भोजपुरी",
    value: "Bihari",
  },
  {
    label: "Bislama",
    value: "Bislama",
  },
  {
    label: "Bosanski",
    value: "Bosnian",
  },
  {
    label: "Brezhoneg",
    value: "Breton",
  },
  {
    label: "Български",
    value: "Bulgarian",
  },
  {
    label: "မြန်မာစာ",
    value: "Burmese",
  },
  {
    label: "ភាសាខ្មែរ",
    value: "Cambodian",
  },
  {
    label: "粵語",
    value: "Cantonese",
  },
  {
    label: "Català",
    value: "Catalan",
  },
  {
    label: "Chamoru",
    value: "Chamorro",
  },
  {
    label: "Нохчийн",
    value: "Chechen",
  },
  {
    label: "Chichewa",
    value: "Chichewa",
  },
  {
    label: "Чӑвашла",
    value: "Chuvash",
  },
  {
    label: "Kernewek",
    value: "Cornish",
  },
  {
    label: "Corsu",
    value: "Corsican",
  },
  {
    label: "ᓀᐦᐃᔭᐍᐏᐣ",
    value: "Cree",
  },
  {
    label: "Hrvatski",
    value: "Croatian",
  },
  {
    label: "Čeština",
    value: "Czech",
  },
  {
    label: "Dansk",
    value: "Danish",
  },
  {
    label: "ދިވެހި",
    value: "Divehi",
  },
  {
    label: "Nederlands",
    value: "Dutch",
  },
  {
    label: "རྫོང་ཁ",
    value: "Dzongkha",
  },
  {
    label: "English",
    value: "English",
  },
  {
    label: "Esperanto",
    value: "Esperanto",
  },
  {
    label: "Eesti",
    value: "Estonian",
  },
  {
    label: "Eʋegbe",
    value: "Ewe",
  },
  {
    label: "Føroyskt",
    value: "Faroese",
  },
  {
    label: "Vosa Vakaviti",
    value: "Fijian",
  },
  {
    label: "Suomi",
    value: "Finnish",
  },
  {
    label: "Français",
    value: "French",
  },
  {
    label: "Galego",
    value: "Galician",
  },
  {
    label: "Luganda",
    value: "Ganda",
  },
  {
    label: "ქართული",
    value: "Georgian",
  },
  {
    label: "Deutsch",
    value: "German",
  },
  {
    label: "Ελληνικά",
    value: "Greek",
  },
  {
    label: "Kalaallisut",
    value: "Greenlandic",
  },
  {
    label: "Avañe'ẽ",
    value: "Guarani",
  },
  {
    label: "ગુજરાતી",
    value: "Gujarati",
  },
  {
    label: "Kreyòl ayisyen",
    value: "Haitian",
  },
  {
    label: "Hausa",
    value: "Hausa",
  },
  {
    label: "עברית",
    value: "Hebrew",
  },
  {
    label: "Otjiherero",
    value: "Herero",
  },
  {
    label: "हिन्दी",
    value: "Hindi",
  },
  {
    label: "Hiri Motu",
    value: "Hiri Motu",
  },
  {
    label: "Magyar",
    value: "Hungarian",
  },
  {
    label: "Íslenska",
    value: "Icelandic",
  },
  {
    label: "Ido",
    value: "Ido",
  },
  {
    label: "Igbo",
    value: "Igbo",
  },
  {
    label: "Bahasa Indonesia",
    value: "Indonesian",
  },
  {
    label: "Interlingua",
    value: "Interlingua",
  },
  {
    label: "Interlingue",
    value: "Interlingue",
  },
  {
    label: "ᐃᓄᒃᑎᑐᑦ",
    value: "Inuktitut",
  },
  {
    label: "Inupiak",
    value: "Inupiak",
  },
  {
    label: "Gaeilge",
    value: "Irish",
  },
  {
    label: "Italiano",
    value: "Italian",
  },
  {
    label: "日本語",
    value: "Japanese",
  },
  {
    label: "Basa Jawa",
    value: "Javanese",
  },
  {
    label: "ಕನ್ನಡ",
    value: "Kannada",
  },
  {
    label: "Kanuri",
    value: "Kanuri",
  },
  {
    label: "Kashmiri",
    value: "Kashmiri",
  },
  {
    label: "Қазақ тілі",
    value: "Kazakh",
  },
  {
    label: "Gĩkũyũ",
    value: "Kikuyu",
  },
  {
    label: "Kirghiz",
    value: "Kirghiz",
  },
  {
    label: "Ikirundi",
    value: "Kirundi",
  },
  {
    label: "Komi",
    value: "Komi",
  },
  {
    label: "Kongo",
    value: "Kongo",
  },
  {
    label: "한국어",
    value: "Korean",
  },
  {
    label: "Oshiwambo",
    value: "Kuanyama",
  },
  {
    label: "Kurdî",
    value: "Kurdish",
  },
  {
    label: "ພາສາລາວ",
    value: "Laotian",
  },
  {
    label: "Latin",
    value: "Latin",
  },
  {
    label: "Latviešu valoda",
    value: "Latvian",
  },
  {
    label: "Limburgian",
    value: "Limburgian",
  },
  {
    label: "Lingála",
    value: "Lingala",
  },
  {
    label: "Lietuvių kalba",
    value: "Lithuanian",
  },
  {
    label: "Lëtzebuergesch",
    value: "Luxembourgish",
  },
  {
    label: "Macedonian",
    value: "Macedonian",
  },
  {
    label: "Malagasy",
    value: "Malagasy",
  },
  {
    label: "Bahasa Melayu",
    value: "Malay",
  },
  {
    label: "മലയാളം",
    value: "Malayalam",
  },
  {
    label: "Malti",
    value: "Maltese",
  },
  {
    label: "简体中文",
    value: "Mandarin",
  },
  {
    label: "Manx",
    value: "Manx",
  },
  {
    label: "Te Reo Māori",
    value: "Maori",
  },
  {
    label: "मराठी",
    value: "Marathi",
  },
  {
    label: "Marshallese",
    value: "Marshallese",
  },
  {
    label: "Moldovan",
    value: "Moldovan",
  },
  {
    label: "Монгол хэл",
    value: "Mongolian",
  },
  {
    label: "Nauruan",
    value: "Nauruan",
  },
  {
    label: "Navajo",
    value: "Navajo",
  },
  {
    label: "Ndonga",
    value: "Ndonga",
  },
  {
    label: "Nepali",
    value: "Nepali",
  },
  {
    label: "Northern Sami",
    value: "Northern Sami",
  },
  {
    label: "North Ndebele",
    value: "North Ndebele",
  },
  {
    label: "Norsk",
    value: "Norwegian",
  },
  {
    label: "Nynorsk",
    value: "Norwegian Nynorsk",
  },
  {
    label: "Occitan",
    value: "Occitan",
  },
  {
    label: "Ojibwa",
    value: "Ojibwa",
  },
  {
    label: "Старославянский / Староболгарский",
    value: "Old Church Slavonic / Old Bulgarian",
  },
  {
    label: "ଓଡ଼ିଆ",
    value: "Oriya",
  },
  {
    label: "Afaan Oromoo",
    value: "Oromo",
  },
  {
    label: "Ирон / Дигорæ",
    value: "Ossetian / Ossetic",
  },
  {
    label: "पाऴि",
    value: "Pali",
  },
  {
    label: "ਪੰਜਾਬੀ / پنجابی",
    value: "Panjabi / Punjabi",
  },
  {
    label: "پښتو",
    value: "Pashto",
  },
  {
    label: "فارسی",
    value: "Persian",
  },
  {
    label: "Fulfulde",
    value: "Peul",
  },
  {
    label: "Polski",
    value: "Polish",
  },
  {
    label: "Português",
    value: "Portuguese",
  },
  {
    label: "Runasimi",
    value: "Quechua",
  },
  {
    label: "Rumantsch",
    value: "Raeto Romance",
  },
  {
    label: "Română",
    value: "Romanian",
  },
  {
    label: "Русский",
    value: "Russian",
  },
  {
    label: "Ikinyarwanda",
    value: "Rwandi",
  },
  {
    label: "Gagana Samoa",
    value: "Samoan",
  },
  {
    label: "Sängö",
    value: "Sango",
  },
  {
    label: "संस्कृतम्",
    value: "Sanskrit",
  },
  {
    label: "Sardu",
    value: "Sardinian",
  },
  {
    label: "Gàidhlig",
    value: "Scottish Gaelic",
  },
  {
    label: "Српски / Srpski",
    value: "Serbian",
  },
  {
    label: "Српскохрватски",
    value: "Serbo-Croatian",
  },
  {
    label: "ChiShona",
    value: "Shona",
  },
  {
    label: "ꆇꉙ / 四川彝语",
    value: "Sichuan Yi",
  },
  {
    label: "سنڌي",
    value: "Sindhi",
  },
  {
    label: "සිංහල",
    value: "Sinhalese",
  },
  {
    label: "Slovenčina",
    value: "Slovak",
  },
  {
    label: "Slovenščina",
    value: "Slovenian",
  },
  {
    label: "Soomaali",
    value: "Somalia",
  },
  {
    label: "Sesotho",
    value: "Southern Sotho",
  },
  {
    label: "isiNdebele",
    value: "South Ndebele",
  },
  {
    label: "Español",
    value: "Spanish",
  },
  {
    label: "Basa Sunda",
    value: "Sundanese",
  },
  {
    label: "Kiswahili",
    value: "Swahili",
  },
  {
    label: "SiSwati",
    value: "Swati",
  },
  {
    label: "Svenska",
    value: "Swedish",
  },
  {
    label: "Tagalog / Filipino",
    value: "Tagalog / Filipino",
  },
  {
    label: "Reo Tahiti",
    value: "Tahitian",
  },
  {
    label: "тоҷикӣ",
    value: "Tajik",
  },
  {
    label: "தமிழ்",
    value: "Tamil",
  },
  {
    label: "Татарча / Tatarça",
    value: "Tatar",
  },
  {
    label: "తెలుగు",
    value: "Telugu",
  },
  {
    label: "ไทย",
    value: "Thai",
  },
  {
    label: "བོད་སྐད།",
    value: "Tibetan",
  },
  {
    label: "ትግርኛ",
    value: "Tigrinya",
  },
  {
    label: "Faka Tonga",
    value: "Tonga",
  },
  {
    label: "Xitsonga",
    value: "Tsonga",
  },
  {
    label: "Setswana",
    value: "Tswana",
  },
  {
    label: "Türkçe",
    value: "Turkish",
  },
  {
    label: "Түркмен / تۆرکمن",
    value: "Turkmen",
  },
  {
    label: "Twi",
    value: "Twi",
  },
  {
    label: "Українська",
    value: "Ukrainian",
  },
  {
    label: "اردو",
    value: "Urdu",
  },
  {
    label: "ئۇيغۇرچە / Uyghurche",
    value: "Uyghur",
  },
  {
    label: "O‘zbek / Ўзбек",
    value: "Uzbek",
  },
  {
    label: "Tshivenda",
    value: "Venda",
  },
  {
    label: "Tiếng Việt",
    value: "Vietnamese",
  },
  {
    label: "Volapük",
    value: "Volapük",
  },
  {
    label: "Walon",
    value: "Walloon",
  },
  {
    label: "Cymraeg",
    value: "Welsh",
  },
  {
    label: "Frysk",
    value: "West Frisian",
  },
  {
    label: "Wolof",
    value: "Wolof",
  },
  {
    label: "isiXhosa",
    value: "Xhosa",
  },
  {
    label: "ייִדיש",
    value: "Yiddish",
  },
  {
    label: "Yorùbá",
    value: "Yoruba",
  },
  {
    label: "Saɯ cueŋƅ / Saw cuengh",
    value: "Zhuang",
  },
  {
    label: "isiZulu",
    value: "Zulu",
  },
];

const COUNTRY_FIELD_OPTIONS = [
  {
    label: "Afghanistan",
    value: "Afghanistan",
  },
  {
    label: "Albania",
    value: "Albania",
  },
  {
    label: "Algeria",
    value: "Algeria",
  },
  {
    label: "American Samoa",
    value: "American Samoa",
  },
  {
    label: "Andorra",
    value: "Andorra",
  },
  {
    label: "Angola",
    value: "Angola",
  },
  {
    label: "Anguilla",
    value: "Anguilla",
  },
  {
    label: "Antigua and Barbuda",
    value: "Antigua & Barbuda",
  },
  {
    label: "Argentina",
    value: "Argentina",
  },
  {
    label: "Armenia",
    value: "Armenia",
  },
  {
    label: "Aruba",
    value: "Aruba",
  },
  {
    label: "Australia",
    value: "Australia",
  },
  {
    label: "Austria",
    value: "Austria",
  },
  {
    label: "Azerbaijan",
    value: "Azerbaijan",
  },
  {
    label: "Bahrain",
    value: "Bahrain",
  },
  {
    label: "Bangladesh",
    value: "Bangladesh",
  },
  {
    label: "Barbados",
    value: "Barbados",
  },
  {
    label: "Belarus",
    value: "Belarus",
  },
  {
    label: "Belgium",
    value: "Belgium",
  },
  {
    label: "Belize",
    value: "Belize",
  },
  {
    label: "Benin",
    value: "Benin",
  },
  {
    label: "Bermuda",
    value: "Bermuda",
  },
  {
    label: "Bhutan",
    value: "Bhutan",
  },
  {
    label: "Bolivia",
    value: "Bolivia",
  },
  {
    label: "Bosnia and Herzegovina",
    value: "Bosnia & Herzegovina",
  },
  {
    label: "Botswana",
    value: "Botswana",
  },
  {
    label: "Brazil",
    value: "Brazil",
  },
  {
    label: "Brunei",
    value: "Brunei",
  },
  {
    label: "Bulgaria",
    value: "Bulgaria",
  },
  {
    label: "Burkina Faso",
    value: "Burkina Faso",
  },
  {
    label: "Burundi",
    value: "Burundi",
  },
  {
    label: "Cambodia",
    value: "Cambodia",
  },
  {
    label: "Cameroon",
    value: "Cameroon",
  },
  {
    label: "Canada",
    value: "Canada",
  },
  {
    label: "Cape Verde",
    value: "Cape Verde",
  },
  {
    label: "Cayman Islands",
    value: "Cayman Islands",
  },
  {
    label: "Central African Republic",
    value: "Central African Rep.",
  },
  {
    label: "Chad",
    value: "Chad",
  },
  {
    label: "Chile",
    value: "Chile",
  },
  {
    label: "China",
    value: "China",
  },
  {
    label: "Colombia",
    value: "Colombia",
  },
  {
    label: "Comoros",
    value: "Comoros",
  },
  {
    label: "Cook Islands",
    value: "Cook Islands",
  },
  {
    label: "Costa Rica",
    value: "Costa Rica",
  },
  {
    label: "Croatia",
    value: "Croatia",
  },
  {
    label: "Curacao",
    value: "Curacao",
  },
  {
    label: "Cyprus",
    value: "Cyprus",
  },
  {
    label: "Czech Republic",
    value: "Czech Republic",
  },
  {
    label: "Dem. Rep. of Congo",
    value: "Dem. Rep. of Congo",
  },
  {
    label: "Denmark",
    value: "Denmark",
  },
  {
    label: "Djibouti",
    value: "Djibouti",
  },
  {
    label: "Dominica",
    value: "Dominica",
  },
  {
    label: "Dominican Republic",
    value: "Dominican Republic",
  },
  {
    label: "East Timor",
    value: "East Timor",
  },
  {
    label: "Ecuador",
    value: "Ecuador",
  },
  {
    label: "Egypt",
    value: "Egypt",
  },
  {
    label: "El Salvador",
    value: "El Salvador",
  },
  {
    label: "Equatorial Guinea",
    value: "Equatorial Guinea",
  },
  {
    label: "Eritrea",
    value: "Eritrea",
  },
  {
    label: "Estonia",
    value: "Estonia",
  },
  {
    label: "Ethiopia",
    value: "Ethiopia",
  },
  {
    label: "Faroe Islands",
    value: "Faroe Islands",
  },
  {
    label: "Fiji",
    value: "Fiji",
  },
  {
    label: "Finland",
    value: "Finland",
  },
  {
    label: "France",
    value: "France",
  },
  {
    label: "French Guiana",
    value: "French Guiana",
  },
  {
    label: "French Polynesia",
    value: "French Polynesia",
  },
  {
    label: "Gabon",
    value: "Gabon",
  },
  {
    label: "Gambia",
    value: "Gambia",
  },
  {
    label: "Georgia",
    value: "Georgia",
  },
  {
    label: "Germany",
    value: "Germany",
  },
  {
    label: "Ghana",
    value: "Ghana",
  },
  {
    label: "Gibraltar",
    value: "Gibraltar",
  },
  {
    label: "Greece",
    value: "Greece",
  },
  {
    label: "Greenland",
    value: "Greenland",
  },
  {
    label: "Grenada",
    value: "Grenada",
  },
  {
    label: "Guadeloupe",
    value: "Guadeloupe",
  },
  {
    label: "Guam",
    value: "Guam",
  },
  {
    label: "Guatemala",
    value: "Guatemala",
  },
  {
    label: "Guinea",
    value: "Guinea",
  },
  {
    label: "Guinea-Bissau",
    value: "Guinea-Bissau",
  },
  {
    label: "Guyana",
    value: "Guyana",
  },
  {
    label: "Haiti",
    value: "Haiti",
  },
  {
    label: "Honduras",
    value: "Honduras",
  },
  {
    label: "Hong Kong",
    value: "Hong Kong - SAR",
  },
  {
    label: "Hungary",
    value: "Hungary",
  },
  {
    label: "Iceland",
    value: "Iceland",
  },
  {
    label: "India",
    value: "India",
  },
  {
    label: "Indonesia",
    value: "Indonesia",
  },
  {
    label: "Iraq",
    value: "Iraq",
  },
  {
    label: "Ireland",
    value: "Ireland",
  },
  {
    label: "Israel",
    value: "Israel",
  },
  {
    label: "Italy",
    value: "Italy",
  },
  {
    label: "Jamaica",
    value: "Jamaica",
  },
  {
    label: "Japan",
    value: "Japan",
  },
  {
    label: "Jordan",
    value: "Jordan",
  },
  {
    label: "Kazakhstan",
    value: "Kazakhstan",
  },
  {
    label: "Kenya",
    value: "Kenya",
  },
  {
    label: "Kiribati",
    value: "Kiribati",
  },
  {
    label: "Kuwait",
    value: "Kuwait",
  },
  {
    label: "Kyrgyzstan",
    value: "Kyrgyzstan",
  },
  {
    label: "Latvia",
    value: "Latvia",
  },
  {
    label: "Lebanon",
    value: "Lebanon",
  },
  {
    label: "Lesotho",
    value: "Lesotho",
  },
  {
    label: "Liberia",
    value: "Liberia",
  },
  {
    label: "Libya",
    value: "Libya",
  },
  {
    label: "Liechtenstein",
    value: "Liechtenstein",
  },
  {
    label: "Lithuania",
    value: "Lithuania",
  },
  {
    label: "Luxembourg",
    value: "Luxembourg",
  },
  {
    label: "Macau",
    value: "Macau",
  },
  {
    label: "Madagascar",
    value: "Madagascar",
  },
  {
    label: "Malawi",
    value: "Malawi",
  },
  {
    label: "Malaysia",
    value: "Malaysia",
  },
  {
    label: "Maldives",
    value: "Maldives",
  },
  {
    label: "Mali",
    value: "Mali",
  },
  {
    label: "Malta",
    value: "Malta",
  },
  {
    label: "Marshall Islands",
    value: "Marshall Islands",
  },
  {
    label: "Martinique",
    value: "Martinique",
  },
  {
    label: "Mauritania",
    value: "Mauritania",
  },
  {
    label: "Mauritius",
    value: "Mauritius",
  },
  {
    label: "Mayotte",
    value: "Mayotte",
  },
  {
    label: "Mexico",
    value: "Mexico",
  },
  {
    label: "Moldova",
    value: "Moldova",
  },
  {
    label: "Monaco",
    value: "Monaco",
  },
  {
    label: "Mongolia",
    value: "Mongolia",
  },
  {
    label: "Montenegro",
    value: "Montenegro",
  },
  {
    label: "Montserrat",
    value: "Montserrat",
  },
  {
    label: "Morocco",
    value: "Morocco",
  },
  {
    label: "Mozambique",
    value: "Mozambique",
  },
  {
    label: "Myanmar",
    value: "Myanmar",
  },
  {
    label: "Namibia",
    value: "Namibia",
  },
  {
    label: "Nauru",
    value: "Nauru",
  },
  {
    label: "Nepal",
    value: "Nepal",
  },
  {
    label: "Netherlands",
    value: "Netherlands",
  },
  {
    label: "Netherlands Antilles",
    value: "Netherlands Antilles",
  },
  {
    label: "New Caledonia",
    value: "New Caledonia",
  },
  {
    label: "New Zealand",
    value: "New Zealand",
  },
  {
    label: "Nicaragua",
    value: "Nicaragua",
  },
  {
    label: "Niger",
    value: "Niger",
  },
  {
    label: "Nigeria",
    value: "Nigeria",
  },
  {
    label: "Norfolk Island",
    value: "Norfolk Island",
  },
  {
    label: "Norway",
    value: "Norway",
  },
  {
    label: "Oman",
    value: "Oman",
  },
  {
    label: "Pakistan",
    value: "Pakistan",
  },
  {
    label: "Palau",
    value: "Palau",
  },
  {
    label: "Panama",
    value: "Panama",
  },
  {
    label: "Papua New Guinea",
    value: "Papua New Guinea",
  },
  {
    label: "Paraguay",
    value: "Paraguay",
  },
  {
    label: "Lao People's Democratic Republic",
    value: "People's Dem. Rep. of Laos",
  },
  {
    label: "Peru",
    value: "Peru",
  },
  {
    label: "Philippines",
    value: "Philippines",
  },
  {
    label: "Poland",
    value: "Poland",
  },
  {
    label: "Portugal",
    value: "Portugal",
  },
  {
    label: "Puerto Rico",
    value: "Puerto Rico",
  },
  {
    label: "Qatar",
    value: "Qatar",
  },
  {
    label: "Macedonia (the former Yugoslav Republic of)",
    value: "Rep. of Macedonia",
  },
  {
    label: "Repub. of the Congo",
    value: "Repub. of the Congo",
  },
  {
    label: "Reunion Island",
    value: "Reunion Island",
  },
  {
    label: "Romania",
    value: "Romania",
  },
  {
    label: "Russia",
    value: "Russia",
  },
  {
    label: "Rwanda",
    value: "Rwanda",
  },
  {
    label: "Saint Kitts and Nevis",
    value: "Saint Kitts & Nevis",
  },
  {
    label: "Saint Lucia",
    value: "Saint Lucia",
  },
  {
    label: "Saint Vincent and the Grenadines",
    value: "Saint Vincent and the Grenadines",
  },
  {
    label: "Samoa",
    value: "Samoa",
  },
  {
    label: "San Marino",
    value: "San Marino",
  },
  {
    label: "Sao Tome and Principe",
    value: "Sao Tome & Principe",
  },
  {
    label: "Saudi Arabia",
    value: "Saudi Arabia",
  },
  {
    label: "Senegal",
    value: "Senegal",
  },
  {
    label: "Serbia",
    value: "Serbia",
  },
  {
    label: "Seychelles",
    value: "Seychelles",
  },
  {
    label: "Sierra Leone",
    value: "Sierra Leone",
  },
  {
    label: "Singapore",
    value: "Singapore",
  },
  {
    label: "Slovakia",
    value: "Slovakia",
  },
  {
    label: "Slovenia",
    value: "Slovenia",
  },
  {
    label: "Solomon Islands",
    value: "Solomon Islands",
  },
  {
    label: "Somalia",
    value: "Somalia",
  },
  {
    label: "South Africa",
    value: "South Africa",
  },
  {
    label: "South Korea",
    value: "South Korea",
  },
  {
    label: "Spain",
    value: "Spain",
  },
  {
    label: "Sri Lanka",
    value: "Sri Lanka",
  },
  {
    label: "Sudan",
    value: "Sudan",
  },
  {
    label: "Suriname",
    value: "Suriname",
  },
  {
    label: "Swaziland",
    value: "Swaziland",
  },
  {
    label: "Sweden",
    value: "Sweden",
  },
  {
    label: "Switzerland",
    value: "Switzerland",
  },
  {
    label: "Syria",
    value: "Syria",
  },
  {
    label: "Taiwan",
    value: "Taiwan",
  },
  {
    label: "Tajikistan",
    value: "Tajikistan",
  },
  {
    label: "Tanzania",
    value: "Tanzania",
  },
  {
    label: "Thailand",
    value: "Thailand",
  },
  {
    label: "The Bahamas",
    value: "The Bahamas",
  },
  {
    label: "Togo",
    value: "Togo",
  },
  {
    label: "Tonga",
    value: "Tonga",
  },
  {
    label: "Trinidad and Tobago",
    value: "Trinidad & Tobago",
  },
  {
    label: "Tunisia",
    value: "Tunisia",
  },
  {
    label: "Turkey",
    value: "Turkey",
  },
  {
    label: "Turkmenistan",
    value: "Turkmenistan",
  },
  {
    label: "Tuvalu",
    value: "Tuvalu",
  },
  {
    label: "Uganda",
    value: "Uganda",
  },
  {
    label: "Ukraine",
    value: "Ukraine",
  },
  {
    label: "United Arab Emirates",
    value: "United Arab Emirates",
  },
  {
    label: "United Kingdom",
    value: "United Kingdom",
  },
  {
    label: "United States of America",
    value: "United States of America",
  },
  {
    label: "Uruguay",
    value: "Uruguay",
  },
  {
    label: "Uzbekistan",
    value: "Uzbekistan",
  },
  {
    label: "Vanuatu",
    value: "Vanuatu",
  },
  {
    label: "Venezuela",
    value: "Venezuela",
  },
  {
    label: "Vietnam",
    value: "Vietnam",
  },
  {
    label: "Virgin Islands",
    value: "Virgin Islands",
  },
  {
    label: "Western Sahara",
    value: "Western Sahara",
  },
  {
    label: "Yemen",
    value: "Yemen",
  },
  {
    label: "Zambia",
    value: "Zambia",
  },
  {
    label: "Zimbabwe",
    value: "Zimbabwe",
  },
];

export {
  SCHOOL_PRIORITISATION_MIN_LENGTH,
  SCHOOL_PRIORITISATION_MAX_MATCHED,
  AGE_FIELD_OPTIONS,
  LANGUAGE_FIELD_OPTIONS,
  COUNTRY_FIELD_OPTIONS,
};
