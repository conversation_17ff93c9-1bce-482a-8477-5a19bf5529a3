import React, { useMemo } from "react";
import Select from "react-select";
import { WindowedMenuList } from "react-windowed-select";
import CreatableSelect from "react-select/creatable";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import useFormLabel from "./hooks/useFormLabel";
import InputFieldset from "../InputFieldset";
import { IAutocompleteOption, Theme, TReactSelectStyles } from "@/common/types";
import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";
import RedTriangleWarning from "@/components/icons/RedTriangleWarning";
import Text from "../Text";

const VIRTUALIZATION_THRESHOLD = 400;

const DropdownIndicator = (props: { selectProps: { menuIsOpen: boolean } }) => {
  const { selectProps } = props;
  const isOpen = selectProps.menuIsOpen;

  return (
    <ChevronDownIcon
      aria-hidden="true"
      className={cn(
        "size-5 text-black transition-transform duration-300 ease-out",
        isOpen && "rotate-180",
      )}
    />
  );
};

interface Props {
  listLength: number;
  nameOfField: string;
  theme: Theme;
  label: string;
  secondaryLabel?: string;
  handleOnChange: (value: any) => void;
  styles?: any;
  createItemLabel?: string;
  useCreatable?: boolean;
  options: IAutocompleteOption[];
  touched?:
    | boolean
    | Record<string, string>
    | Record<string, boolean | undefined>;
  error?: string | Record<string, string>;
  isClearable?: boolean;
  validation?: string[];
  placeholder?: string;
  value?: IAutocompleteOption;
  isDisabled?: boolean;
  isLoading?: boolean;
  filterOption?: any;
  onInputChange?: any;
  isValidNewOption?: () => boolean;
  openMenuOnFocus?: boolean;
  noOptionsMessage?: () => string;
  formId?: string;
  formatOptionLabel?: (
    option: IAutocompleteOption,
    labelMeta: any,
  ) => React.ReactNode;
}

// Reference: https://github.com/JedWatson/react-select
const AutocompleteField = (props: Props) => {
  const {
    listLength,
    nameOfField,
    theme,
    label = "",
    secondaryLabel = "",
    handleOnChange,
    createItemLabel,
    useCreatable,
    options,
    touched,
    error,
    formId,
    formatOptionLabel,
    ...rest
  } = props;

  const hasErrors = Boolean(touched && error);

  const controlVariants = cva(
    "border py-[12px] px-2 !shadow-none !focus:shadow-none hover:!shadow-none !font-inherit outline-none !text-grays-G5 font-medium flex items-center !rounded-lg  box-border cursor-default flex-wrap justify-between transition-all duration-100 relative",
    {
      variants: {
        theme: {
          light: "!bg-white autofill-light",
          dark: "!bg-primary01-75 autofill-dark",
        },
      },
      defaultVariants: {
        theme: "light",
      },
    },
  );

  const colourStyles = () => ({
    container: () => cn("border-none w-full"),
    control: (state: { isFocused: boolean; menuIsOpen: boolean }) =>
      cn(
        controlVariants({ theme }),
        hasErrors
          ? "!border-primary01-50 !border"
          : state.isFocused
            ? "!border-secondary01-50 !border"
            : "!border-grays-G7 !border",
      ),

    option: (base: TReactSelectStyles) =>
      cn(
        "px-4 py-2 font-body-p text-b1 flex items-center text-red-900 border-none",
        base,
      ),

    placeholder: (base: TReactSelectStyles) =>
      cn(base, "font-body-p text-b1  p-0 !text-grays-G5 !font-normal"),

    singleValue: (base: TReactSelectStyles) =>
      cn(
        base,
        theme === "light" ? "!text-black" : "!text-white",
        "font-inherit font-body-p text-b1 overflow-visible",
      ),

    input: (base: TReactSelectStyles) =>
      cn(
        base,
        "font-body-p font-medium !border-0 !text-grays-G5 ![box-shadow:none] ",
      ),

    indicatorSeparator: (base: TReactSelectStyles) => cn(base, "hidden"),

    indicatorsContainer: (base: TReactSelectStyles) =>
      cn(base, `text-grays-G5`),

    multiValue: (base: TReactSelectStyles) => cn(base, "font-body-p"),

    menuPortal: (base: TReactSelectStyles) => cn(base, "z-[9999]"),
  });

  const sharedProps = {
    classNames: colourStyles(),
    menuPortalTarget:
      typeof document !== `undefined`
        ? document.querySelector("body")
        : undefined,
    onChange: handleOnChange,
    formatOptionLabel,
  };

  const formatCreateLabel = (inputValue: string) => {
    return (
      <Text tag="span" style="b1" className="font-bold">
        {createItemLabel} {`'${inputValue}'`}
      </Text>
    );
  };

  // Filter out invalid options
  const filteredOptions = useMemo(() => {
    if (Array.isArray(options)) {
      return options.filter(
        (option) =>
          option &&
          typeof option.label === "string" &&
          option.value !== undefined,
      );
    }
    return [];
  }, [options]);

  const labelToUse = useFormLabel(label, secondaryLabel);

  const errorMessageToRender =
    typeof error === "string" ? error : error?.[nameOfField];

  return (
    <InputFieldset
      label={labelToUse}
      theme={theme}
      hasValidationError={hasErrors}
      errorMessage={errorMessageToRender}
      nameOfField={nameOfField}
    >
      <div className="relative flex flex-wrap" data-test-id={nameOfField}>
        {listLength > VIRTUALIZATION_THRESHOLD || useCreatable ? (
          // @ts-expect-error: Not typed very well for custom styles
          <CreatableSelect
            components={{ MenuList: WindowedMenuList, DropdownIndicator }}
            formatCreateLabel={formatCreateLabel}
            {...sharedProps}
            {...rest}
            options={filteredOptions}
            name={nameOfField}
            instanceId={nameOfField + formId}
          />
        ) : (
          // @ts-expect-error: Not typed very well for custom styles
          <Select
            {...sharedProps}
            {...rest}
            components={{ DropdownIndicator }}
            options={filteredOptions}
            name={nameOfField}
            instanceId={nameOfField + formId}
          />
        )}
        {hasErrors && (
          <span className="absolute bottom-0 right-11">
            <RedTriangleWarning className="absolute bottom-4 right-2" />
          </span>
        )}
      </div>
    </InputFieldset>
  );
};

export default AutocompleteField;
