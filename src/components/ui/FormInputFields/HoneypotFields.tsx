import React from "react";
import { IFormValues } from "@/components/ui/Forms/types";
import { Field } from "formik";

export interface HoneypotField {
  name: string;
  type: "text" | "email";
}

export const defaultHoneypotFields: HoneypotField[] = [
  { name: "website_url", type: "text" },
  { name: "contact_preference", type: "text" },
];

export const getHoneypotInitialValues = () =>
  defaultHoneypotFields.reduce(
    (acc, field) => ({ ...acc, [field.name]: "" }),
    {},
  );

export const removeHoneypotFields = (formData: Record<string, any>) =>
  Object.fromEntries(
    Object.entries(formData).filter(
      ([key]) =>
        !defaultHoneypotFields.map((field) => field.name).includes(key),
    ),
  );

export const validateHoneypot = (formValues: IFormValues) => {
  if (!defaultHoneypotFields) return true;

  return !defaultHoneypotFields.some((field) => formValues[field.name]);
};

const HoneypotFields = () => {
  return (
    <div
      aria-hidden="true"
      className="pointer-events-none absolute left-[-9999px] top-[-9999px] opacity-0"
    >
      {defaultHoneypotFields.map((field) => (
        <Field
          key={field.name}
          className="text-black"
          type={field.type}
          name={field.name}
          id={field.name}
          autoComplete="off"
          tabIndex={-1}
        />
      ))}
    </div>
  );
};

export default HoneypotFields;
