"use client";
import { useState, useEffect } from "react";
import sfGoogleMap from "@/common/data/sfGoogleMap.json";
import COUNTRIES from "@/common/data/full_countries.json";

interface GoogleCountry {
  "alpha-2": string;
  name: string;
  salesforce?: string;
}

interface UseUserLocationResult {
  userCountry: string | undefined;
  alternateName: string | undefined;
  isLoading: boolean;
  error: Error | null;
}

interface CachedData {
  userCountry: string;
  alternateName: string;
  timestamp: number;
}

const STORAGE_KEY = "userCountry";
const CACHE_DURATION = 60 * 60 * 1000;

const getCachedData = (): CachedData | null => {
  if (typeof window === "undefined") return null;

  try {
    const cached = sessionStorage.getItem(STORAGE_KEY);
    if (!cached) return null;

    const data = JSON.parse(cached) as CachedData;

    const isExpired = Date.now() - data.timestamp > CACHE_DURATION;
    if (isExpired) {
      sessionStorage.removeItem(STORAGE_KEY);
      return null;
    }

    if (data.userCountry && data.alternateName) {
      return data;
    }

    return null;
  } catch (error) {
    console.error(`Fail error`, error);
    sessionStorage.removeItem(STORAGE_KEY);
    return null;
  }
};

const saveCachedData = (userCountry: string, alternateName: string): void => {
  if (typeof window === "undefined") return;

  try {
    const cacheData: CachedData = {
      userCountry,
      alternateName,
      timestamp: Date.now(),
    };
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(cacheData));
  } catch (error) {
    console.warn("Failed to save location data to sessionStorage:", error);
  }
};

const fetchWithRetry = async (
  url: string,
  maxRetries = 3,
  timeout = 5000,
): Promise<Response> => {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          Accept: "application/json",
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        return response;
      }

      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
      const isLastAttempt = attempt === maxRetries - 1;

      if (isLastAttempt) {
        throw error;
      }

      const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw new Error("Max retries exceeded");
};

const processCountryData = (countryCode: string) => {
  const sfCountry = sfGoogleMap.find(
    (country: GoogleCountry) => country["alpha-2"] === countryCode,
  );

  const fullCountryInfo = getCountryInfo(countryCode);

  const userCountry = sfCountry?.salesforce ?? fullCountryInfo.name;
  const alternateName = sfCountry?.name ?? fullCountryInfo.alternateName;

  return {
    userCountry,
    alternateName,
    isLoading: false,
    error: null,
  };
};

/**
 * Helper function to get country information from country code
 */
function getCountryInfo(code: string): { name: string; alternateName: string } {
  // Find in the complete country list
  const country = COUNTRIES.find((c) => c["alpha-2"] === code);

  if (country) {
    return {
      name: country.name || country.en || code,
      alternateName: country.en || country.name || code,
    };
  }

  // If not found, return the code itself
  return {
    name: code,
    alternateName: code,
  };
}

/**
 * Custom hook to get user's location information
 * Returns country information including Salesforce country name and alternate country name
 */
export default function useUserLocation(): UseUserLocationResult {
  const [state, setState] = useState<UseUserLocationResult>(() => {
    const cached = getCachedData();
    if (cached) {
      return {
        userCountry: cached.userCountry,
        alternateName: cached.alternateName,
        isLoading: false,
        error: null,
      };
    }

    return {
      userCountry: undefined,
      alternateName: undefined,
      isLoading: true,
      error: null,
    };
  });

  useEffect(() => {
    if (state.userCountry && state.alternateName && !state.isLoading) {
      return;
    }

    if (!state.isLoading) {
      return;
    }

    let isCancelled = false;

    const fetchUserLocation = async () => {
      try {
        if (!isCancelled) {
          setState((prev) => ({
            ...prev,
            isLoading: true,
            error: null,
          }));
        }

        const response = await fetchWithRetry("https://api.country.is/");
        const data = (await response.json()) as { country: string };

        if (isCancelled) return;

        if (data?.country) {
          const result = processCountryData(data.country);

          setState(result);

          saveCachedData(result.userCountry, result.alternateName);
        } else {
          throw new Error("Country information not available in response");
        }
      } catch (error) {
        if (!isCancelled) {
          const errorMessage =
            error instanceof Error ? error.message : "Unknown error occurred";

          console.error("Error fetching user location:", errorMessage);

          setState((prev) => ({
            ...prev,
            isLoading: false,
            error: error instanceof Error ? error : new Error(errorMessage),
          }));
        }
      }
    };

    fetchUserLocation().catch(() => {
      // handle error
    });

    return () => {
      isCancelled = true;
    };
  }, [state.userCountry, state.alternateName, state.isLoading]);

  return state;
}
