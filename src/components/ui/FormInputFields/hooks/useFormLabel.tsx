import { useFormikContext } from "formik";

interface IFormValues {
  person_type__c?: string;
}

const useFormLabel = (label: string, secondaryLabel?: string) => {
  const { values } = useFormikContext<IFormValues>();
  const selectedPersonType = values?.person_type__c;

  const labelToDisplay =
    secondaryLabel && selectedPersonType === "Guardian"
      ? secondaryLabel
      : label;

  return labelToDisplay;
};

export default useFormLabel;
