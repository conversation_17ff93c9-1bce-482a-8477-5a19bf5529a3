import React, { useEffect, useState } from "react";
import { FormikErrors, useFormikContext } from "formik";
import { createFilter } from "react-select";
import {
  SCHOOL_PRIORITISATION_MIN_LENGTH,
  SCHOOL_PRIORITISATION_MAX_MATCHED,
} from "./constants";
import AutocompleteField from "./AutocompleteField";
import { IAutocompleteOption, Theme, IStory } from "@/common/types";
import { IFormValues } from "../Forms/types";
import useCSVParser from "@/common/hooks/useCSVParser";
import { usePageContext } from "@/components/context/PageContext";
import { fetchStoryblokStories } from "@/common/storyblok";

const getSchoolValues = (stringOfValues: string) => stringOfValues.split(";");

interface ISchoolPrioritisationList {
  label?: string;
  value?: string;
  country?: string;
}

const normalizeSchoolPrioritisationList = (
  prioritisationList: ISchoolPrioritisationList[],
  filterByCountry = false,
  countryInput = "",
) =>
  prioritisationList
    .filter((item) => {
      // Checkbox in Storyblok
      if (!filterByCountry) return true;
      // Country field from the page
      if (!countryInput) return true;
      // "country" column from the CSV
      if (!item.country) return true;

      return item?.country?.split(";").includes(countryInput);
    })
    .map((item) => {
      if (!item.value) return null;
      return { label: item.label, value: getSchoolValues(item.value) };
    })
    .filter((item) => item !== null);

interface Props {
  label: string;
  nameOfField: string;
  setFieldValue: (
    field: string,
    value: any,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
  theme: Theme;
  placeholder?: string;
  validation?: string[];
  touched?:
    | boolean
    | Record<string, string>
    | Record<string, boolean | undefined>;
  error?: string | Record<string, string>;
  limitCharacters?: boolean;
  noOptionsLabel?: string;
  minCharacterLabel?: string;
  schoolPrioritisation?: string;
  filterByCountry?: boolean;
  formId?: string;
}

const SchoolPrioritisationField = ({
  setFieldValue,
  nameOfField,
  minCharacterLabel = "Search input must be at least 3 characters",
  filterByCountry,
  limitCharacters: removeCharacterLimit = false,
  formId,
  ...props
}: Props) => {
  const { locale } = usePageContext();
  const [spLink, setSpLink] = useState("");
  const [showOptions, setShowOptions] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  let modifiedSchoolPrioritisation;
  if (spLink) {
    modifiedSchoolPrioritisation = spLink.replace(
      "//a.",
      "//s3.amazonaws.com/a.",
    );
  }
  const prioritisationList = useCSVParser(modifiedSchoolPrioritisation) ?? [];
  const { values } = useFormikContext<IFormValues>();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data } = await fetchStoryblokStories({
          starts_with: `${locale}/`,
          filter_query: {
            component: {
              in: "settings",
            },
          },
        });

        const stories = (data as { stories: IStory[] })?.stories;
        const content = stories?.[0]?.content as Record<string, any>;
        const schoolPrioritisation = content?.schoolPrioritisation as
          | string
          | undefined;

        setSpLink(schoolPrioritisation ?? "");
      } catch (error) {
        console.error("Error fetching settings data:", error);
        setSpLink("");
      } finally {
        setIsLoading(false);
      }
    };

    void fetchData();
  }, [locale]); // eslint-disable-line react-hooks/exhaustive-deps

  if (isLoading) return null;

  /**
   * Reference:
   * https://www.storyblok.com/faq/xmlhttprequest-cannot-load-no-access-control-allow-origin-header
   * In a situation like this where we're using a CSV file as a data source straight from storyblok,
   * we sometimes end up with a CORS error. To account for this, we replace the regular storyblok
   * CDN URL with the S3 URL (see reference link for more information).
   */

  const countryInput = values?.country ?? "";
  const schoolPrioritisationList: IAutocompleteOption[] =
    normalizeSchoolPrioritisationList(
      prioritisationList,
      filterByCountry,
      countryInput,
    );

  const stringify = (option: IAutocompleteOption) => {
    if (!option?.label || !option?.value) return "";

    const valueCheck = Array.isArray(option?.value)
      ? option?.value.flat().join(";")
      : "";
    return `${option?.label} ${valueCheck}`;
  };

  const getInputValue = (inputValue: { value: string | string[] }) => {
    const isResultArray = Array.isArray(inputValue?.value);
    return isResultArray ? inputValue?.value[0] : inputValue?.value;
  };

  const handleInputChange = (inputValue: string) => {
    const shouldHideOptions =
      inputValue.length < SCHOOL_PRIORITISATION_MIN_LENGTH &&
      !removeCharacterLimit;

    if (!inputValue || shouldHideOptions) {
      setShowOptions(false);
      return;
    }

    if (removeCharacterLimit) {
      setShowOptions(true);
      return;
    }

    // See how many schools will be matched
    const matchedOptions = schoolPrioritisationList.filter((option) => {
      if (option?.label && option?.value) {
        return stringify(option).trim().toLowerCase().includes(inputValue);
      }
      return false;
    });
    // Only show schools list (as well as create new label) when no more than e.g. 4 matched
    setShowOptions(matchedOptions.length <= SCHOOL_PRIORITISATION_MAX_MATCHED);
  };

  return (
    <AutocompleteField
      listLength={schoolPrioritisationList.length}
      options={showOptions ? schoolPrioritisationList : []}
      nameOfField={nameOfField}
      handleOnChange={(selectedValue: { value: string | string[] }) => {
        setFieldValue(nameOfField, getInputValue(selectedValue) ?? "").catch(
          console.error,
        );
      }}
      filterOption={createFilter({ ignoreAccents: false })}
      isClearable={true}
      useCreatable={true}
      onInputChange={handleInputChange}
      isValidNewOption={() => showOptions}
      openMenuOnFocus={false}
      noOptionsMessage={() => minCharacterLabel}
      formId={formId}
      {...props}
    />
  );
};

export default SchoolPrioritisationField;
