import { Theme } from "@/common/types";
import PhoneNumberField from "@/components/ui/FormInputFields/PhoneNumberField";
import { usePageContext } from "@/components/context/PageContext";

interface StudentField {
  label: string;
  marketoField: string;
}

interface GuardianField {
  label: string;
  marketoField: string;
}

interface Props {
  studentLocalNumberPlaceholder: string;
  studentCountryCodePlaceholder: string;
  theme: Theme;
  studentField: StudentField;
  guardianField: GuardianField;
  guardianLocalNumberPlaceholder: string;
  guardianCountryCodePlaceholder: string;
  values: Record<string, any>;
  touched: Record<string, boolean>;
  errors: Record<string, string>;
}

const STUDENT_PERSON_TYPE = "Student";
const STUDENT_LOCALE_NUMBER = "studentLocalNumber";
const STUDENT_COUNTRY_CODE = "studentCountryCode";
const GUARDIAN_LOCALE_NUMBER = "guardianLocalNumber";
const GUARDIAN_COUNTRY_CODE = "guardianCountryCode";
const STUDENT_PHONE_NUMBER = "studentPhoneNumber";
const GUARDIAN_PHONE_NUMBER = "guardianPhoneNumber";

const ConditionalPhoneField = ({
  studentLocalNumberPlaceholder,
  studentCountryCodePlaceholder,
  studentField,
  guardianField,
  guardianLocalNumberPlaceholder,
  guardianCountryCodePlaceholder,
  values,
  touched,
  errors,
  theme,
}: Props) => {
  const { person_type__c } = values;
  const { isVisualComposer } = usePageContext();

  const { label: studentLabel } = studentField;
  const { label: guardianLabel } = guardianField;

  const isStudentField = person_type__c === STUDENT_PERSON_TYPE;
  const localeNumberField = isStudentField
    ? GUARDIAN_LOCALE_NUMBER
    : STUDENT_LOCALE_NUMBER;
  const countryCodeField = isStudentField
    ? GUARDIAN_COUNTRY_CODE
    : STUDENT_COUNTRY_CODE;
  const phoneNumberField = isStudentField
    ? GUARDIAN_PHONE_NUMBER
    : STUDENT_PHONE_NUMBER;
  const label = isStudentField ? guardianLabel : studentLabel;
  const LocalNumberPlaceholder = isStudentField
    ? guardianLocalNumberPlaceholder
    : studentLocalNumberPlaceholder;
  const countryCodePlaceholder = isStudentField
    ? guardianCountryCodePlaceholder
    : studentCountryCodePlaceholder;

  const phoneTouched = {
    [localeNumberField]: touched[localeNumberField],
    [countryCodeField]: touched[countryCodeField],
  };

  const phoneErrors = {
    [localeNumberField]: errors[localeNumberField],
    [countryCodeField]: errors[countryCodeField],
  };

  if (isVisualComposer || person_type__c) {
    return (
      <div>
        <PhoneNumberField
          label={label}
          nameOfField={localeNumberField}
          localNumberFieldName={localeNumberField}
          localNumberPlaceholder={LocalNumberPlaceholder}
          countryCodeFieldName={countryCodeField}
          countryCodePlaceholder={countryCodePlaceholder}
          theme={theme}
          formId={phoneNumberField}
          isConditional={true}
          conditionalErrors={
            phoneErrors[localeNumberField] ?? phoneErrors[countryCodeField]
          }
          conditionalTouched={
            phoneTouched[localeNumberField] ?? phoneTouched[countryCodeField]
          }
        />
      </div>
    );
  }

  return null;
};

export default ConditionalPhoneField;
