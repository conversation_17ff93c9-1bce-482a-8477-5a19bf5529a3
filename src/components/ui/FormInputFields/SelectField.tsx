import React from "react";
import { useFormikContext, getIn } from "formik";
import Select from "react-select";
import { WindowedMenuList } from "react-windowed-select";
import useFormLabel from "./hooks/useFormLabel";
import InputFieldset from "../InputFieldset";
import { IAutocompleteOption, Theme, TReactSelectStyles } from "@/common/types";
import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";
import RedTriangleWarning from "@/components/icons/RedTriangleWarning";

const VIRTUALIZATION_THRESHOLD = 400;

interface SelectFieldProps {
  nameOfField: string;
  label: string;
  placeholder?: string;
  options: IAutocompleteOption[];
  theme: Theme;
  error?: string;
  touched?: boolean;
  isDisabled?: boolean;
  isClearable?: boolean;
  formId?: string;
  secondaryLabel?: string;
}

const SelectField: React.FC<SelectFieldProps> = ({
  nameOfField,
  label,
  placeholder,
  options,
  theme,
  error,
  touched,
  isDisabled = false,
  isClearable = false,
  formId,
  secondaryLabel = "",
}) => {
  const { values, setFieldValue } = useFormikContext<Record<string, any>>();
  const hasErrors = Boolean(touched && error);
  const currentValue = getIn(values, nameOfField);

  const selectedOption =
    options.find((option) => option.value === currentValue) ?? null;

  const handleChange = async (selectedOption: IAutocompleteOption | null) => {
    await setFieldValue(
      nameOfField,
      selectedOption ? selectedOption.value : "",
    );
  };

  const controlVariants = cva("", {
    variants: {
      theme: {
        light: "!bg-white autofill-light",
        dark: "!bg-blue-600 autofill-dark",
      },
    },
    defaultVariants: {
      theme: "light",
    },
  });

  const colourStyles = (): any => ({
    container: () => cn("border-none w-full"),
    control: (state: { isFocused: boolean }) =>
      cn(
        controlVariants({ theme }),
        "!font-inherit outline-none !text-grey-300 px-0.3 pr-4 py-[2px] font-medium flex items-center !rounded-lg h-12 md:!h-[58px] box-border cursor-default flex-wrap justify-between transition-all duration-100 relative",
        "!shadow-none !focus:shadow-none hover:!shadow-none",
        hasErrors
          ? "!border-primary01-50 !border"
          : state.isFocused
            ? "!border-secondary01-25 !border"
            : "!border-grays-G7 !border",
      ),
    option: (base: TReactSelectStyles) =>
      cn(
        "px-4 py-2 font-body-p flex items-center text-red-900 border-none",
        base,
      ),
    placeholder: (base: TReactSelectStyles) =>
      cn(base, "font-body-p p-0 !text-grays-G5 !font-normal"),
    singleValue: (base: TReactSelectStyles) =>
      cn(
        base,
        theme === "light" ? "!text-blue-700" : "!text-white",
        "font-inherit font-body-p font-medium overflow-visible",
      ),
    input: (base: TReactSelectStyles) =>
      cn(
        base,
        "font-body-p font-medium !border-0 !text-grey-300 ![box-shadow:none]",
      ),
    indicatorSeparator: (base: TReactSelectStyles) => cn(base, "hidden"),
    indicatorsContainer: (base: TReactSelectStyles) =>
      cn(base, "text-grey-300"),
    menuPortal: (base: TReactSelectStyles) => cn(base, "z-[9999]"),
  });

  const labelToUse = useFormLabel(label, secondaryLabel);

  return (
    <InputFieldset
      label={labelToUse}
      theme={theme}
      hasValidationError={hasErrors}
      errorMessage={error}
      nameOfField={nameOfField}
    >
      <div className="relative flex flex-wrap" data-test-id={nameOfField}>
        {options.length > VIRTUALIZATION_THRESHOLD ? (
          <Select
            components={{ MenuList: WindowedMenuList }}
            classNames={colourStyles()}
            menuPortalTarget={
              typeof document !== "undefined"
                ? document.querySelector("body")
                : undefined
            }
            value={selectedOption}
            onChange={handleChange}
            options={options}
            placeholder={placeholder}
            isDisabled={isDisabled}
            isClearable={isClearable}
            name={nameOfField}
            instanceId={nameOfField + formId}
          />
        ) : (
          <Select
            classNames={colourStyles()}
            menuPortalTarget={
              typeof document !== "undefined"
                ? document.querySelector("body")
                : undefined
            }
            value={selectedOption}
            onChange={handleChange}
            options={options}
            placeholder={placeholder}
            isDisabled={isDisabled}
            isClearable={isClearable}
            name={nameOfField}
            instanceId={nameOfField + formId}
          />
        )}
        {hasErrors && (
          <RedTriangleWarning
            className={cn("absolute bottom-3 right-12 lg:bottom-5 lg:right-12")}
          />
        )}
      </div>
    </InputFieldset>
  );
};

export default SelectField;
