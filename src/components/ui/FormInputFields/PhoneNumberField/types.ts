import { Theme } from "@/common/types";
import { SingleValue } from "react-select";

export interface PhoneNumberFieldProps {
  label: string;
  nameOfField: string;
  language?: keyof CountryCode;
  errors?: IPhoneNumberFieldError;
  touched?: IPhoneNumberFieldTouched;
  values?: IPhoneNumberFieldValues;
  theme: Theme;
  className?: string;
  secondaryLabel?: string;
  localNumberFieldName?: string;
  localNumberPlaceholder?: string;
  phoneNumberFieldName?: string;
  countryCodeFieldName?: string;
  countryCodePlaceholder?: string;
  formId: string;
  autofillCountryCodeByIP?: boolean;
  isConditional?: boolean;
  conditionalErrors?: string;
  conditionalTouched?: boolean;
  enableCountryLinking?: boolean;
}

export interface ICountryCodeInputProps {
  countryCodeFieldName: string;
  formId: string;
  countryCodePlaceholder?: string;
  theme: Theme;
  hasErrors: boolean;
  userCountry?: string;
  selectedCountryCode: TSelectedCountryCode;
  enableCountryLinking: boolean;
  language: keyof CountryCode;
}

export interface IPhoneNumberFieldError {
  countryCode: {
    value: string;
    label?: string;
  };
  localNumber: string;
}

export interface IPhoneNumberFieldTouched {
  countryCode: {
    label: boolean;
    value: boolean;
  };
  localNumber: boolean;
}

export interface IPhoneNumberFieldValues {
  countryCode: {
    label: string;
    value: string;
  };
  localNumber: string;
}

export interface IFilterOption {
  label: string;
  value: string;
  data: {
    label: string;
    value: string;
    en?: string;
  };
}

export interface CountryCode {
  "dial-code": string;
  salesforce: string;
  en: string;
  es: string;
}

export interface ICountryOption {
  alpha2: string;
  label: string;
  value: string;
  en: string;
  salesforce: string;
}

export type TReactSelectSingleValue = SingleValue<
  string | { value: string; label: string }
>;

export type TSelectedCountryCode = { value: string; label: string } | undefined;
