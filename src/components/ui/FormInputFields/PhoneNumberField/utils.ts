import { cva } from "class-variance-authority";
import { IPhoneNumberFieldError } from "./types";

export const getPhoneNumberFieldErrorMessage = (
  isConditional?: boolean,
  conditionalErrors?: string,
  errors?: IPhoneNumberFieldError,
): string | undefined => {
  if (isConditional) {
    return conditionalErrors;
  }

  const countryCodeError = errors?.countryCode?.value;
  const localNumberError = errors?.localNumber;

  return countryCodeError ?? localNumberError;
};

export const phoneNumberFieldVariants = cva(
  "w-full rounded-xl border-0 px-4 py-2 outline-none ring-0 ring-offset-0 shadow-none focus:ring-0 focus:ring-offset-0 focus:shadow-none placeholder:text-grays-G5 placeholder:font-body-single !text-b1 text-body-p block pl-[10px]",
  {
    variants: {
      theme: {
        dark: "bg-primary01-75 text-white autofill-dark",
        light: "bg-white text-neutral01-100 autofill-light",
      },
      errors: {
        true: "",
        false: "",
      },
    },
    defaultVariants: {
      theme: "light",
      errors: false,
    },
  },
);

export const countryCodeControlVariants = cva(
  "!font-inherit outline-none rounded-md cursor-default transition-all duration-100 h-full max-w-[60px] min-w-[60px] rounded-r-none border-0 !shadow-none !focus:shadow-none hover:!shadow-none !min-h-[30px] !max-h-[34px] !text-grays-G5",
  {
    variants: {
      theme: {
        light: "!bg-white autofill-light",
        dark: "!bg-primary01-75 autofill-dark",
      },
      errors: {
        true: "!border-0",
        false: "!border-0",
      },
    },
    defaultVariants: {
      theme: "light",
      errors: false,
    },
  },
);
