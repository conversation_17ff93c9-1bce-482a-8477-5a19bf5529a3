import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  ICountryOption,
  IFilterOption,
  TReactSelectSingleValue,
  ICountryCodeInputProps,
} from "./types";
import Select, { components } from "react-select";
import { cn } from "@/common/utils";
import { TReactSelectStyles } from "@/common/types";
import Text from "../../Text";
import { ChevronDownIcon } from "@heroicons/react/24/solid";
import { useFormikContext } from "formik";
import { IFormValues } from "../../Forms/types";
import COUNTRY_CODES from "@/common/data/full_countries.json";
import useUserLocation from "../hooks/useUserLocation";
import { countryCodeControlVariants } from "./utils";

const { Placeholder } = components;

function CountryFieldInput({
  countryCodeFieldName,
  formId,
  countryCodePlaceholder,
  theme,
  hasErrors,
  selectedCountryCode,
  enableCountryLinking,
  language,
}: ICountryCodeInputProps) {
  const { setFieldValue, values } = useFormikContext<IFormValues>();
  const hasRun = useRef(false);
  const selectedCountryLabel = values?.country;
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const { userCountry, alternateName } = useUserLocation();

  const countryCodeOptions = useMemo(
    () =>
      COUNTRY_CODES.sort((a, b) => {
        if ((a[language] ?? a.en) < (b[language] ?? b.en)) return -1;
        if ((a[language] ?? a.en) > (b[language] ?? b.en)) return 1;
        return 0;
      }).map((country) => ({
        en: country.en,
        value: country["dial-code"],
        label: `${country[language] ?? country.en} (${country["dial-code"]})`,
        alpha2: country["alpha-2"],
        countryLabel: `${country?.salesforce ?? country[language] ?? country.en}`,
      })),
    [language],
  );

  const colourStyles = () => ({
    container: () => cn("border-0 w-full"),
    control: () => cn(countryCodeControlVariants({ theme, errors: hasErrors })),
    option: (base: TReactSelectStyles) =>
      cn(
        base,
        "px-4 py-2 text-b1 flex items-center text-red-900 border-none width-full",
      ),
    singleValue: (base: TReactSelectStyles) =>
      cn(base, "font-inherit text-b1 overflow-visible !text-white"),
    input: (base: TReactSelectStyles) =>
      cn(
        { ...base, boxShadow: "none" },
        "!text-grey-300 !shadow-none !focus:shadow-none",
      ),
    menuList: (base: TReactSelectStyles) => cn(base, "w-[500px] bg-white"),
  });

  const sharedProps = {
    classNames: colourStyles(),
    menuPortalTarget:
      typeof document !== `undefined`
        ? document.querySelector("body")
        : undefined,
  };

  const filterOption = (option: IFilterOption, inputValue: string) => {
    if (!inputValue || !option) return true;

    const { label, value, data } = option;
    const regex = new RegExp(`[.s]*${inputValue}[.s]*`, "gi");

    return (
      regex.test(label) ||
      regex.test(value) ||
      (!!data.en && regex.test(data.en))
    );
  };

  const handleCountryCodeChange = async (option: TReactSelectSingleValue) => {
    await setFieldValue(countryCodeFieldName, option);
  };

  useEffect(() => {
    if (!hasRun.current && userCountry) {
      const countryCodeByName = countryCodeOptions.find((option) => {
        return option.en === userCountry || option.en === alternateName;
      });

      if (countryCodeByName) {
        void handleCountryCodeChange(countryCodeByName);
      }

      if (countryCodeByName) {
        hasRun.current = true;
      }
    }
  }, [userCountry, alternateName]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (!enableCountryLinking) {
      return;
    }
    if (selectedCountryLabel && countryCodeOptions.length > 0) {
      const matchingCountryCode = countryCodeOptions.find((option) => {
        return option.countryLabel === selectedCountryLabel;
      });

      if (matchingCountryCode) {
        const currentCountryCode = values?.[countryCodeFieldName] as
          | { value: string; label: string }
          | undefined;

        if (currentCountryCode?.value !== matchingCountryCode.value) {
          void handleCountryCodeChange(matchingCountryCode);
        }
      }
    }
  }, [enableCountryLinking, selectedCountryLabel, countryCodeOptions]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="shrink-0">
      <label htmlFor={countryCodeFieldName + formId} className="sr-only">
        Country Code
      </label>
      {/* @ts-expect-error: Not typed very well for custom styles */}
      <Select
        {...sharedProps}
        options={countryCodeOptions}
        onMenuOpen={() => setIsMenuOpen(true)}
        onMenuClose={() => setIsMenuOpen(false)}
        components={{
          DropdownIndicator: () => null,
          IndicatorSeparator: () => null,
          Placeholder: (props) => (
            <Placeholder {...props}>
              <div
                className={cn("p-0 !font-body-single text-b1 !text-grays-G5")}
              >
                {props.selectProps.placeholder}
              </div>
            </Placeholder>
          ),
          Input: (
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            { autoComplete, ...props },
          ) => (
            <components.Input
              {...props}
              autoComplete="none"
              autoCorrect="off"
              spellCheck={false}
            />
          ),
        }}
        id="countryCode"
        value={selectedCountryCode?.value === "" ? null : selectedCountryCode}
        onChange={(option) => handleCountryCodeChange(option)}
        filterOption={filterOption}
        maxMenuHeight={150}
        theme={(themeProp) => ({
          ...themeProp,
          borderRadius: 0,
        })}
        placeholder={countryCodePlaceholder}
        instanceId={countryCodeFieldName + formId}
        isDisabled={userCountry === undefined}
        isLoading={userCountry === undefined}
        formatOptionLabel={(
          option,
          labelMeta: {
            context: string;
          },
        ) => {
          const countryOption = option as ICountryOption;
          const context = labelMeta?.context;
          if (context === "value") {
            return (
              <div className="flex w-full cursor-pointer items-center justify-between">
                <div
                  className={cn(
                    "fi mr-2 size-[1.25rem]",
                    `fi-${countryOption?.alpha2?.toLowerCase()}`,
                  )}
                ></div>
                <ChevronDownIcon
                  className={cn(
                    "size-4 cursor-pointer fill-neutral01-75 stroke-neutral01-75 transition-transform duration-200",
                    isMenuOpen && "rotate-180",
                  )}
                />
              </div>
            );
          }
          return (
            <div className="flex items-center">
              <span
                className={cn(
                  "fi mr-2 size-[1.25rem]",
                  `fi-${countryOption?.alpha2?.toLowerCase()}`,
                )}
              ></span>
              <Text tag="span" style="b1" className="mr-1 text-sm md:text-base">
                {countryOption.label}
              </Text>
            </div>
          );
        }}
      />
    </div>
  );
}

export default CountryFieldInput;
