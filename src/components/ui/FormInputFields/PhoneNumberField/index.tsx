import { useState } from "react";
import { Field, useFormikContext } from "formik";
import InputFieldset from "../../InputFieldset";
import { cn } from "@/common/utils";
import { IFormValues } from "../../Forms/types";
import RedTriangleWarning from "@/components/icons/RedTriangleWarning";
import { PhoneNumberFieldProps, TSelectedCountryCode } from "./types";
import {
  getPhoneNumberFieldErrorMessage,
  phoneNumberFieldVariants,
} from "./utils";
import CountryFieldInput from "./CountryFieldInput";

const PhoneNumberField = (props: PhoneNumberFieldProps) => {
  const { values } = useFormikContext<IFormValues>();
  const [isComponentFocused, setIsComponentFocused] = useState(false);

  const {
    label,
    errors,
    touched,
    language = "en",
    theme,
    localNumberFieldName,
    localNumberPlaceholder,
    countryCodeFieldName = "countryCode",
    countryCodePlaceholder,
    formId,
    isConditional,
    conditionalErrors,
    conditionalTouched,
    enableCountryLinking = false,
  } = props;

  const selectedCountryCode = values?.[
    countryCodeFieldName
  ] as TSelectedCountryCode;

  const hasCountryCodeErrors = isConditional
    ? Boolean(conditionalTouched && conditionalErrors)
    : Boolean(
        (touched?.countryCode?.value ?? touched?.localNumber) &&
          errors?.countryCode?.value,
      );

  const hasNumberErrors = isConditional
    ? Boolean(conditionalTouched && conditionalErrors)
    : Boolean(touched?.localNumber && errors?.localNumber);

  const hasErrors = hasCountryCodeErrors || hasNumberErrors;

  const errorMessage = getPhoneNumberFieldErrorMessage(
    isConditional,
    conditionalErrors,
    errors,
  );

  return (
    <InputFieldset
      nameOfField={localNumberFieldName + formId}
      label={label}
      theme={theme}
      hasValidationError={hasErrors}
      errorMessage={errorMessage}
    >
      <div
        className={`relative flex h-[58px] items-center overflow-hidden rounded-lg border transition-colors duration-200 ${
          hasErrors
            ? "border border-primary01-50"
            : isComponentFocused
              ? "border border-secondary01-50"
              : "border-grays-G7"
        }`}
      >
        <CountryFieldInput
          countryCodeFieldName={countryCodeFieldName}
          formId={formId}
          countryCodePlaceholder={countryCodePlaceholder}
          theme={theme}
          hasErrors={hasErrors}
          selectedCountryCode={selectedCountryCode}
          enableCountryLinking={enableCountryLinking}
          language={language}
        />
        <div
          className={`shrink-0 self-stretch border-l ${
            hasErrors
              ? "border-l border-primary01-50"
              : isComponentFocused
                ? "border-l border-secondary01-50"
                : "border-l border-grays-G7"
          }`}
        ></div>
        <div className="text-gray-700 ml-[12px] shrink-0 text-b1">
          {selectedCountryCode?.value}
        </div>
        <div className="grow">
          <Field
            id={localNumberFieldName + formId}
            name={localNumberFieldName}
            type="text"
            placeholder={localNumberPlaceholder}
            className={cn(
              phoneNumberFieldVariants({ theme, errors: hasErrors }),
            )}
            onFocus={() => {
              setIsComponentFocused(true);
            }}
            onBlur={() => {
              setIsComponentFocused(false);
            }}
          />
        </div>

        {hasErrors && (
          <RedTriangleWarning className="absolute bottom-5 right-2" />
        )}
      </div>
    </InputFieldset>
  );
};

export default PhoneNumberField;
