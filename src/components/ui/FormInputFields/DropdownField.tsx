import { useState } from "react";
import InputFieldset from "../InputFieldset";
import { IDropdownOption, Theme } from "@/common/types";
import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";
import { useFormikContext } from "formik";
import Alert from "../Alert";
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from "@headlessui/react";
import { CheckIcon, ChevronDownIcon } from "@heroicons/react/16/solid";
import RedTriangleWarning from "@/components/icons/RedTriangleWarning";
import Text from "../Text";

interface Props {
  label?: string;
  nameOfField: string;
  placeholder?: string;
  error?: string;
  touched?: boolean;
  theme: Theme;
  className?: string;
  secondaryLabel?: string;
  flatOptionsList?: string[];
  formId?: string;
  hideLabel?: boolean;
  hideErrorMessage?: boolean;
  // Dynamic number of options
  [key: `option_${number}`]: IDropdownOption;
}

const dropdownVariants = cva(
  "w-full rounded-lg p-[0.9rem] border border-transparent outline outline-grays-G7 outline-1 transition-all duration-75  relative grid cursor-default grid-cols-1 text-left",
  {
    variants: {
      theme: {
        dark: "bg-primary01-75 text-white autofill-dark",
        light: "bg-white text-grays-G5 autofill-light ",
      },
      errors: {
        true: "border-primary01-50 focus:border-primary01-50 focus:shadow-[0_0_0_1px_theme(colors.primary01.50)]",
        false: "focus:border-secondary01-50",
      },
    },
    defaultVariants: {
      theme: "light",
      errors: false,
    },
  },
);

const DropdownField = (props: Props) => {
  const {
    label,
    nameOfField,
    placeholder = "Select a value",
    error,
    touched,
    theme,
    className,
    flatOptionsList,
    hideLabel,
    hideErrorMessage,
    secondaryLabel,
  } = props;

  const [selected, setSelected] = useState("");
  const { setFieldValue, values } = useFormikContext();

  const hasErrors = Boolean(touched && error);

  const handleFieldChange = (value: string) => {
    setSelected(value);
    setFieldValue(nameOfField, value).catch((error) => {
      console.error("Error setting field value:", error);
    });
  };

  const options = Object.keys(props)
    .filter((key) => key.startsWith("option_"))
    .map((key) => {
      const option = props[key as keyof typeof props] as IDropdownOption;
      if (!option.marketoField) {
        return null;
      }

      return {
        label: option.label,
        value: option.marketoField,
        _uid: option._uid,
      };
    })
    .filter((option) => {
      return option !== null && option.label !== "" && option.value !== "";
    });

  if (options.length < 1 && !flatOptionsList) {
    return (
      <Alert theme="error">
        No dropdown options found for this field: {nameOfField}
      </Alert>
    );
  }

  const personType = (values as Record<string, string>)?.person_type__c;
  const labelValue =
    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
    secondaryLabel && personType === "Guardian" ? secondaryLabel : label;

  return (
    <InputFieldset
      label={labelValue}
      theme={theme}
      hasValidationError={hasErrors}
      errorMessage={error}
      hideLabel={hideLabel}
      nameOfField={nameOfField}
      hideErrorMessage={hideErrorMessage}
    >
      <Listbox value={selected} onChange={handleFieldChange}>
        {({ open }) => (
          <>
            <div className="relative">
              <ListboxButton
                className={cn(
                  dropdownVariants({ theme, errors: hasErrors }),
                  className,
                  open && "outline-secondary01-50",
                )}
              >
                <Text
                  tag="span"
                  style="b1"
                  className={cn(
                    "col-start-1 row-start-1 truncate pr-6",
                    selected ? "text-black" : "text-grays-G5",
                    "data-[focus]:text-black",
                  )}
                >
                  {flatOptionsList?.find((option) => option === selected) ??
                    options.find((option) => option?.value === selected)
                      ?.label ??
                    placeholder}
                </Text>
                {hasErrors && (
                  <span className="absolute bottom-0 right-9">
                    <RedTriangleWarning className="absolute bottom-4 right-2" />
                  </span>
                )}
                <div className="relative left-2 col-start-1 row-start-1 self-center justify-self-end pl-2">
                  <ChevronDownIcon
                    aria-hidden="true"
                    className={cn(
                      "size-5 text-black transition-transform duration-300 ease-out",
                      open && "rotate-180",
                    )}
                  />
                </div>
              </ListboxButton>

              <ListboxOptions
                transition
                className={cn(
                  "focus-none absolute z-10 mt-3 max-h-60 w-full overflow-auto rounded-md bg-white shadow-lg ring-1 ring-black/5 data-[closed]:data-[leave]:opacity-0 data-[leave]:transition data-[leave]:duration-100 data-[leave]:ease-in",
                  open && "border border-secondary01-50",
                )}
              >
                {flatOptionsList?.map((option, index) => {
                  return (
                    <ListBoxOptionContainer
                      optionValue={option}
                      key={option + index}
                    >
                      <ListBoxOptionLabel label={option} />
                    </ListBoxOptionContainer>
                  );
                })}

                {!flatOptionsList &&
                  options.map((option, index) => {
                    return (
                      <ListBoxOptionContainer
                        optionValue={option?.value ?? ""}
                        key={
                          option?.label ?? option?.value ?? "option_" + index
                        }
                        className="z-10"
                      >
                        <ListBoxOptionLabel label={option?.label ?? ""} />
                      </ListBoxOptionContainer>
                    );
                  })}
              </ListboxOptions>
            </div>
          </>
        )}
      </Listbox>
    </InputFieldset>
  );
};

const ListBoxOptionContainer = ({
  children,
  optionValue,
  className,
}: {
  children: React.ReactNode;
  optionValue: string;
  className?: string;
}) => {
  return (
    <ListboxOption
      value={optionValue}
      className={cn(
        "pointer-cursor group relative select-none truncate p-4 text-black data-[focus]:bg-[#E4EEFF] data-[focus]:outline-none",
        className,
      )}
    >
      {children}
    </ListboxOption>
  );
};

const ListBoxOptionLabel = ({ label }: { label: string }) => {
  return (
    <span className="flex justify-between truncate">
      <Text
        tag="span"
        style="b1"
        className="group-data-[selected]:max-w-[95%] group-data-[selected]:truncate"
      >
        {label}
      </Text>
      <CheckIcon className="hidden size-5 group-data-[selected]:block" />
    </span>
  );
};

export default DropdownField;
