import { Theme } from "@/common/types";
import InputFieldset from "../InputFieldset";
import { Field } from "formik";
import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";

interface Props {
  label: string;
  nameOfField: string;
  error?: string;
  touched?: boolean;
  theme: Theme;
  className?: string;
  secondaryLabel?: string;
  value: boolean | string;
  formId: string;
  style?: "default" | "green" | "maroon";
  renderMarkdown?: boolean;
}

const checkboxVariants = cva(
  "focus:outline-none focus:ring-0 focus:ring-offset-0 border-grey-100",
  {
    variants: {
      style: {
        default:
          "checked:bg-blue-500 checked:hover:bg-blue-500 checked:focus:bg-blue-500 checked:text-white rounded-sm border-2 size-4",
        green:
          "size-5 rounded border transition duration-300 ease-in-out checked:bg-green-700 checked:hover:bg-green-700 checked:focus:bg-green-700 text-blue-700 accent-blue-700 checked:text-blue-700",
        maroon:
          "size-5 rounded  border-grays-G7 transition duration-300 ease-in-out checked:bg-primary01-75 checked:hover:bg-primary01-75 checked:focus:bg-primary01-75 text-primary01-75 accent-primary01-75 checked:text-primary01-75",
      },
    },
  },
);

const CheckboxField = (props: Props) => {
  const {
    label,
    nameOfField,
    error,
    theme,
    touched,
    formId,
    style = "default",
    className,
    renderMarkdown,
  } = props;

  const hasValidationError = Boolean(touched && error);
  return (
    <>
      <InputFieldset
        label={label}
        theme={theme}
        nameOfField={nameOfField + formId}
        hasValidationError={hasValidationError}
        errorMessage={error}
        size="inline"
        flow="reverse"
        errorMessageIsIndependent={true}
        renderMarkdown={renderMarkdown}
        className="relative"
        isCheckbox={true}
      >
        <Field
          type="checkbox"
          id={nameOfField + formId}
          name={nameOfField}
          className={cn(
            checkboxVariants({ style }),
            className,
            "accent-grey-100",
          )}
        />
      </InputFieldset>
    </>
  );
};

export default CheckboxField;
