import React from "react";
import { useFormikContext } from "formik";
import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";
import { Theme } from "@/common/types";
import Text from "@/components/ui/Text";

interface RectRadioFieldProps {
  name: string;
  value: string;
  label: string;
  theme: Theme;
  className?: string;
  disabled?: boolean;
}

const RectRadioField: React.FC<RectRadioFieldProps> = ({
  name,
  value,
  label,
  theme,
  className,
  disabled = false,
}) => {
  const { values, setFieldValue } = useFormikContext<Record<string, any>>();

  const isChecked = values[name] === value;

  const handleChange = async () => {
    if (!disabled) {
      await setFieldValue(name, value);
    }
  };

  const buttonVariants = cva(
    "relative w-full h-[5.75rem] rounded-lg border transition-all duration-200 cursor-pointer flex items-center justify-center",
    {
      variants: {
        theme: {
          light: [
            "border-[#D8D8D8] bg-white text-gray-700",
            "data-[checked=true]:border-primary01-75 data-[checked=true]:bg-primary01-75 data-[checked=true]:text-white",
            "hover:border-primary01-75",
            "data-[checked=true]:hover:border-primary01-100 data-[checked=true]:hover:bg-primary01-100",
            "disabled:border-gray-300 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed",
          ],
          dark: [
            "border-gray-600 bg-gray-800 text-gray-300",
            "data-[checked=true]:border-blue-500 data-[checked=true]:bg-blue-500 data-[checked=true]:text-white",
            "hover:border-blue-400",
            "data-[checked=true]:hover:border-blue-400 data-[checked=true]:hover:bg-blue-400",
            "disabled:border-gray-700 disabled:bg-gray-900 disabled:text-gray-600 disabled:cursor-not-allowed",
          ],
        },
      },
      defaultVariants: {
        theme: "light",
      },
    },
  );

  const textVariants = cva("font-medium transition-colors duration-200", {
    variants: {
      theme: {
        light: ["text-gray-700", "group-data-[checked=true]:text-white"],
        dark: ["text-gray-300", "group-data-[checked=true]:text-white"],
      },
    },
    defaultVariants: {
      theme: "light",
    },
  });

  return (
    <div className={cn("flex-1", className)}>
      <input
        type="radio"
        id={`${name}-${value}`}
        name={name}
        value={value}
        checked={isChecked}
        onChange={handleChange}
        disabled={disabled}
        className="sr-only"
      />
      <label
        htmlFor={`${name}-${value}`}
        className={cn(buttonVariants({ theme }), "group")}
        data-checked={isChecked}
        role="button"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={async (e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            await handleChange();
          }
        }}
        onClick={async () => {
          await handleChange();
        }}
        aria-pressed={isChecked}
        aria-disabled={disabled}
      >
        <Text tag="span" style="sh3" className={cn(textVariants({ theme }))}>
          {label}
        </Text>
      </label>
    </div>
  );
};

export default RectRadioField;
