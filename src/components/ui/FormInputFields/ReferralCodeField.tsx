"use client";

import React, { Suspense, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import TextField from "./TextField";
import { Theme } from "@/common/types";
import Skeleton from "../Skeleton";

type Props = {
  label: string;
  placeholder: string;
  nameOfField: string;
  setFieldTouched: () => null;
  setFieldValue: (field: string, value: string) => null;
  error?: string;
  touched?: boolean;
  value: string;
  theme: Theme;
};

const ReferralCodeFieldPlaceholder = () => {
  return <Skeleton className="h-10 w-full" />;
};

const ReferralCodeFieldContent = (props: Props) => {
  const {
    label,
    placeholder,
    nameOfField,
    theme,
    setFieldValue,
    touched,
    error,
    value,
  } = props;

  const searchParams = useSearchParams();
  const referralCodeQueryParam = searchParams.get("utm_content");

  const validateReferralCode = (referralCode: string) => {
    const referralCodePattern = /^CRMSN-[A-Za-z0-9]{9,}$/;
    const trimmedReferralCode = referralCode.trim();

    if (referralCodePattern.test(trimmedReferralCode)) {
      return true;
    }

    return false;
  };

  useEffect(() => {
    if (
      referralCodeQueryParam &&
      validateReferralCode(referralCodeQueryParam)
    ) {
      setFieldValue(nameOfField, referralCodeQueryParam);
    }
  }, [setFieldValue, nameOfField, referralCodeQueryParam]);

  // Only allow read only when the value comes from the query param
  const allowReadOnly =
    referralCodeQueryParam && validateReferralCode(referralCodeQueryParam);

  return (
    <div>
      <TextField
        label={label}
        placeholder={placeholder}
        nameOfField={nameOfField}
        theme={theme}
        touched={touched}
        value={value}
        error={error}
        readOnly={Boolean(!!value && !error && allowReadOnly)}
      />
    </div>
  );
};

const ReferralCodeField = (props: Props) => {
  return (
    <Suspense fallback={<ReferralCodeFieldPlaceholder />}>
      <ReferralCodeFieldContent {...props} />
    </Suspense>
  );
};

export default ReferralCodeField;
