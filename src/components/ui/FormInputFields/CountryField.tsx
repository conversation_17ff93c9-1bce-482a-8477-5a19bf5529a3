import React, { useRef, useEffect, useMemo, useCallback } from "react";
import { FormikErrors, useFormikContext } from "formik";
import COUNTRIES from "@/common/data/full_countries.json";
import Text from "@/components/ui/Text";
import <PERSON>completeField from "./AutocompleteField";
import { Theme } from "@/common/types";
import { IFormValues } from "../Forms/types";
import useUserLocation from "./hooks/useUserLocation";
import { cn } from "@/common/utils";

interface Props {
  label: string;
  language: keyof CountryCode;
  nameOfField: string;
  setFieldValue: (
    field: string,
    value: any,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
  theme: Theme;
  placeholder?: string;
  validation?: string[];
  touched?:
    | boolean
    | Record<string, string>
    | Record<string, boolean | undefined>;
  error?: string | Record<string, string>;
  formId?: string;
}

interface CountryCode {
  salesforce: string;
  name: string;
  en: string;
  es: string;
  ru: string;
  "zh-cn": string;
  "zh-tw": string;
  "dial-code": string;
  "alpha-2": string;
  pt: string;
}

const CountryField = (props: Props) => {
  const {
    label,
    language = "en",
    placeholder,
    nameOfField,
    setFieldValue,
    theme,
    error = "",
    validation,
    touched = false,
    formId,
  } = props;

  const hasRun = useRef(false);
  const { userCountry, alternateName, isLoading } = useUserLocation();

  const handleOnChange = useCallback(
    (selectedValue: { value: string }) =>
      setFieldValue(nameOfField, selectedValue?.value || ""),
    [setFieldValue, nameOfField],
  );

  const { values } = useFormikContext<IFormValues>();

  const selectedValue =
    values[nameOfField] ?? values?.primary_language__c ?? "";

  const countryFieldOptions = useMemo(
    () =>
      COUNTRIES.map((country) => ({
        label: `${country[language] || country.en}`,
        value: country.salesforce,
        alpha2: country["alpha-2"],
      })).sort((a, b) => {
        if (a.label < b.label) return -1;
        if (a.label > b.label) return 1;
        return 0;
      }),
    [language],
  );

  const parsedSelectedValue = countryFieldOptions.find(
    (option) =>
      option.value === selectedValue || option.label === selectedValue,
  );

  useEffect(() => {
    if (!hasRun.current && userCountry && !selectedValue && !isLoading) {
      const selectedCountryByLabel = countryFieldOptions.find(
        (option) => option.label === userCountry,
      );
      const countryToSet =
        selectedCountryByLabel?.value ?? userCountry ?? alternateName;

      if (countryToSet) {
        void handleOnChange({
          value: countryToSet,
        });
      }

      hasRun.current = true;
    }
  }, [
    userCountry,
    alternateName,
    selectedValue,
    isLoading,
    countryFieldOptions,
    handleOnChange,
  ]);

  useEffect(() => {
    if (selectedValue && hasRun.current) {
      if (!selectedValue) {
        hasRun.current = false;
      }
    }
  }, [selectedValue]);

  return (
    <AutocompleteField
      listLength={countryFieldOptions?.length}
      label={label}
      nameOfField={nameOfField}
      options={countryFieldOptions}
      theme={theme}
      error={error}
      touched={touched}
      validation={validation}
      handleOnChange={(selectedValue: { value: string }) =>
        handleOnChange(selectedValue)
      }
      isClearable={true}
      placeholder={placeholder}
      value={parsedSelectedValue}
      formId={formId}
      isDisabled={isLoading}
      isLoading={isLoading}
      formatOptionLabel={(
        option,
        labelMeta: {
          context: string;
        },
      ) => {
        const countryOption = option as unknown as {
          alpha2: string;
          label: string;
        };
        const context = labelMeta?.context;
        if (context === "value") {
          return (
            <div className="flex items-center">
              <Text tag="span" style="b1" className="mr-1 text-b1">
                {countryOption.label}
              </Text>
            </div>
          );
        }
        return (
          <div className="flex items-center">
            <div
              className={cn(
                "fi mr-2 size-[1.25rem]",
                `fi-${countryOption?.alpha2?.toLowerCase()}`,
              )}
            ></div>
            <Text tag="span" style="b1" className="mr-1">
              {countryOption.label}
            </Text>
          </div>
        );
      }}
    />
  );
};

export default CountryField;
