import { Theme } from "@/common/types";
import TextField from "@/components/ui/FormInputFields/TextField";
import { usePageContext } from "@/components/context/PageContext";

interface Props {
  studentField: {
    label: string;
    marketoField: string;
  };
  guardianField: {
    label: string;
    marketoField: string;
  };
  guardianPlaceholder: string;
  studentPlaceholder: string;
  values: Record<string, any>;
  touched: Record<string, boolean>;
  errors: Record<string, string>;
  theme: Theme;
}

const ConditionalTextField = (props: Props) => {
  const {
    studentPlaceholder,
    studentField,
    guardianField,
    guardianPlaceholder,
    values,
    touched,
    errors,
    theme,
  } = props;
  const { isVisualComposer } = usePageContext();

  const { person_type__c } = values;
  const { label: studentLabel, marketoField: studentFieldName } = studentField;
  const { label: guardianLabel, marketoField: guardianFieldName } =
    guardianField;

  const nameOfField =
    person_type__c === "Student" ? guardianFieldName : studentFieldName;

  const fieldLabel =
    person_type__c === "Student" ? guardianLabel : studentLabel;
  const fieldPlaceholder =
    person_type__c === "Student" ? guardianPlaceholder : studentPlaceholder;

  if (isVisualComposer || person_type__c) {
    return (
      <div>
        <TextField
          label={fieldLabel}
          placeholder={fieldPlaceholder}
          nameOfField={nameOfField}
          theme={theme}
          touched={touched[nameOfField]}
          value={values[nameOfField]}
          error={errors[nameOfField]}
        />
      </div>
    );
  }

  return null;
};

export default ConditionalTextField;
