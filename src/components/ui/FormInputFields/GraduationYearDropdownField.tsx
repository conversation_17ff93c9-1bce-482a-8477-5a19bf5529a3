import { Theme } from "@/common/types";
import DropdownField from "./DropdownField";
import { useMemo } from "react";

interface Props {
  year: string;
  label?: string;
  nameOfField: string;
  placeholder?: string;
  error?: string;
  touched?: boolean;
  theme: Theme;
  className?: string;
  secondaryLabel?: string;
  flatOptionsList?: string[];
  formId?: string;
  hideLabel?: boolean;
  hideErrorMessage?: boolean;
  alreadyGraduatedLabel: string;
}

const FALLBACK_YEAR = new Date().getFullYear();
const FALLBACK_FUTURE_YEAR = FALLBACK_YEAR + 15;

const GraduationYearDropdownField = ({
  year,
  alreadyGraduatedLabel,
  ...props
}: Props) => {
  const [startYear, targetYear] = year?.split("-")?.map(Number) ?? [];
  const [RANGE_START, RANGE_END] = [
    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
    startYear || FALLBACK_YEAR,
    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
    targetYear || FALLBACK_FUTURE_YEAR,
  ];

  const range = useMemo(() => {
    const options = Array(Math.abs(RANGE_END - RANGE_START + 1))
      .fill(null)
      .reduce((accum, _, index) => {
        const current = RANGE_START + index;
        return {
          ...accum,
          [`option_${index + 1}`]: {
            label: current,
            marketoField: current,
            _uuid: `${RANGE_START}-${index}`,
          },
        };
      }, {});

    return {
      ...options,
      [`option_${RANGE_END}`]: {
        label: alreadyGraduatedLabel || "Already Graduated",
        marketoField: 1900,
        _uuid: RANGE_END,
      },
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return <DropdownField {...props} {...range} />;
};

export default GraduationYearDropdownField;
