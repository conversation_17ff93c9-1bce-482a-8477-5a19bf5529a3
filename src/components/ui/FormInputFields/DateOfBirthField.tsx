import { Theme } from "@/common/types";
import { FormikErrors, FormikTouched } from "formik";
import { IFormValues } from "../Forms/types";
import InputFieldset from "../InputFieldset";
import DropdownField from "./DropdownField";

const arrayOfDays = [...Array(31).keys()].map((v) => (v + 1).toString());
const arrayOfMonths = [...Array(12).keys()].map((v) => (v + 1).toString());
const thisYear = new Date().getFullYear();
const arrayOfYears = [...Array(100).keys()]
  .map((v) => (v + thisYear - 99).toString())
  .reverse();

interface Props {
  label: string;
  values: IFormValues;
  setFieldTouched: (
    field: string,
    isTouched?: boolean,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
  setFieldValue: (
    field: string,
    value: any,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
  touched?: FormikTouched<IFormValues>;
  errors?: FormikErrors<IFormValues>;
  theme: Theme;
  birthDayFieldName: string;
  birthMonthFieldName: string;
  birthYearFieldName: string;
  formId?: string;
}
const DateOfBirthField = (props: Props) => {
  const {
    label,
    touched,
    errors,
    theme,
    birthDayFieldName,
    birthMonthFieldName,
    birthYearFieldName,
    formId,
  } = props;

  const dobErrorMessage = () => {
    if (touched && errors) {
      return (
        (touched[birthDayFieldName] && errors[birthDayFieldName]) ??
        (touched[birthMonthFieldName] && errors[birthMonthFieldName]) ??
        (touched[birthYearFieldName] && errors[birthYearFieldName])
      );
    }
  };

  return (
    <InputFieldset
      label={label}
      theme={theme}
      hasValidationError={!!dobErrorMessage()}
      errorMessage={dobErrorMessage() as string}
      className="pb-0"
    >
      <div className="flex justify-between gap-3">
        <DropdownField
          error={errors?.[birthDayFieldName]}
          touched={touched?.[birthDayFieldName]}
          nameOfField={birthDayFieldName}
          hideLabel={true}
          theme={theme}
          flatOptionsList={arrayOfDays}
          placeholder="DD"
          className="w-full"
          formId={formId}
          hideErrorMessage={true}
        />
        <DropdownField
          error={errors?.[birthMonthFieldName]}
          touched={touched?.[birthMonthFieldName]}
          nameOfField={birthMonthFieldName}
          hideLabel={true}
          theme={theme}
          flatOptionsList={arrayOfMonths}
          placeholder="MM"
          className="w-full"
          formId={formId}
          hideErrorMessage={true}
        />
        <DropdownField
          error={errors?.[birthYearFieldName]}
          touched={touched?.[birthYearFieldName]}
          nameOfField={birthYearFieldName}
          hideLabel={true}
          theme={theme}
          flatOptionsList={arrayOfYears}
          placeholder="YYYY"
          className="w-full"
          formId={formId}
          hideErrorMessage={true}
        />
      </div>
    </InputFieldset>
  );
};

export default DateOfBirthField;
