import React from "react";
import { useFormikContext, getIn } from "formik";
import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";
import { Theme } from "@/common/types";
import Text from "@/components/ui/Text";

interface RadioFieldProps {
  name: string;
  value: string;
  label: string;
  theme: Theme;
  className?: string;
  disabled?: boolean;
}

const RadioField: React.FC<RadioFieldProps> = ({
  name,
  value,
  label,
  theme,
  className,
  disabled = false,
}) => {
  const { values, setFieldValue } = useFormikContext<Record<string, any>>();

  const currentValue = getIn(values, name);
  const isChecked = currentValue === value;

  const handleChange = async () => {
    if (!disabled) {
      await setFieldValue(name, value);
    }
  };

  const radioVariants = cva(
    "relative size-4 rounded-full border-2 transition-all duration-200 cursor-pointer",
    {
      variants: {
        theme: {
          light: [
            "border-grays-G7",
            "hover:border-primary01-75",
            "disabled:border-grays-G6 disabled:bg-grays-G8 disabled:cursor-not-allowed",
          ],
          dark: [
            "border-blue-400",
            "hover:border-primary01-75",
            "disabled:border-blue-600 disabled:bg-blue-700 disabled:cursor-not-allowed",
          ],
        },
      },
      defaultVariants: {
        theme: "light",
      },
    },
  );

  const labelVariants = cva(
    "font-medium transition-colors duration-200 cursor-pointer select-none",
    {
      variants: {
        theme: {
          light: [
            "text-blue-700",
            "hover:text-blue-800",
            "disabled:text-grays-G6 disabled:cursor-not-allowed",
          ],
          dark: [
            "text-white",
            "hover:text-blue-100",
            "disabled:text-blue-400 disabled:cursor-not-allowed",
          ],
        },
      },
      defaultVariants: {
        theme: "light",
      },
    },
  );

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <div className="relative">
        <input
          type="radio"
          id={`${name}-${value}`}
          name={name}
          value={value}
          checked={isChecked}
          onChange={handleChange}
          disabled={disabled}
          className="sr-only"
        />
        <div
          className={cn(
            radioVariants({ theme }),
            isChecked && "border-primary01-75",
            disabled && "opacity-50",
          )}
          onClick={handleChange}
        >
          {isChecked && (
            <div
              className={cn(
                "absolute left-1/2 top-1/2 size-2 -translate-x-1/2 -translate-y-1/2 transform rounded-full",
                theme === "light" ? "bg-primary01-75" : "bg-primary01-75",
              )}
            />
          )}
        </div>
      </div>
      <Text
        tag="label"
        style="b1"
        htmlFor={`${name}-${value}`}
        className={cn(labelVariants({ theme }), disabled && "opacity-50")}
      >
        <div onClick={handleChange}>{label}</div>
      </Text>
    </div>
  );
};

export default RadioField;
