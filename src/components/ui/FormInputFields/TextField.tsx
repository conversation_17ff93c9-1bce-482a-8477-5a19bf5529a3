import React, { useEffect } from "react";
import InputFieldset from "../InputFieldset";
import { Theme } from "@/common/types";
import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";
import { Field } from "formik";
import RedTriangleWarning from "@/components/icons/RedTriangleWarning";

type Props = {
  label: string;
  nameOfField: string;
  placeholder?: string;
  renderAsTextArea?: boolean;
  error?: string;
  touched?: boolean;
  theme: Theme;
  className?: string;
  value?: string;
  readOnly?: boolean;
  disabled?: boolean;
  type?: string;
};

function setupNumberInputScrollPrevention() {
  const preventWheelOnNumberInputs = (e: WheelEvent) => {
    const target = e.target as HTMLElement;
    if (
      target.tagName === "INPUT" &&
      (target as HTMLInputElement).type === "number"
    ) {
      e.preventDefault();
    }
  };

  const preventTouchOnNumberInputs = (e: TouchEvent) => {
    const target = document.activeElement as HTMLElement;
    if (
      target &&
      target.tagName === "INPUT" &&
      (target as HTMLInputElement).type === "number"
    ) {
      e.stopPropagation();
    }
  };
  document.addEventListener("wheel", preventWheelOnNumberInputs, {
    passive: false,
  });
  document.addEventListener("touchstart", preventTouchOnNumberInputs, {
    passive: false,
  });
  document.addEventListener("touchmove", preventTouchOnNumberInputs, {
    passive: false,
  });

  return () => {
    document.removeEventListener("wheel", preventWheelOnNumberInputs);
    document.removeEventListener("touchstart", preventTouchOnNumberInputs);
    document.removeEventListener("touchmove", preventTouchOnNumberInputs);
  };
}

const textFieldVariants = cva(
  "w-full rounded-lg border border-grays-G7 p-[0.9rem]  placeholder:font-body-single transition-all duration-75 focus:ring-0 focus:border-secondary01-50font-body-p placeholder:text-b1 [&::placeholder]:!text-grays-G5",
  {
    variants: {
      theme: {
        light: "!bg-white text-neutral01-100 autofill-light text-b1 ",
        dark: "!bg-blue-600 text-white autofill-dark",
      },
      errors: {
        true: "border-primary01-50 focus:border-primary01-50 focus:shadow-[0_0_0_1px_theme(colors.primary01.50)]",
        false:
          "focus:border-secondary01-50 focus:shadow-[0_0_0_1px_theme(colors.secondary01.50)]",
      },
    },
    defaultVariants: {
      theme: "light",
      errors: false,
    },
  },
);

const TextField = (props: Props) => {
  const {
    label,
    nameOfField,
    placeholder,
    error,
    touched,
    theme,
    className,
    readOnly,
    disabled,
    type = "text",
  } = props;

  const hasValidationError = Boolean(touched && error);

  useEffect(() => {
    const cleanup = setupNumberInputScrollPrevention();
    return cleanup;
  }, []);

  return (
    <InputFieldset
      label={label}
      theme={theme}
      hasValidationError={hasValidationError}
      errorMessage={error}
      nameOfField={nameOfField}
      className="relative"
    >
      <Field
        as="input"
        type={type}
        name={nameOfField}
        id={nameOfField}
        placeholder={placeholder}
        className={cn(
          textFieldVariants({ theme, errors: hasValidationError }),
          className,
        )}
        readOnly={readOnly}
        disabled={disabled}
        min={type === "number" ? "0" : undefined}
        step={type === "number" ? "1" : undefined}
      />
      {hasValidationError && (
        <RedTriangleWarning
          className={cn(
            "absolute bottom-4 right-2",
            type === "text" ? "lg:right-2" : "lg:right-9",
          )}
        />
      )}
    </InputFieldset>
  );
};

export default TextField;
