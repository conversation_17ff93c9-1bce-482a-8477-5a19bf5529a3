import React from "react";
import InputFieldset from "../InputFieldset";
import { Field } from "formik";
import { Theme } from "@/common/types";
import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";
import Text from "../Text";

type Props = {
  maxCharacters?: number;
  label: string;
  nameOfField: string;
  placeholder?: string;
  error?: string;
  touched?: boolean;
  theme: Theme;
  value?: string;
  className?: string;
};

const textAreaFieldVariants = cva(
  "w-full rounded-lg border border-grays-G7 p-[0.9rem] [&::placeholder]:!text-grays-G5 placeholder:text-b1 placeholder:font-body-single !text-b1 font-body-p transition-all duration-75 focus:ring-0 focus:border-secondary01-50 h-auto resize-none",
  {
    variants: {
      theme: {
        light: "!bg-white text-neutral01-100 autofill-light",
        dark: "!bg-blue-600 text-white autofill-dark",
      },
      errors: {
        true: "border-primary01-50 focus:border-primary01-50 focus:shadow-[0_0_0_1px_theme(colors.primary01.50)]",
        false:
          "focus:border-secondary01-50 focus:shadow-[0_0_0_1px_theme(colors.secondary01.50)]",
      },
    },
    defaultVariants: {
      theme: "light",
      errors: false,
    },
  },
);

export default function TextAreaField({
  maxCharacters = 225,
  label,
  nameOfField,
  placeholder,
  error,
  touched,
  theme,
  value,
  className,
}: Props) {
  const hasValidationError = Boolean(touched && error);

  return (
    <InputFieldset
      label={label}
      theme={theme}
      hasValidationError={hasValidationError}
      errorMessage={error}
      nameOfField={nameOfField}
      className="relative"
    >
      <Field
        as="textarea"
        name={nameOfField}
        placeholder={placeholder}
        className={cn(
          textAreaFieldVariants({ theme, errors: hasValidationError }),
          className,
        )}
        rows={4}
        maxLength={maxCharacters}
      />
      {maxCharacters && (
        <Text tag="p" style="b3" className="pt-2 text-right text-grays-G4">
          {value?.length ?? 0}/{maxCharacters}
        </Text>
      )}
    </InputFieldset>
  );
}
