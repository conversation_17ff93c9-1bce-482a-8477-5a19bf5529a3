import { IAutocompleteOption, Theme } from "@/common/types";
import { LANGUAGE_FIELD_OPTIONS } from "../constants";
import { type FormikErrors, useFormikContext } from "formik";
import { IFormValues } from "../../Forms/types";
import AutocompleteField from "../AutocompleteField";

interface Props {
  label: string;
  nameOfField: string;
  setFieldValue: (
    field: string,
    value: any,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
  theme: Theme;
  placeholder?: string;
  validation?: string[];
  touched?:
    | boolean
    | Record<string, string>
    | Record<string, boolean | undefined>;
  error?: string | Record<string, string>;
  formId?: string;
}

const LanguageField = (props: Props) => {
  const {
    label,
    nameOfField,
    setFieldValue,
    theme,
    placeholder,
    touched,
    error,
    formId,
  } = props;

  const { values } = useFormikContext<IFormValues>();

  const selectedValue = values?.primary_language__c ?? "";

  const parsedSelectedValue = LANGUAGE_FIELD_OPTIONS.find(
    (option) => option.value === selectedValue,
  );
  return (
    <AutocompleteField
      listLength={LANGUAGE_FIELD_OPTIONS.length}
      label={label}
      nameOfField={nameOfField}
      options={LANGUAGE_FIELD_OPTIONS}
      theme={theme}
      error={error}
      touched={touched}
      handleOnChange={(selectedValue: IAutocompleteOption) => {
        setFieldValue(nameOfField, selectedValue?.value || "").catch(
          console.error,
        );
      }}
      isClearable={true}
      placeholder={placeholder}
      value={parsedSelectedValue}
      formId={formId}
    />
  );
};

export default LanguageField;
