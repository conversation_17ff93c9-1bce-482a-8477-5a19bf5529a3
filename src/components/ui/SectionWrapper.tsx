import { cn, httpsPath } from "@/common/utils";
import type { Theme, TBackgroundColour } from "@/common/types";
import ImageWrapper from "./ImageWrapper";
import { cva } from "class-variance-authority";

interface Props {
  backgroundImage?: string;
  backgroundColour: TBackgroundColour;
  theme: Theme;
  flipBackgroundImage?: boolean;
  anchorId?: string;
  children: React.ReactNode;
  className?: string;
}

const sectionVariants = cva("relative", {
  variants: {
    backgroundColour: {
      navy: "bg-blue-700",
      charcoal: "bg-blue-900",
      indigo: "bg-blue-500",
      mulberry: "bg-maroon-700",
      salmon: "bg-red-300",
      white: "bg-white",
      yellow: "bg-yellow-500",
      whisper: "bg-grey-100",
      mist: "bg-grey-50",
    },
    theme: {
      light: "text-blue-700",
      dark: "text-white",
    },
  },
});

/**
 * @deprecated - Container can be used inside of components instead
 */
const SectionWrapper = ({
  backgroundColour,
  backgroundImage,
  flipBackgroundImage,
  theme,
  anchorId,
  children,
  className,
}: Props) => {
  const imagePath = httpsPath(backgroundImage);

  return (
    <section
      className={cn(sectionVariants({ backgroundColour, theme }), className)}
    >
      {anchorId && (
        <a
          className="invisible relative -top-20 block md:-top-24 lg:-top-36"
          id={anchorId}
        ></a>
      )}
      {imagePath && (
        <div className="absolute inset-0 flex overflow-hidden">
          <ImageWrapper
            flip={flipBackgroundImage}
            src={imagePath}
            width={1080}
            height={400}
            fit="cover"
            className="size-full"
            sizes="100vw"
          />
        </div>
      )}
      <div className="relative mx-auto w-full">{children}</div>
    </section>
  );
};

export default SectionWrapper;
