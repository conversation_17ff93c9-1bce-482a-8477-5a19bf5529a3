import Text from "@/components/ui/Text";

interface Props {
  children: React.ReactNode;
  isIndependent?: boolean;
  className?: string;
}

const FormErrorMessage = (props: Props) => {
  const { children } = props;
  return (
    <div className="mt-[0.3125rem]">
      <Text
        tag="span"
        style="b2"
        className="font-medium italic text-primary01-50"
      >
        {children}
      </Text>
    </div>
  );
};

export default FormErrorMessage;
