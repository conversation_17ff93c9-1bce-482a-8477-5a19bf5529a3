import React from "react";
import Link from "next/link";
import Image from "next/image";
import Text from "@/components/ui/Text";
import { cn } from "@/common/utils";

export type ServiceCardType = {
  id: number;
  title: string;
  image: any;
  link: string;
  cta: string;
};

interface ServiceCardProps {
  card: ServiceCardType;
  width?: number;
}

const ServiceCard = ({ card, width }: ServiceCardProps) => {
  const cardClassName = cn(
    "flex flex-col rounded bg-white shadow-[0px_0px_17.52px_rgba(86,65,46,0.1)] transition-shadow hover:shadow-[0px_5px_25px_0px_rgba(0,0,0,0.25)] h-[380px]",
    width
      ? `w-[${width}px] min-w-[${width}px] max-w-[${width}px] flex-shrink-0`
      : "w-full"
  );

  return (
    <Link href={card.link} className={cn("group", !width && "block w-full")}>
      <div className={cardClassName}>
        <div className="relative h-48 overflow-hidden rounded-t">
          <Image
            src={card.image}
            alt={card.title}
            fill
            className="object-cover"
          />
        </div>
        <div className="flex flex-1 flex-col justify-between px-6 pt-8 pb-6">
          <div>
            <Text
              tag="h3"
              style="h4"
              className={cn("mb-4 text-grays-G1", width && "max-w-60")}
            >
              {card.title}
            </Text>
          </div>
          <Text
            tag="span"
            style="button"
            className="transition-colors text-primary01-75 group-hover:text-primary01-50"
          >
            {card.cta}
          </Text>
        </div>
      </div>
    </Link>
  );
};

export default ServiceCard;
