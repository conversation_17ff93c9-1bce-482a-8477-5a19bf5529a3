import type { Theme } from "@/common/types";
import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";

interface Props {
  theme?: Theme;
  children: React.ReactNode;
  className?: string;
  tag?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p" | "span" | "label";
  style?:
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "body1"
  | "body2"
  | "body3"
  | "breadcrumb"
  | "caption";
  htmlFor?: string;
}

const typographyVariants = cva("whitespace-pre-line", {
  variants: {
    theme: {
      light: "text-blue-700",
      dark: "text-white",
    },
    style: {
      h1: "text-h1 font-bold",
      h2: "text-h2 font-bold",
      h3: "text-h3 font-medium",
      h4: "text-h4 font-bold",
      h5: "text-h5 font-bold",
      h6: "text-h6 font-bold",
      body1: "text-body1 font-medium",
      body2: "text-body2 font-medium",
      body3: "text-body3 font-normal",
      breadcrumb: "text-breadcrumb font-medium",
      caption: "text-caption font-medium",
    },
  },
  defaultVariants: {
    theme: "light",
    style: "body1",
  },
});

/**
 * @deprecated Use the `Text` component instead
 */
const Typography = ({
  tag,
  children,
  className,
  style = "body1",
  theme = "light",
  ...rest
}: Props) => {
  const Component = tag ?? "p";
  return (
    <Component
      className={cn(typographyVariants({ style, theme }), className)}
      {...rest}
    >
      {children}
    </Component>
  );
};

export default Typography;
