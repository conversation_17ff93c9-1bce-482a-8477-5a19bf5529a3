import { cva } from "class-variance-authority";

import { cn } from "@/common/utils";

const alertVariants = cva(
  "flex items-center gap-[0.63rem] rounded-md px-4 py-2 text-base font-semibold border-l-4 rounded-r",
  {
    variants: {
      theme: {
        success: "bg-green-300 text-green-900 border-green-900",
        warning: "bg-yellow-300 text-yellow-900 border-yellow-900",
        error: "bg-red-50 text-red-500 border-red-500",
        info: "bg-blue-50 text-blue-500 border-blue-500",
      },
    },
    defaultVariants: {
      theme: "success",
    },
  },
);

interface Props {
  children: React.ReactNode;
  theme: "success" | "warning" | "error" | "info";
  className?: string;
}

const Alert = ({ theme, className, ...props }: Props) => {
  return <div className={cn(alertVariants({ theme }), className)} {...props} />;
};

export default Alert;
