import { xxlClasses } from "./responsiveStyles";
import { xlClasses } from "./responsiveStyles";
import { lgClasses } from "./responsiveStyles";
import { mdClasses } from "./responsiveStyles";
import { styleClasses } from "./responsiveStyles";

export type TStyle =
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "sh1"
  | "sh2"
  | "sh3"
  | "sh4"
  | "sh5"
  | "sh6"
  | "sh7"
  | "ph1"
  | "ph2"
  | "q1"
  | "q2"
  | "q3"
  | "q4"
  | "q5"
  | "q6"
  | "n1"
  | "n2"
  | "a1"
  | "a2"
  | "b1"
  | "b2"
  | "b3"
  | "b4"
  | "b5"
  | "c1"
  | "button"
  | "t1"
  | "t2"
  | "t3"
  | "hw1"
  | "hw2"
  | "hw3"
  | "hw4"
  | "hw5"
  | "mh1"
  | "mh1.5"
  | "mb1"
  | "mph1";

export type TTag =
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "p"
  | "span"
  | "label"
  | "li"
  | "blockquote";

interface Props {
  children: React.ReactNode;
  className?: string;
  tag: TTag;
  style: TStyle;
  /**
   * @optional Style to apply at medium screen sizes (MD breakpoint)
   */
  mdStyle?: TStyle;
  /**
   * @optional Style to apply at large screen sizes (LG breakpoint)
   */
  lgStyle?: TStyle;
  /**
   * @optional Style to apply at extra large screen sizes (XL breakpoint)
   */
  xlStyle?: TStyle;
  /**
   * @optional Style to apply at 2XL screen sizes (2XL breakpoint)
   */
  xxlStyle?: TStyle;
  htmlFor?: string;
}

const Text = ({
  tag,
  children,
  className,
  style = "b1",
  mdStyle,
  lgStyle,
  xlStyle,
  xxlStyle,
  ...rest
}: Props) => {
  const Component = tag ?? "p";

  const baseClasses = [
    "whitespace-pre-line",
    ...(styleClasses[style]?.split(" ") || []),
    mdStyle && mdClasses[mdStyle]?.split(" "),
    lgStyle && lgClasses[lgStyle]?.split(" "),
    xlStyle && xlClasses[xlStyle]?.split(" "),
    xxlStyle && xxlClasses[xxlStyle]?.split(" "),
  ]
    .filter(Boolean)
    .flat();

  /**
   * When using Tailwind, classes with the same prefix (e.g. 'text-', 'p-')
   * will override each other. By concatenating baseClasses and
   * customClasses separately, we ensure custom classes aren't
   * accidentally overwritten
   */
  const customClasses = className ? className.split(" ") : [];
  const allClasses = [...baseClasses, ...customClasses];

  return (
    <Component className={allClasses.join(" ")} {...rest}>
      {children}
    </Component>
  );
};

export default Text;
