// In order to avoid FOUC and using a hook to handle device sizes
// we need to define static class mappings for all breakpoints.
// These must be complete strings that <PERSON><PERSON><PERSON> can detect during build
// Otherwise, tailwind will not apply the correct classes to the text.

export const styleClasses = {
  h1: "font-display-sans text-h1",
  h2: "font-display-sans text-h2",
  h3: "font-display-sans text-h3",
  h4: "font-display-sans text-h4",
  h5: "font-display-sans text-h5",
  h6: "font-display-sans text-h6",
  sh1: "font-display-serif text-sh1",
  sh2: "font-display-serif text-sh2",
  sh3: "font-display-serif text-sh3",
  sh4: "font-display-serif text-sh4",
  sh5: "font-display-serif text-sh5",
  sh6: "font-display-serif text-sh6",
  sh7: "font-display-serif text-sh7",
  ph1: "font-display-serif text-ph1",
  ph2: "font-display-serif text-ph2",
  q1: "font-display-serif text-q1",
  q2: "font-display-serif text-q2",
  q3: "font-display-serif text-q3",
  q4: "font-display-serif text-q4",
  q5: "font-display-serif text-q5",
  q6: "font-display-serif text-q6",
  n1: "font-display-serif text-n1",
  n2: "font-display-serif text-n2",
  a1: "font-display-serif text-a1",
  a2: "font-display-serif text-a2",
  b1: "font-body-p text-b1",
  b2: "font-body-p text-b2",
  b3: "font-body-p text-b3",
  b4: "font-body-p text-b4",
  b5: "font-body-p text-b5",
  c1: "font-body-p text-c1",
  button: "font-display-sans text-button",
  t1: "font-display-sans text-t1",
  t2: "font-display-sans text-t2",
  t3: "font-display-sans text-t3",
  hw1: "font-handwritten text-hw1",
  hw2: "font-handwritten text-hw2",
  hw3: "font-handwritten text-hw3",
  hw4: "font-handwritten text-hw4",
  hw5: "font-handwritten text-hw5",
  mh1: "font-display-sans text-mh1",
  "mh1.5": "font-display-sans text-mh1.5",
  mb1: "font-body-p text-mb1",
  mph1: "font-display-serif text-mph1",
};

// Define responsive variants with the complete classes
// Tailwind needs to see these complete strings in the source
export const mdClasses = {
  h1: "md:font-display-sans md:text-h1",
  h2: "md:font-display-sans md:text-h2",
  h3: "md:font-display-sans md:text-h3",
  h4: "md:font-display-sans md:text-h4",
  h5: "md:font-display-sans md:text-h5",
  h6: "md:font-display-sans md:text-h6",
  sh1: "md:font-display-serif md:text-sh1",
  sh2: "md:font-display-serif md:text-sh2",
  sh3: "md:font-display-serif md:text-sh3",
  sh4: "md:font-display-serif md:text-sh4",
  sh5: "md:font-display-serif md:text-sh5",
  sh6: "md:font-display-serif md:text-sh6",
  sh7: "md:font-display-serif md:text-sh7",
  ph1: "md:font-display-serif md:text-ph1",
  ph2: "md:font-display-serif md:text-ph2",
  q1: "md:font-display-serif md:text-q1",
  q2: "md:font-display-serif md:text-q2",
  q3: "md:font-display-serif md:text-q3",
  q4: "md:font-display-serif md:text-q4",
  q5: "md:font-display-serif md:text-q5",
  q6: "md:font-display-serif md:text-q6",
  n1: "md:font-display-serif md:text-n1",
  n2: "md:font-display-serif md:text-n2",
  a1: "md:font-display-serif md:text-a1",
  a2: "md:font-display-serif md:text-a2",
  b1: "md:font-body-p md:text-b1",
  b2: "md:font-body-p md:text-b2",
  b3: "md:font-body-p md:text-b3",
  b4: "md:font-body-p md:text-b4",
  b5: "md:font-body-p md:text-b5",
  c1: "md:font-body-p md:text-c1",
  button: "md:font-display-sans md:text-button",
  t1: "md:font-display-sans md:text-t1",
  t2: "md:font-display-sans md:text-t2",
  t3: "md:font-display-sans md:text-t3",
  hw1: "md:font-handwritten md:text-hw1",
  hw2: "md:font-handwritten md:text-hw2",
  hw3: "md:font-handwritten md:text-hw3",
  hw4: "md:font-handwritten md:text-hw4",
  hw5: "md:font-handwritten md:text-hw5",
  mh1: "md:font-display-sans md:text-mh1",
  "mh1.5": "md:font-display-sans md:text-mh1.5",
  mb1: "md:font-body-p md:text-mb1",
  mph1: "md:font-display-serif md:text-mph1",
};

export const lgClasses = {
  h1: "lg:font-display-sans lg:text-h1",
  h2: "lg:font-display-sans lg:text-h2",
  h3: "lg:font-display-sans lg:text-h3",
  h4: "lg:font-display-sans lg:text-h4",
  h5: "lg:font-display-sans lg:text-h5",
  h6: "lg:font-display-sans lg:text-h6",
  sh1: "lg:font-display-serif lg:text-sh1",
  sh2: "lg:font-display-serif lg:text-sh2",
  sh3: "lg:font-display-serif lg:text-sh3",
  sh4: "lg:font-display-serif lg:text-sh4",
  sh5: "lg:font-display-serif lg:text-sh5",
  sh6: "lg:font-display-serif lg:text-sh6",
  sh7: "lg:font-display-serif lg:text-sh7",
  ph1: "lg:font-display-serif lg:text-ph1",
  ph2: "lg:font-display-serif lg:text-ph2",
  q1: "lg:font-display-serif lg:text-q1",
  q2: "lg:font-display-serif lg:text-q2",
  q3: "lg:font-display-serif lg:text-q3",
  q4: "lg:font-display-serif lg:text-q4",
  q5: "lg:font-display-serif lg:text-q5",
  q6: "lg:font-display-serif lg:text-q6",
  n1: "lg:font-display-serif lg:text-n1",
  n2: "lg:font-display-serif lg:text-n2",
  a1: "lg:font-display-serif lg:text-a1",
  a2: "lg:font-display-serif lg:text-a2",
  b1: "lg:font-body-p lg:text-b1",
  b2: "lg:font-body-p lg:text-b2",
  b3: "lg:font-body-p lg:text-b3",
  b4: "lg:font-body-p lg:text-b4",
  b5: "lg:font-body-p lg:text-b5",
  c1: "lg:font-body-p lg:text-c1",
  button: "lg:font-display-sans lg:text-button",
  t1: "lg:font-display-sans lg:text-t1",
  t2: "lg:font-display-sans lg:text-t2",
  t3: "lg:font-display-sans lg:text-t3",
  hw1: "lg:font-handwritten lg:text-hw1",
  hw2: "lg:font-handwritten lg:text-hw2",
  hw3: "lg:font-handwritten lg:text-hw3",
  hw4: "lg:font-handwritten lg:text-hw4",
  hw5: "lg:font-handwritten lg:text-hw5",
  mh1: "lg:font-display-sans lg:text-mh1",
  "mh1.5": "lg:font-display-sans lg:text-mh1.5",
  mb1: "lg:font-body-p lg:text-mb1",
  mph1: "lg:font-display-serif lg:text-mph1",
};

export const xlClasses = {
  h1: "xl:font-display-sans xl:text-h1",
  h2: "xl:font-display-sans xl:text-h2",
  h3: "xl:font-display-sans xl:text-h3",
  h4: "xl:font-display-sans xl:text-h4",
  h5: "xl:font-display-sans xl:text-h5",
  h6: "xl:font-display-sans xl:text-h6",
  sh1: "xl:font-display-serif xl:text-sh1",
  sh2: "xl:font-display-serif xl:text-sh2",
  sh3: "xl:font-display-serif xl:text-sh3",
  sh4: "xl:font-display-serif xl:text-sh4",
  sh5: "xl:font-display-serif xl:text-sh5",
  sh6: "xl:font-display-serif xl:text-sh6",
  sh7: "xl:font-display-serif xl:text-sh7",
  ph1: "xl:font-display-serif xl:text-ph1",
  ph2: "xl:font-display-serif xl:text-ph2",
  q1: "xl:font-display-serif xl:text-q1",
  q2: "xl:font-display-serif xl:text-q2",
  q3: "xl:font-display-serif xl:text-q3",
  q4: "xl:font-display-serif xl:text-q4",
  q5: "xl:font-display-serif xl:text-q5",
  q6: "xl:font-display-serif xl:text-q6",
  n1: "xl:font-display-serif xl:text-n1",
  n2: "xl:font-display-serif xl:text-n2",
  a1: "xl:font-display-serif xl:text-a1",
  a2: "xl:font-display-serif xl:text-a2",
  b1: "xl:font-body-p xl:text-b1",
  b2: "xl:font-body-p xl:text-b2",
  b3: "xl:font-body-p xl:text-b3",
  b4: "xl:font-body-p xl:text-b4",
  b5: "xl:font-body-p xl:text-b5",
  c1: "xl:font-body-p xl:text-c1",
  button: "xl:font-display-sans xl:text-button",
  t1: "xl:font-display-sans xl:text-t1",
  t2: "xl:font-display-sans xl:text-t2",
  t3: "xl:font-display-sans xl:text-t3",
  hw1: "xl:font-handwritten xl:text-hw1",
  hw2: "xl:font-handwritten xl:text-hw2",
  hw3: "xl:font-handwritten xl:text-hw3",
  hw4: "xl:font-handwritten xl:text-hw4",
  hw5: "xl:font-handwritten xl:text-hw5",
  mh1: "xl:font-display-sans xl:text-mh1",
  "mh1.5": "xl:font-display-sans xl:text-mh1.5",
  mb1: "xl:font-body-p xl:text-mb1",
  mph1: "xl:font-display-serif xl:text-mph1",
};

export const xxlClasses = {
  h1: "2xl:font-display-sans 2xl:text-h1",
  h2: "2xl:font-display-sans 2xl:text-h2",
  h3: "2xl:font-display-sans 2xl:text-h3",
  h4: "2xl:font-display-sans 2xl:text-h4",
  h5: "2xl:font-display-sans 2xl:text-h5",
  h6: "2xl:font-display-sans 2xl:text-h6",
  sh1: "2xl:font-display-serif 2xl:text-sh1",
  sh2: "2xl:font-display-serif 2xl:text-sh2",
  sh3: "2xl:font-display-serif 2xl:text-sh3",
  sh4: "2xl:font-display-serif 2xl:text-sh4",
  sh5: "2xl:font-display-serif 2xl:text-sh5",
  sh6: "2xl:font-display-serif 2xl:text-sh6",
  sh7: "2xl:font-display-serif 2xl:text-sh7",
  ph1: "2xl:font-display-serif 2xl:text-ph1",
  ph2: "2xl:font-display-serif 2xl:text-ph2",
  q1: "2xl:font-display-serif 2xl:text-q1",
  q2: "2xl:font-display-serif 2xl:text-q2",
  q3: "2xl:font-display-serif 2xl:text-q3",
  q4: "2xl:font-display-serif 2xl:text-q4",
  q5: "2xl:font-display-serif 2xl:text-q5",
  q6: "2xl:font-display-serif 2xl:text-q6",
  n1: "2xl:font-display-serif 2xl:text-n1",
  n2: "2xl:font-display-serif 2xl:text-n2",
  a1: "2xl:font-display-serif 2xl:text-a1",
  a2: "2xl:font-display-serif 2xl:text-a2",
  b1: "2xl:font-body-p 2xl:text-b1",
  b2: "2xl:font-body-p 2xl:text-b2",
  b3: "2xl:font-body-p 2xl:text-b3",
  b4: "2xl:font-body-p 2xl:text-b4",
  b5: "2xl:font-body-p 2xl:text-b5",
  c1: "2xl:font-body-p 2xl:text-c1",
  button: "2xl:font-display-sans 2xl:text-button",
  t1: "2xl:font-display-sans 2xl:text-t1",
  t2: "2xl:font-display-sans 2xl:text-t2",
  t3: "2xl:font-display-sans 2xl:text-t3",
  hw1: "2xl:font-handwritten 2xl:text-hw1",
  hw2: "2xl:font-handwritten 2xl:text-hw2",
  hw3: "2xl:font-handwritten 2xl:text-hw3",
  hw4: "2xl:font-handwritten 2xl:text-hw4",
  hw5: "2xl:font-handwritten 2xl:text-hw5",
  mh1: "2xl:font-display-sans 2xl:text-mh1",
  "mh1.5": "2xl:font-display-sans 2xl:text-mh1.5",
  mb1: "2xl:font-body-p 2xl:text-mb1",
  mph1: "2xl:font-display-serif 2xl:text-mph1",
};
