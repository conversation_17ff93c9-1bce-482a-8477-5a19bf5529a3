"use client";

import React, { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/16/solid";
import { cn } from "@/common/utils";
import Container from "../ui/Container";
import IconButton from "./IconButton";
import HeadlessCarousel, { useCardsIndex } from "./HeadlessCarousel";

export interface IIndexHandlerProps {
  currentIndex: number;
  setCurrentIndex: Dispatch<SetStateAction<number>>;
  maxIndex: number;
  currentPage: number;
  maxPages: number;
  setCurrentPage: Dispatch<SetStateAction<number>>;
}

type TIndexHandler = (props: IIndexHandlerProps) => void;

interface ISlideCarouselProps {
  children: React.ReactNode[];
  transparent?: boolean; // use this to make the background transparent
  colour?: "maroon" | "white"; // use this to set the colour of the buttons
  wrapperClassName?: string; // use this to add a class to the wrapper
  innerWrapperClassName?: string; // use this to add a class to the inner wrapper
  cardsPerViewGetter?: (width: number) => number; // use this to set the number of cards per view
  header?: React.ReactNode; // use this to render a header (if you want)
  handleNext?: TIndexHandler; // use this to handle the next button
  handlePrev?: TIndexHandler; // use this to handle the previous button
  autoCardWrapper?: boolean; // use this to remove the default wrapper around the children (if you want)
  buttonsWrapperClassName?: string; // use this to add a class to the buttons wrapper
  buttonsRender?: (props: {
    currentIndex: number;
    setCurrentIndex: Dispatch<SetStateAction<number>>;
    maxIndex: number;
    currentPage: number;
    maxPages: number;
    setCurrentPage: Dispatch<SetStateAction<number>>;
  }) => React.ReactNode;
  paginateByPage?: boolean; // use this to paginate by page instead of by cards
}

const defaultNextHandler: TIndexHandler = ({
  currentIndex,
  setCurrentIndex,
}) => {
  setCurrentIndex(currentIndex + 1);
};

const defaultPrevHandler: TIndexHandler = ({
  currentIndex,
  setCurrentIndex,
}) => {
  setCurrentIndex(currentIndex - 1);
};

const defaultCardsPerViewGetter = (width: number) => {
  if (width >= 1024) {
    return 3;
  } else if (width >= 768) {
    return 2;
  } else {
    return 1;
  }
};

const SlideCarousel = (props: ISlideCarouselProps) => {
  const {
    children,
    transparent = false,
    colour = "maroon",
    wrapperClassName = "",
    innerWrapperClassName = "",
    cardsPerViewGetter = defaultCardsPerViewGetter,
    header = null,
    autoCardWrapper = true,
    handleNext = defaultNextHandler,
    handlePrev = defaultPrevHandler,
    buttonsWrapperClassName = "",
    buttonsRender,
    paginateByPage,
  } = props;

  const containerRef = useRef<HTMLDivElement>(null);

  const {
    currentIndex,
    setCurrentIndex,
    maxIndex,
    currentPage,
    maxPages,
    setCurrentPage,
  } = useCardsIndex(cardsPerViewGetter, children.length, paginateByPage);

  const canScrollLeft = currentIndex > 0;
  const canScrollRight = currentIndex < maxIndex;

  const [cardsPerView, setCardsPerView] = useState(
    typeof window !== "undefined" ? cardsPerViewGetter(window.innerWidth) : 1
  );

  useEffect(() => {
    const updateView = () => setCardsPerView(cardsPerViewGetter(window.innerWidth));
    window.addEventListener("resize", updateView);
    return () => window.removeEventListener("resize", updateView);
  }, [cardsPerViewGetter]);

  const shouldShowDefaultButtons = !buttonsRender && children.length > cardsPerView;

  return (
    <Container
      size="full"
      className={cn(
        "overflow-hidden overscroll-contain",
        transparent ? "bg-transparent" : "bg-grays-G6",
        wrapperClassName,
      )}
    >
      <Container
        className={cn(
          "mx-auto flex flex-col gap-3xl overscroll-contain !py-0 lg:gap-4xl",
          innerWrapperClassName,
        )}
      >
        {header}
        <div className="relative overflow-visible">
          <div
            ref={containerRef}
            className="relative flex select-none transition-[left] duration-700 ease-in-out"
          >
            <HeadlessCarousel
              currentIndex={currentIndex}
              containerRef={containerRef}
            >
              {autoCardWrapper
                ? children.map((child, index) => (
                  <div
                    key={index}
                    className="mr-lg w-full flex-none md:w-[calc((100%-16px)/2)] lg:mr-2xl lg:w-[calc((100%-48px)/3)]"
                  >
                    {child}
                  </div>
                ))
                : children}
            </HeadlessCarousel>
          </div>
        </div>
        {buttonsRender ? (
          buttonsRender({
            currentIndex,
            setCurrentIndex,
            maxIndex,
            currentPage,
            maxPages,
            setCurrentPage,
          })
        ) : (shouldShowDefaultButtons &&
          <div
            className={cn("flex justify-end gap-md", buttonsWrapperClassName)}
          >
            <IconButton
              colour={colour}
              onClick={() =>
                handlePrev({
                  currentIndex,
                  setCurrentIndex,
                  maxIndex,
                  currentPage,
                  setCurrentPage,
                  maxPages,
                })
              }
              disabled={!canScrollLeft}
              Icon={ChevronLeftIcon}
            />
            <IconButton
              colour={colour}
              onClick={() =>
                handleNext({
                  currentIndex,
                  setCurrentIndex,
                  maxIndex,
                  currentPage,
                  maxPages,
                  setCurrentPage,
                })
              }
              disabled={!canScrollRight}
              Icon={ChevronRightIcon}
            />
          </div>
        )}
      </Container>
    </Container>
  );
};

export default SlideCarousel;
