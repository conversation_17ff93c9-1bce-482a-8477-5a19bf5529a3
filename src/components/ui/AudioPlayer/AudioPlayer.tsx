import { useState, useRef, useEffect, useCallback } from "react";
import Back15S from "@/components/icons/Back15s";
import Forward15S from "@/components/icons/Forward15s";
import Loading from "@/components/icons/Loading";
import { XMarkIcon, PlayIcon, PauseIcon } from "@heroicons/react/24/solid";
import clsx from "clsx";
function formatSecondsToMMSS(seconds: number): string {
  if (seconds < 0) throw new Error("Seconds cannot be negative");
  const minutes = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
}

const getProgress = (mouseX: number, barLeft: number, barRight: number) => {
  const a = (mouseX - barLeft) / (barRight - barLeft);
  if (a < 0) {
    return 0;
  }
  if (a > 1) {
    return 1;
  }
  return a;
};

type AnyFunc = (...args: any[]) => any;

const speedOptions = ["1.0", "1.5", "2.0"];

const AudioContainer = (props: {
  src: string;
  playerBtnRef: React.RefObject<HTMLElement>;
  containerClassname?: string;
  title: string;
  onPlay?: AnyFunc;
  onPause?: AnyFunc;
  onStop?: AnyFunc;
}) => {
  const {
    src: audioSrc,
    containerClassname = "",
    playerBtnRef,
    title,
    onPlay,
    onStop,
  } = props;
  const [loading, setLoading] = useState<boolean>(true);
  const [dragging, setDragging] = useState<boolean>(false);
  const [speedOption, setSpeedOption] = useState<number>(0);
  const baseBarRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [seeking, setSeeking] = useState<boolean>(false);
  const [duration, setDuration] = useState<number>(100000);
  const [progress, setProgress] = useState<number>(0);
  const [playing, setPlaying] = useState<boolean>(false);
  const [audioReady, setAudioReady] = useState<boolean>(false);
  const [showPlayer, setShowPlayer] = useState<boolean>(false);
  const position = `${((progress * 100) / duration).toFixed(0)}%`;
  const switchSpeed = () => {
    setSpeedOption((speedOption + 1) % 3);
  };
  const togglePlayStatus = useCallback(() => {
    const { current } = audioRef;
    if (!current) {
      return;
    }
    if (showPlayer) {
      setShowPlayer(false);
      current.pause();
    } else {
      setShowPlayer(true);
      current.play().catch((e) => {
        console.error(e);
      });
    }
  }, [setShowPlayer, showPlayer]);
  useEffect(() => {
    const { current: playerBtn } = playerBtnRef;
    if (!playerBtn) {
      return;
    }
    const { current: audio } = audioRef;
    if (!audio) {
      return;
    }
    if (!audioReady) {
      return;
    }
    const handler = togglePlayStatus;
    playerBtn.addEventListener("click", handler);
    return () => {
      playerBtn.removeEventListener("click", handler);
    };
  }, [playerBtnRef, playing, setShowPlayer, audioReady, togglePlayStatus]);
  const speedText = speedOptions[speedOption];
  const speed = Number(speedText);
  const handlePlayStart = () => {
    setPlaying(true);
    onPlay?.();
  };
  useEffect(() => {
    const { current } = audioRef;
    if (!current) {
      return;
    }
    current.playbackRate = speed;
  }, [speed]);
  const handleSeekOffset = (offset: number) => {
    const { current } = audioRef;
    if (!current) {
      return;
    }
    let newCurrent = current.currentTime + offset;
    if (newCurrent < 0) {
      newCurrent = 0;
    } else if (newCurrent > current.duration) {
      newCurrent = current.duration;
    }
    current.currentTime = newCurrent;
  };
  const handleClickBaseBar = (e: { clientX: number }) => {
    const { current: audioCurrent } = audioRef;
    if (!audioCurrent) {
      return;
    }
    if (!audioReady) {
      return;
    }
    const { current: baseBarCurrent } = baseBarRef;
    if (!baseBarCurrent) {
      return;
    }
    const { left, right } = baseBarCurrent.getBoundingClientRect();
    const { clientX } = e;
    const p = getProgress(clientX, left, right);
    audioCurrent.currentTime = audioCurrent.duration * p;
  };
  const handleTimeUpdate = () => {
    const { current } = audioRef;
    if (!current) {
      return;
    }
    if (dragging) {
      return;
    }
    setProgress(current.currentTime);
  };

  const handleClickPlay = async () => {
    const { current } = audioRef;
    if (!current) {
      return;
    }
    if (!playing) {
      if (audioReady) {
        await current.play();
        setShowPlayer(true);
      }
    } else {
      current.pause();
    }
  };
  const handlePause = () => {
    setPlaying(false);
    onStop?.();
  };
  const handleLoadedMetaData = () => {
    setDuration(audioRef?.current?.duration ?? 0);
    checkAudioReady();
  };
  const handleDraggerTouchStart = () => {
    const { current } = baseBarRef;
    if (!current) {
      return;
    }

    setDragging(true);
    document.body.style.cursor = "pointer";

    const rec = current.getBoundingClientRect();

    const moveHandler = (e: TouchEvent) => {
      const touch = e.touches[0];
      if (!touch) {
        return;
      }
      const p = getProgress(touch.clientX, rec.left, rec.right);
      setProgress(p * duration);
    };

    const upHandler = (e: TouchEvent) => {
      document.body.style.cursor = "";
      window.removeEventListener("touchend", upHandler);
      window.removeEventListener("touchmove", moveHandler);

      const { current: audio } = audioRef;
      const { current: barCurrent } = baseBarRef;
      if (!audio || !barCurrent) {
        return;
      }

      setDragging(false);
      const { left, right } = barCurrent.getBoundingClientRect();

      // Use `changedTouches` because `touches` will be empty on `touchend`
      const touch = e.changedTouches[0];
      if (!touch) {
        return;
      }
      const p = getProgress(touch.clientX, left, right);
      audio.currentTime = audio.duration * p;
    };

    window.addEventListener("touchmove", moveHandler);
    window.addEventListener("touchend", upHandler);
  };

  const handleDraggerMousedown = () => {
    const { current } = baseBarRef;
    if (!current) {
      return;
    }
    setDragging(true);
    document.body.style.cursor = "pointer";
    const rec = current.getBoundingClientRect();
    const moveHandler = (e: MouseEvent) => {
      const p = getProgress(e.clientX, rec.left, rec.right);
      setProgress(p * duration);
    };
    const upHandler = (e: { clientX: number }) => {
      document.body.style.cursor = "";
      window.removeEventListener("mouseup", upHandler);
      window.removeEventListener("mouseup", upHandler);
      window.removeEventListener("mousemove", moveHandler);
      window.removeEventListener("mousemove", moveHandler);
      const { current: audio } = audioRef;
      const { current: barCurrent } = baseBarRef;
      if (!audio || !barCurrent) {
        return;
      }
      setDragging(false);
      const { left, right } = barCurrent.getBoundingClientRect();
      const p = getProgress(e.clientX, left, right);
      audio.currentTime = audio.duration * p;
    };
    window.addEventListener("mouseup", upHandler);
    window.addEventListener("mousemove", moveHandler);
  };
  const checkAudioReady = () => {
    const { current } = audioRef;
    if (!current) {
      return;
    }
    const { readyState } = current;
    if (
      (
        [
          HTMLMediaElement.HAVE_ENOUGH_DATA,
          HTMLMediaElement.HAVE_FUTURE_DATA,
        ] as number[]
      ).includes(readyState)
    ) {
      setLoading(false);
      setAudioReady(true);
    }
  };

  return (
    <>
      <div
        style={{
          visibility: showPlayer ? "visible" : "hidden",
        }}
        className={clsx(
          "shadow-[0_0.375rem_2.5rem_rgba(0,0,0,0.1)] md:rounded-[0.625rem]",
          containerClassname,
        )}
      >
        <div className="relative w-screen rounded-[0.625rem] px-xl py-lg md:w-96">
          <audio
            src={audioSrc}
            ref={audioRef}
            onCanPlay={checkAudioReady}
            onSeeking={() => {
              setSeeking(true);
            }}
            onSeeked={() => {
              setSeeking(false);
            }}
            onCanPlayThrough={checkAudioReady}
            onTimeUpdate={handleTimeUpdate}
            onPlay={handlePlayStart}
            onPause={handlePause}
            onLoadedMetadata={handleLoadedMetaData}
          />
          <XMarkIcon
            className="absolute right-xl top-xl size-lg cursor-pointer"
            onClick={togglePlayStatus}
          />
          <div className="mr-4xl truncate font-display-serif text-[1.25rem] font-normal leading-[120%] tracking-normal">
            {title}
          </div>
          <div className="relative mt-[1.875rem]">
            <div
              ref={baseBarRef}
              className="absolute h-[0.1875rem] w-full cursor-pointer rounded bg-grays-G5"
              onClick={handleClickBaseBar}
            ></div>
            <div
              className={`pointer-events-none absolute h-[0.1875rem] rounded-[0.0625rem] bg-primary01-50`}
              style={{
                width: `calc(${position} + 0.0625rem)`,
              }}
            ></div>
            <div
              className={`absolute top-[-0.21875rem] size-2.5 cursor-pointer rounded-[0.3125rem] bg-primary01-50 shadow-[0_0.25rem_0.25rem_rgba(0,0,0,0.25)]`}
              style={{ left: `calc(${position} - 0.3125rem)` }}
              onMouseDown={handleDraggerMousedown}
              onTouchStart={handleDraggerTouchStart}
            />
          </div>
          <div className="relative top-2.5 flex w-full select-none justify-between font-body-single text-[0.875rem] font-normal leading-[120%] tracking-normal">
            <div>{formatSecondsToMMSS(progress)}</div>
            <div>{formatSecondsToMMSS(duration)}</div>
          </div>
          <div className="mt-lg flex justify-center">
            <div className="flex w-36 items-center justify-between">
              <Back15S
                className="size-[1.875rem] cursor-pointer select-none"
                onClick={() => {
                  handleSeekOffset(-15);
                }}
              />
              {loading || seeking ? (
                <Loading className="pointer-events-none h-4xl" />
              ) : playing ? (
                <PauseIcon
                  className="size-4xl cursor-pointer select-none"
                  onClick={handleClickPlay}
                />
              ) : (
                <PlayIcon
                  className="size-4xl cursor-pointer select-none"
                  onClick={handleClickPlay}
                />
              )}
              <Forward15S
                className="size-[1.875rem] cursor-pointer select-none"
                onClick={() => {
                  handleSeekOffset(15);
                }}
              />
            </div>
          </div>
          <div className="mt-2.5 hidden select-none font-body-single text-[0.875rem] font-normal leading-[120%] tracking-normal md:block">
            <span
              className="cursor-pointer hover:text-primary01-50"
              onClick={switchSpeed}
            >
              {speedText}x
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default AudioContainer;
