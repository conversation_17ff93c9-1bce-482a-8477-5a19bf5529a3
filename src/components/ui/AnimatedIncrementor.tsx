import React from "react";
import CountUp from "./clientCountup";

interface AnimatedIncrementorProps {
  target: string;
  startWhenInView?: boolean;
  runOnce?: boolean;
}

const AnimatedIncrementor = (props: AnimatedIncrementorProps) => {
  const { target, startWhenInView, runOnce = true } = props;
  const isANumber = (stringValue: string) => {
    if (typeof stringValue !== "string") return false;

    return !isNaN(Number(stringValue));
  };

  const extractSuffix = (value: string) => {
    if (value.endsWith("%")) return "%";
    if (value.endsWith("+")) return "+";

    return false;
  };

  // This is a natural number that doesn't need further processing and can be quickly animated
  if (isANumber(target))
    return (
      <CountUp
        end={Number(target)}
        separator=","
        enableScrollSpy={startWhenInView}
        scrollSpyOnce={runOnce}
      />
    );

  const hasSuffix = extractSuffix(target);

  if (hasSuffix) {
    // There is potential for the value WITHOUT a suffix to be a valid number, verify this
    const valueWithoutSuffix = target.substring(0, target.length - 1);
    if (isANumber(valueWithoutSuffix))
      return (
        <CountUp
          end={Number(valueWithoutSuffix)}
          suffix={hasSuffix}
          separator=","
          enableScrollSpy={startWhenInView}
          scrollSpyOnce={runOnce}
        />
      );
  }

  return <>{target}</>;
};

export default AnimatedIncrementor;
