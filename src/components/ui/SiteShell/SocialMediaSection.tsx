import Image from "next/image";
import { ISocialMediaIcon } from "./types";
import StoryblokLink from "../StoryblokLink";

const SocialMediaSection = (props: {
  socialMediaIcons: ISocialMediaIcon[];
  followUsLabel: string;
}) => {
  const { socialMediaIcons, followUsLabel } = props;

  return (
    <div>
      <h4 className="font-display-sans text-sans-lg text-primary01-75">
        {followUsLabel}
      </h4>
      <div className="mt-[0.94rem] grid max-w-[200px] grid-cols-4 gap-3">
        {socialMediaIcons.map((icon) => (
          <StoryblokLink key={icon.icon} link={icon.socialMediaLink}>
            <Image
              key={icon.icon}
              width={24}
              height={24}
              src={icon.icon}
              alt={icon.icon}
              className="size-10 transition-colors duration-200 hover:[filter:invert(13%)_sepia(98%)_saturate(2500%)_hue-rotate(2deg)_brightness(90%)_contrast(105%)]"
            />
          </StoryblokLink>
        ))}
      </div>
    </div>
  );
};

export default SocialMediaSection;
