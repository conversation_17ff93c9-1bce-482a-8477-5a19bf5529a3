"use client";

import { Disclosure } from "@headlessui/react";
import { IFooterDisclosureProps } from "../FooterDisclosure";
import { cn } from "@/common/utils";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import { lazy, Suspense } from "react";

const DisclosureButton = lazy(() =>
  import("@headlessui/react").then((mod) => ({
    default: mod.DisclosureButton,
  })),
);
const DisclosurePanel = lazy(() =>
  import("@headlessui/react").then((mod) => ({ default: mod.DisclosurePanel })),
);

// TODO: implement a more graceful loading fallback
const LoadingFallback = () => <div>Loading...</div>;

const MobileFooterDisclosure = (props: IFooterDisclosureProps) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Disclosure as="div" className="w-full md:hidden">
        {({ open }: { open: boolean }) => (
          <>
            <DisclosureButton className="flex w-full justify-between text-left !font-display-sans !text-sans-lg text-primary01-75">
              <span className="font-display-sans text-sans-lg">
                {props.label}
              </span>
              <ChevronDownIcon className={cn("size-5", open && "rotate-180")} />
            </DisclosureButton>
            <div className="overflow-hidden pb-5xl">
              <DisclosurePanel
                transition
                unmount={false}
                className="origin-top pt-[0.94rem] transition duration-200 ease-out data-[closed]:-translate-y-6 data-[closed]:opacity-0"
              >
                <div className="flex flex-col gap-[0.62rem]">
                  {props.items.map((item) => {
                    return (
                      <Link
                        className="block w-full font-body-p text-body-p-md text-primary01-75"
                        href={item.link}
                        key={item.label + item.id}
                      >
                        {item.label}
                      </Link>
                    );
                  })}
                </div>
              </DisclosurePanel>
            </div>
          </>
        )}
      </Disclosure>
    </Suspense>
  );
};

export default MobileFooterDisclosure;
