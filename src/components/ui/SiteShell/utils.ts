const PageTypeFlags = {
  blogPageV2: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: true,
    isFixedNavbarPageType: false,
  },
  profilePage: {
    shouldHideCtaBanner: false,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: false,
    isFixedNavbarPageType: true,
  },
  NceaToGpaCacResultPageV2: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: true,
    isFixedNavbarPageType: true,
  },
  NceaToGpaCalculatorV2: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: true,
    isFixedNavbarPageType: true,
  },
  NotFoundPage: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: false,
    isFixedNavbarPageType: true,
  },
  ServerErrorPage: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: false,
    isFixedNavbarPageType: true,
  },
  ALevelIBToGpaCalculatorV2: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: true,
    isFixedNavbarPageType: true,
  },
  ALevelIBToGpaCalculatorResultsV2: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: true,
    isFixedNavbarPageType: true,
  },
  UshToGpaCalculatorV2: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: true,
    isFixedNavbarPageType: true,
  },
  UshToGpaResultPageV2: {
    shouldHideCtaBanner: true,
    isWhiteNavbar: true,
    shouldHideFloatNavbar: true,
    isFixedNavbarPageType: true,
  },
  ebookPage: {
    shouldHideCtaBanner: false,
    isWhiteNavbar: false,
    shouldHideFloatNavbar: true,
    isFixedNavbarPageType: false,
  },
};

type PageType = keyof typeof PageTypeFlags;

const specialPagesToHideFooterEverywhere = new Set<PageType>([
  "NceaToGpaCacResultPageV2",
  "NceaToGpaCalculatorV2",
  "NotFoundPage",
]);

export const getShouldHideCtaBanner = (pageType?: string): boolean => {
  return PageTypeFlags[pageType as PageType]?.shouldHideCtaBanner;
};

export const getHasWhiteNavbar = (pageType?: string): boolean =>
  !!pageType && PageTypeFlags[pageType as PageType]?.isWhiteNavbar;

export const getHideFloatNavbar = (pageType?: string): boolean =>
  !!pageType && PageTypeFlags[pageType as PageType]?.shouldHideFloatNavbar;

export const getIsFixedNavbarPageType = (pageType?: string): boolean =>
  !!pageType && PageTypeFlags[pageType as PageType]?.isFixedNavbarPageType;

export const getShouldHideFooterAllDevices = (pageType?: string): boolean => {
  return (
    !!pageType && specialPagesToHideFooterEverywhere.has(pageType as PageType)
  );
};

export const shouldRenderBottomCtaBanner = (pageType?: string): boolean => {
  if (!pageType) return true;
  if (getShouldHideFooterAllDevices(pageType)) return false;
  return true;
};
