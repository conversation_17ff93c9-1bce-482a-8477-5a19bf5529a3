/* eslint-disable @typescript-eslint/no-empty-object-type */
import { IStory, IStoryblokLink } from "@/common/types";

export interface IHeaderBlockItem {
  _uid: string;
  link: IStoryblokLink;
  text: string;
  description?: string;
  component: string;
}
export interface IHeaderBlock {
  _uid: string;
  items: IHeaderBlockItem[];
  title: string;
  component: string;
  _editable: boolean;
}

export interface ISocialMediaIcon {
  _uid: string;
  icon: string;
  socialMediaLink: IStoryblokLink;
}

export interface ISbSiteShellStoryContent {
  eventsLabel: string;
  stickyNavigationMessage: string;
  stickyCtaLabel: string;
  stickyPageUrl: IStoryblokLink;
  ourServicesHeader: IHeaderBlock[];
  aboutCrimsonHeader: IHeaderBlock[];
  admissionsResourcesHeader: IHeaderBlock[];
  ctaButtonLabel: string;
  // Footer
  upcomingEventsLabel: string;
  ourServicesFooter: IHeaderBlockItem[];
  aboutCrimsonFooter: IHeaderBlockItem[];
  followUsLabel: string;
  socialMediaIcons: ISocialMediaIcon[];
  sitemapLabel: string;
  termsOfUseLabel: string;
  privacyPolicyLabel: string;
  cookiePreferencesLabel: string;
  declarationLabel: string;
  copyrightLabel: string;
  logoutLabel?: string;
}

export interface ISbSiteShellStory extends IStory<ISbSiteShellStoryContent> {};
