import Link from "next/link";
import MobileFooterDisclosure from "./Footer/MobileFooterDisclosure";

type FooterItem = {
  label: string;
  link: string;
  id: string;
};

export interface IFooterDisclosureProps {
  label: string;
  items: FooterItem[];
}
const FooterDisclosure = (props: IFooterDisclosureProps) => {
  return (
    <>
      <MobileFooterDisclosure {...props} />
      <div className="hidden md:block">
        <div className="flex w-full justify-between text-left font-display-sans text-sans-lg text-primary01-75">
          <span className="font-display-sans text-sans-lg">{props.label}</span>
        </div>
        <nav className="pb-4xl">
          <div className="origin-top pt-[0.94rem] transition duration-200 ease-out data-[closed]:-translate-y-6 data-[closed]:opacity-0">
            <ul className="flex flex-col gap-[0.62rem]">
              {props.items.map((item) => {
                return (
                  <li key={item.label + item.id}>
                    <Link
                      className="block w-full font-body-p text-body-p-md text-primary01-75 hover:text-primary01-50"
                      href={item.link}
                    >
                      {item.label}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        </nav>
      </div>
    </>
  );
};

export default FooterDisclosure;
