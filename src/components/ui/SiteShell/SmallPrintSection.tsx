"use client";

import Link from "next/link";
import { SmallPrintSeparator } from "./FooterIcons";
import { usePageContext } from "@/components/context/PageContext";
import OneTrustLink from "@/components/bloks/layout/OneTrustLink";
import Text from "../Text";
import { useLabelTranslation } from "@/common/hooks/useTranslation";

interface SmallPrint {
  label: string;
  link: string;
}

interface Props {
  sitemapLabel: string;
  termsOfUseLabel: string;
  cookiePreferencesLabel: string;
  privacyPolicyLabel: string;
  declarationLabel: string;
  hideNavFooter?: boolean;
}

const SmallPrintSection = (props: Props) => {
  const {
    sitemapLabel,
    termsOfUseLabel,
    cookiePreferencesLabel,
    privacyPolicyLabel,
    declarationLabel,
    hideNavFooter,
  } = props;

  const { locale } = usePageContext();
  const { t } = useLabelTranslation();

  const smallPrints: SmallPrint[] = [
    {
      label: sitemapLabel,
      link: "/sitemap/",
    },
    {
      label: termsOfUseLabel,
      link: "/terms-and-conditions/",
    },
    {
      label: cookiePreferencesLabel,
      link: "cookie-preferences",
    },
    {
      label: privacyPolicyLabel,
      link: "/privacy-policy/",
    },
  ];
  return (
    <div className="font-body-single text-body-single-2xs text-primary01-75">
      <div className="flex flex-wrap items-center gap-2 pb-[0.62rem]">
        {smallPrints.map((smallPrint, index) => {
          if (hideNavFooter && smallPrint.link === "/sitemap/") {
            return null;
          }
          return (
            <SmallPrintItem
              key={smallPrint.label}
              smallPrint={smallPrint}
              isLastItem={index === smallPrints.length - 1}
              locale={locale}
            />
          );
        })}
      </div>
      <Text tag="p" style="b4">
        © {new Date().getFullYear()}{" "}
        {t("Crimson Education. All rights reserved.")}
      </Text>
      <Text tag="p" style="b4">
        {declarationLabel}
      </Text>
    </div>
  );
};

const SmallPrintItem = ({
  smallPrint,
  isLastItem,
  locale,
}: {
  smallPrint: SmallPrint;
  isLastItem: boolean;
  locale: string;
}) => {
  if (smallPrint.link === "cookie-preferences") {
    return (
      <>
        <OneTrustLink key={smallPrint.label}>{smallPrint.label}</OneTrustLink>
        {!isLastItem && <SmallPrintSeparator />}
      </>
    );
  }

  return (
    <>
      <Link
        className="underline hover:text-primary01-50"
        href={`/${locale}${smallPrint.link}`}
      >
        {smallPrint.label}
      </Link>

      {!isLastItem && <SmallPrintSeparator />}
    </>
  );
};

export default SmallPrintSection;
