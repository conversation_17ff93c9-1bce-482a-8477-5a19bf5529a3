import React from "react";
import { TNavMenuItem } from ".";
import { cn } from "@/common/utils";
import Container from "../../Container";
import CrimsonLogo from "./CrimsonLogo";
import DesktopMenu from "./DesktopMenu";
import DesktopMenuEnd from "./DesktopMenuEnd";

type Props = {
  menuItems: TNavMenuItem[];
  ctaButtonLabel: string;
};

const FallbackNavbar = ({ menuItems, ctaButtonLabel }: Props) => {
  return (
    <div>
      <div
        className={cn(
          "fixed z-10 w-full bg-white px-[1.56rem] shadow md:px-[1.875rem] xl:px-[4.69rem]",
        )}
      >
        <Container className="!p-0 3xl:max-w-full">
          <div>
            <div className="relative flex h-[4.6875rem] justify-between">
              <div className="flex flex-1 items-stretch justify-start">
                <CrimsonLogo forceDarkStyle={true} />
                <DesktopMenu menuItems={menuItems} forceDarkStyle={true} />
              </div>

              <DesktopMenuEnd
                buttonLabel={ctaButtonLabel}
                forceDarkStyle={true}
              />
            </div>
          </div>
        </Container>
      </div>
    </div>
  );
};

export default FallbackNavbar;
