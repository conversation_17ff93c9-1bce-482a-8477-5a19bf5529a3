"use client";

import { useNavigation } from "./NavigationContext";
import { cn } from "@/common/utils";
import Link from "next/link";
import { usePageContext } from "@/components/context/PageContext";
import RedLogo from "./CrimsonLogoIcons/RedLogo.svg";
import WhiteLogo from "./CrimsonLogoIcons/WhiteLogo.svg";
import Image from "next/image";

type Props = {
  forceDarkStyle?: boolean;
};

const CrimsonLogo = ({ forceDarkStyle = false }: Props) => {
  const { isOpen, isScrolled, isInWhiteNavbarPageType } = useNavigation();
  const { locale, navLogoOverride, hideNavFooter } = usePageContext();
  const shouldUseDarkStyle =
    forceDarkStyle || isScrolled || isInWhiteNavbarPageType;

  if (hideNavFooter) {
    return (
      <span
        className={cn(
          "flex shrink-0 items-center",
          isOpen && "hidden lg:block",
        )}
      >
        {navLogoOverride && (
          <Image
            src={navLogoOverride}
            alt="Crimson Logo"
            width={128}
            height={39}
          />
        )}
        {!navLogoOverride &&
          (shouldUseDarkStyle ? (
            <Image src={RedLogo} alt="Crimson Logo" width={128} height={39} />
          ) : (
            <Image src={WhiteLogo} alt="Crimson Logo" width={128} height={39} />
          ))}
      </span>
    );
  }

  return (
    <Link
      href={locale ? "/" : `/${locale}`}
      className={cn(
        "flex shrink-0 items-center",
        shouldUseDarkStyle ? "text-primary01-100" : "text-white",
        isOpen && "hidden lg:block",
      )}
    >
      {navLogoOverride && (
        <Image
          src={navLogoOverride}
          alt="Crimson Logo"
          width={128}
          height={39}
        />
      )}
      {!navLogoOverride &&
        (shouldUseDarkStyle ? (
          <Image src={RedLogo} alt="Crimson Logo" width={128} height={39} />
        ) : (
          <Image src={WhiteLogo} alt="Crimson Logo" width={128} height={39} />
        ))}
    </Link>
  );
};

export default CrimsonLogo;
