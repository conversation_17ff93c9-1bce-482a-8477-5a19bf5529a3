"use client";

import { Disclosure } from "@headlessui/react";
import { NavigationProvider } from "./NavigationContext";
import { useMediaQuery, useWindowScroll } from "@uidotdev/usehooks";
import { cn } from "@/common/utils";
import { motion, AnimatePresence } from "motion/react";
import { useEffect, useState, useRef } from "react";
import {
  getHasWhiteNavbar,
  getHideFloatNavbar,
  getIsFixedNavbarPageType,
} from "../utils";

type Props = {
  children: React.ReactNode;
  pageType: string;
  className?: string;
};

export default function NavigationDisclosure({
  children,
  pageType,
  className,
}: Props) {
  const isLargeScreen = useMediaQuery("(min-width: 1024px)");
  const [{ y: scrollY }] = useWindowScroll();
  const isScrolled = (scrollY ?? 0) > 0;
  const [isVisible, setIsVisible] = useState(true);
  const lastScrollY = useRef(0);
  const [isScrollingUp, setIsScrollingUp] = useState(false);
  const ticking = useRef(false);

  // Define the page types that should have a white navbar initially
  const isInWhiteNavbarPageType = getHasWhiteNavbar(pageType);
  const haveFloatNavbar = getHideFloatNavbar(pageType);
  const isFixedNavbarPageType = getIsFixedNavbarPageType(pageType);

  useEffect(() => {
    if (isFixedNavbarPageType) {
      setIsVisible(true);
      return;
    }

    if (haveFloatNavbar) {
      setIsVisible(scrollY === 0);
      return;
    }

    if (scrollY === null) return;

    if (!ticking.current) {
      window.requestAnimationFrame(() => {
        const currentScrollY = scrollY;
        setIsScrollingUp(currentScrollY < lastScrollY.current);
        lastScrollY.current = currentScrollY;
        ticking.current = false;
      });
      ticking.current = true;
    }

    if (scrollY === 0) {
      setIsVisible(true);
    } else if (isScrollingUp) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [scrollY, isScrollingUp, haveFloatNavbar, isFixedNavbarPageType]);

  const hasScrolledPastThreshold = scrollY !== null && scrollY > 50;

  if (isLargeScreen) {
    return (
      <Disclosure as="div" className={cn("static", className)}>
        {({ open }) => (
          <AnimatePresence>
            {isFixedNavbarPageType ? (
              <nav
                className={cn(
                  "fixed left-0 top-0 z-10 w-full bg-transparent px-[1.56rem] md:px-[1.875rem] xl:px-[4.69rem]",
                  isScrolled && hasScrolledPastThreshold && "bg-white shadow",
                  open && "bg-neutral01-0",
                  !open && isInWhiteNavbarPageType
                    ? "bg-white shadow"
                    : undefined,
                )}
              >
                <NavigationProvider
                  isOpen={open}
                  isScrolled={isScrolled && hasScrolledPastThreshold}
                  pageType={pageType}
                  isInWhiteNavbarPageType={isInWhiteNavbarPageType}
                >
                  {children}
                </NavigationProvider>
              </nav>
            ) : (
              <motion.nav
                initial={{ y: 0 }}
                animate={{ y: isVisible ? 0 : -100 }}
                exit={{ y: 0 }}
                transition={{
                  type: "tween",
                  duration: 0.3,
                  ease: "easeInOut",
                }}
                className={cn(
                  "fixed left-0 top-0 z-10 w-full bg-transparent px-[1.56rem] md:px-[1.875rem] xl:px-[4.69rem]",
                  isScrolled && hasScrolledPastThreshold && "bg-white shadow",
                  open && "bg-neutral01-0",
                  !open && isInWhiteNavbarPageType
                    ? "bg-white shadow"
                    : undefined,
                )}
              >
                <NavigationProvider
                  isOpen={open}
                  isScrolled={isScrolled && hasScrolledPastThreshold}
                  pageType={pageType}
                  isInWhiteNavbarPageType={isInWhiteNavbarPageType}
                >
                  {children}
                </NavigationProvider>
              </motion.nav>
            )}
          </AnimatePresence>
        )}
      </Disclosure>
    );
  }

  return (
    <Disclosure>
      {({ open }) => (
        <div
          className={cn(
            "fixed z-10 w-full bg-transparent px-[1.56rem] md:px-[1.875rem] xl:px-[4.69rem]",
            isScrolled && "bg-white shadow",
            open && "bg-neutral01-0",
            !open && isInWhiteNavbarPageType ? "bg-white shadow" : undefined,
          )}
        >
          <NavigationProvider
            isOpen={open}
            isScrolled={isScrolled}
            pageType={pageType}
            isInWhiteNavbarPageType={isInWhiteNavbarPageType}
          >
            {children}
          </NavigationProvider>
        </div>
      )}
    </Disclosure>
  );
}
