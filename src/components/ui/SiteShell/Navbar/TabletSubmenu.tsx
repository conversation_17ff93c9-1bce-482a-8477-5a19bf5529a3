"use client";

import { ChevronRightIcon } from "@heroicons/react/24/solid";
import { TNavMenuItem } from ".";
import { cn } from "@/common/utils";
import StoryblokLink from "../../StoryblokLink";

type Props = {
  item: TNavMenuItem;
  setMenuOpen: (open: number) => void;
  itemId: number;
  isActive: boolean;
  hasSubItems: boolean;
};

const TabletSubmenu = ({
  item,
  setMenuOpen,
  itemId,
  isActive,
  hasSubItems,
}: Props) => {
  const handleMenuClick = () => {
    if (hasSubItems) {
      setMenuOpen(itemId);
    }
  };

  if (item.id === "events") {
    // The events page is hardcoded to always link to the locale events page
    return (
      <StoryblokLink
        link={
          item.href ?? {
            url: "/events",
            linktype: "url",
            cached_url: "/events",
          }
        }
        className={cn(
          "text-sans-md flex w-full cursor-pointer items-center justify-between p-xl font-display-sans",
          isActive ? "bg-white text-primary01-50" : "text-primary01-100",
        )}
      >
        {item.label}
      </StoryblokLink>
    );
  }

  return (
    <div
      onClick={handleMenuClick}
      className={cn(
        "text-sans-md flex w-full cursor-pointer items-center justify-between p-xl font-display-sans",
        isActive ? "bg-white text-primary01-50" : "text-primary01-100",
      )}
    >
      <span>{item.label}</span>
      {!isActive && hasSubItems && <ChevronRightIcon className="size-2xl" />}
    </div>
  );
};

export default TabletSubmenu;
