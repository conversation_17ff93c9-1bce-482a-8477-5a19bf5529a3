"use client";

import Button from "@/components/ui/Button";
import { useNavigation } from "./NavigationContext";
import StoryblokLink from "../../StoryblokLink";
import { usePageContext } from "@/components/context/PageContext";
// TODO: Add these elements once we get to this point in the project. UI is mainly done in this component.
// import { LanguageIcon } from "@heroicons/react/24/outline";
// import { cn } from "@/common/utils";
// import ProfileDropdown from "./ProfileDropdown";

type Props = {
  buttonLabel?: string;
  forceDarkStyle?: boolean;
};

const DesktopMenuEnd = ({
  buttonLabel = "Get Started",
  forceDarkStyle = false,
}: Props) => {
  const { hideNavFooter } = usePageContext();
  const { isScrolled, isInWhiteNavbarPageType } = useNavigation();
  const shouldUseDarkStyle =
    forceDarkStyle || isScrolled || isInWhiteNavbarPageType;

  if (hideNavFooter) {
    return null;
  }
  return (
    <div className="hidden items-center gap-x-xl lg:flex">
      {/* <ProfileDropdown /> */}
      {/* <LanguageIcon
        className={cn(
          "size-5",
          isScrolled ? "text-primary01-100" : "text-white",
        )}
      /> */}
      <StoryblokLink
        link={{
          url: "/en/contact/",
          linktype: "url",
          cached_url: "/en/contact/",
        }}
      >
        <Button theme="primary" colour={shouldUseDarkStyle ? "red" : "white"}>
          {buttonLabel}
        </Button>
      </StoryblokLink>
    </div>
  );
};

export default DesktopMenuEnd;
