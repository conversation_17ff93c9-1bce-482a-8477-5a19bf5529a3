"use client";

import { DisclosurePanel } from "@headlessui/react";
import dynamic from "next/dynamic";
import { TNavMenuItem } from ".";
// import { ArrowLeftEndOnRectangleIcon } from "@heroicons/react/24/outline";
import BottomCtaBanner from "./BottomCtaBanner";
import { useMediaQuery } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { cn } from "@/common/utils";
import { useNavigation } from "./NavigationContext";
import { IStoryblokLink } from "@/common/types";
import StoryblokLink from "../../StoryblokLink";
import { usePageContext } from "@/components/context/PageContext";

const MobileSubmenu = dynamic(
  () => import("./MobileSubmenu").then((mod) => mod.default),
  {
    ssr: false,
  },
);

const TabletSubmenu = dynamic(
  () => import("./TabletSubmenu").then((mod) => mod.default),
  {
    ssr: false,
  },
);

interface Props {
  menuItems: TNavMenuItem[];
  stickyNavigationMessage: string;
  stickyCtaLabel: string;
  stickyPageUrl: IStoryblokLink;
  ctaButtonLabel: string;
}
const MobileMenu = ({
  menuItems,
  stickyNavigationMessage,
  ctaButtonLabel,
}: Props) => {
  const isTablet = useMediaQuery(
    "only screen and (min-width : 768px) and (max-width : 1023px)",
  );
  const { isOpen } = useNavigation();
  const [isTabletMenuOpen, setIsTabletMenuOpen] = useState(Infinity);

  useEffect(() => {
    if (!isOpen) {
      setIsTabletMenuOpen(Infinity);
    }
  }, [isOpen]);

  const { hideNavFooter } = usePageContext();
  if (hideNavFooter) {
    return null;
  }

  return (
    <DisclosurePanel className="fixed inset-x-0 bottom-0 top-[4.6875rem] z-30 flex flex-col bg-neutral01-0 lg:hidden">
      <div
        className={cn(
          "flex-1 overflow-y-auto",
          isTabletMenuOpen < menuItems.length &&
            "md:grid md:grid-cols-[1fr_2fr]",
        )}
      >
        <div>
          <nav>
            {!isTablet && (
              <div className="border-b border-b-grays-G5">
                {menuItems.map((item) => {
                  return <MobileSubmenu key={item.label} item={item} />;
                })}
              </div>
            )}
            {isTablet && (
              <div className="border-b border-b-grays-G5">
                {menuItems.map((item, index) => {
                  return (
                    <TabletSubmenu
                      key={item.label}
                      item={item}
                      setMenuOpen={setIsTabletMenuOpen}
                      itemId={index}
                      isActive={isTabletMenuOpen === index}
                      hasSubItems={item.subItems.length > 0}
                    />
                  );
                })}
              </div>
            )}

            {/* TODO: Logout Button implementation here */}
            {/* <button className="text-sans-md flex w-full items-center justify-between p-xl font-display-sans text-primary01-100">
              <span>Logout</span>
              <ArrowLeftEndOnRectangleIcon className="size-xl" />
            </button> */}
          </nav>
          {/* TODO: Language Selector implementation here */}
          {/* <div>
            <div className="border-t border-t-grays-G5 p-lg">
              <LanguageSelector />
            </div>
          </div> */}
        </div>
        {isTabletMenuOpen < menuItems.length && menuItems[isTabletMenuOpen] && (
          <div className="overflow-y-auto bg-white">
            {menuItems[isTabletMenuOpen].subItems.map((subItem) => (
              <StoryblokLink
                key={subItem.title}
                link={subItem.href}
                className="block rounded-md p-xl"
              >
                <p className="text-sans-md font-display-sans text-primary01-100">
                  {subItem.title}
                </p>
                {subItem?.description && (
                  <p className="mt-[0.31rem] font-body-single text-body-single-sm text-grays-G4">
                    {subItem.description}
                  </p>
                )}
              </StoryblokLink>
            ))}
          </div>
        )}
      </div>
      <div className="shrink-0">
        <BottomCtaBanner
          title={stickyNavigationMessage}
          buttonText={ctaButtonLabel}
          isInline={true}
        />
      </div>
    </DisclosurePanel>
  );
};

// const LanguageSelector = () => {
//   const notificationMethods = [
//     { id: "english", title: "English" },
//     { id: "chinese", title: "繁體中文" },
//     { id: "spanish", title: "Español" },
//   ];
//   return (
//     <fieldset>
//       <legend className="font-body-p text-body-p-sm text-primary01-100">
//         Language Settings
//       </legend>

//       <div className="mt-4 flex flex-col gap-y-lg">
//         {notificationMethods.map((notificationMethod) => (
//           <div key={notificationMethod.id} className="flex items-center">
//             <input
//               defaultChecked={notificationMethod.id === "email"}
//               id={notificationMethod.id}
//               name="notification-method"
//               type="radio"
//               className="relative size-4 appearance-none rounded-full border border-neutral01-25 bg-white before:absolute before:inset-1 before:rounded-full before:bg-primary01-50 checked:!border-primary01-50 checked:!bg-white focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 active:outline-none active:ring-0 forced-colors:appearance-auto forced-colors:before:hidden [&:not(:checked)]:before:hidden"
//             />
//             <label
//               htmlFor={notificationMethod.id}
//               className="ml-2 block font-body-p text-body-p-2xs text-primary01-75"
//             >
//               {notificationMethod.title}
//             </label>
//           </div>
//         ))}
//       </div>
//     </fieldset>
//   );
// };

export default MobileMenu;
