"use client";

import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import { DisclosureButton } from "@headlessui/react";
import { useNavigation } from "./NavigationContext";
import { cn } from "@/common/utils";
import { usePageContext } from "@/components/context/PageContext";

export default function MobileMenuButton() {
  const { isScrolled, isOpen, pageType } = useNavigation();

  const { hideNavFooter } = usePageContext();
  if (hideNavFooter) {
    return null;
  }

  return (
    <div className={cn(
      "absolute inset-y-0 right-0 flex items-center",
      pageType === "NotFoundPage" ? "flex xl:hidden" : "lg:hidden"
    )}>
      {/* Mobile menu button */}
      <DisclosureButton
        className={cn(
          "group relative inline-flex items-center justify-center rounded-md focus:outline-none focus:ring-transparent",
          isScrolled ? "text-primary01-100" : "text-white",
        )}
      >
        <span className="absolute -inset-0.5" />
        <span className="sr-only">Open main menu</span>
        <Bars3Icon
          aria-hidden="true"
          className={cn("block size-6 group-data-[open]:hidden", {
            "text-primary01-100": isScrolled,
            "text-white": !isScrolled,
          })}
        />
        <XMarkIcon
          aria-hidden="true"
          className={cn(
            "hidden size-6 group-data-[open]:block",
            !isScrolled && "text-white",
            isOpen && "text-primary01-100",
            isScrolled && "text-primary01-100",
          )}
        />
      </DisclosureButton>
    </div>
  );
}
