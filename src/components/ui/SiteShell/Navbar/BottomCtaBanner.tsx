"use client";

import Button from "../../Button";
import { cn } from "@/common/utils";
import StoryblokLink from "../../StoryblokLink";
import Container from "../../Container";
import { usePageContext } from "@/components/context/PageContext";
import { getShouldHideCtaBanner, shouldRenderBottomCtaBanner } from "../utils";

type Props = {
  title: string;
  buttonText: string;
  isInline?: boolean;
  pageType?: string;
};

const BottomCtaBanner = ({
  title = "Book a free consultation with one of our expert advisors",
  buttonText = "Get Started",
  isInline = true,
  pageType,
}: Props) => {
  const { stickyBannerOverrides } = usePageContext();
  const displayTitle = stickyBannerOverrides?.title ?? title;
  const displayButtonText = stickyBannerOverrides?.buttonText ?? buttonText;
  const hasButtonBeenOverridden = !!stickyBannerOverrides?.buttonText;
  const hasCustomClick = typeof stickyBannerOverrides?.onClick === "function";

  // TODO - can remove scrollIntoView logic to the page level
  const handleButtonClick = () => {
    if (hasCustomClick) {
      stickyBannerOverrides?.onClick?.();
    } else {
      const formElement = document.getElementById("standard-web-lead-form");
      if (formElement) {
        formElement.scrollIntoView({ behavior: "smooth" });
      }
    }
  };

  if (!shouldRenderBottomCtaBanner(pageType)) return null;
  const shouldHideCtaBanner = getShouldHideCtaBanner(pageType);

  // Hide CTA banner completely for pages that have shouldHideCtaBanner: true
  if (shouldHideCtaBanner) return null;

  return (
    <div
      className={cn(
        "border-t border-solid border-neutral01-0 bg-white py-xl md:py-4",
        "sticky bottom-0 z-[9]",
        isInline && "z-20",
        shouldHideCtaBanner ? "lg:hidden" : "",
      )}
    >
      <Container className="flex items-center justify-between gap-x-xl !py-0">
        <p className="font-body-p text-body-p-sm font-bold text-neutral01-75 md:text-body-p-md md:font-extrabold lg:text-body-p-lg lg:font-extrabold">
          {displayTitle}
        </p>
        {hasButtonBeenOverridden && (
          <div>
            <BottomCtaBannerButton
              buttonText={displayButtonText}
              onClick={handleButtonClick}
            />
          </div>
        )}
        {!hasButtonBeenOverridden && (
          <StoryblokLink
            link={{
              url: "/en/contact/",
              linktype: "url",
              cached_url: "/en/contact/",
            }}
          >
            <BottomCtaBannerButton buttonText={displayButtonText} />
          </StoryblokLink>
        )}
      </Container>
    </div>
  );
};

const BottomCtaBannerButton = ({
  buttonText,
  onClick,
}: {
  buttonText: string;
  onClick?: () => void;
}) => {
  return (
    <Button className="w-full max-w-[8.625rem]" onClick={onClick}>
      <span className="whitespace-nowrap">{buttonText}</span>
    </Button>
  );
};

export default BottomCtaBanner;
