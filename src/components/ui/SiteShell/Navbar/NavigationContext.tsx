"use client";

import React, { createContext, useContext } from "react";

type NavigationContextType = {
  isOpen: boolean;
  isScrolled: boolean;
  pageType: string;
  isInWhiteNavbarPageType: boolean;
};

const NavigationContext = createContext<NavigationContextType>({
  isOpen: false,
  isScrolled: false,
  pageType: "",
  isInWhiteNavbarPageType: false,
});

export const useNavigation = () => useContext(NavigationContext);

export const NavigationProvider = ({
  children,
  isOpen,
  isScrolled,
  pageType,
  isInWhiteNavbarPageType,
}: {
  children: React.ReactNode;
  isOpen: boolean;
  isScrolled: boolean;
  pageType: string;
  isInWhiteNavbarPageType: boolean;
}) => {
  return (
    <NavigationContext.Provider value={{ isOpen, isScrolled, pageType,isInWhiteNavbarPageType }}>
      {children}
    </NavigationContext.Provider>
  );
};
