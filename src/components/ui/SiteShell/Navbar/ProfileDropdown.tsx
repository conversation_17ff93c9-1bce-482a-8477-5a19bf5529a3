import { <PERSON>u, <PERSON>u<PERSON>utton, <PERSON>u<PERSON><PERSON>, MenuItems } from "@headlessui/react";
import Image from "next/image";

export default function ProfileDropdown() {
  const dummyUser =
    "https://a.storyblok.com/f/64062/1080x1080/ff9d7bc38a/tha-bdsa-cassidy-headshot.png";
  return (
    <div className="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
      <Menu as="div" className="relative ml-3">
        <div>
          <MenuButton className="relative flex rounded focus:outline-none focus:ring-2 focus:ring-offset-2">
            <span className="absolute -inset-1.5" />
            <span className="sr-only">Open user menu</span>
            <Image
              alt="User Profile"
              width={32}
              height={32}
              src={dummyUser}
              className="size-8 rounded-full"
            />
          </MenuButton>
        </div>
        <MenuItems
          transition
          className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-neutral01-0 py-1 shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[enter]:duration-200 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
        >
          <MenuItem>
            <a
              href="#"
              className="font-body-single-p block px-4 py-2 text-body-single-sm text-primary01-100 data-[focus]:outline-none"
            >
              Your Profile
            </a>
          </MenuItem>
          <MenuItem>
            <a
              href="#"
              className="block px-4 py-2 text-sm text-primary01-100 data-[focus]:outline-none"
            >
              Settings
            </a>
          </MenuItem>
          <MenuItem>
            <a
              href="#"
              className="block px-4 py-2 text-sm text-primary01-100 data-[focus]:outline-none"
            >
              Log out
            </a>
          </MenuItem>
        </MenuItems>
      </Menu>
    </div>
  );
}
