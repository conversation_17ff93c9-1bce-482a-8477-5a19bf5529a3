"use client";

import {
  Popover,
  PopoverButton,
  PopoverGroup,
  PopoverPanel,
} from "@headlessui/react";
import { TNavMenuItem } from ".";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { cn } from "@/common/utils";
import { useNavigation } from "./NavigationContext";
import StoryblokLink from "../../StoryblokLink";
import { useEffect } from "react";
import { usePageContext } from "@/components/context/PageContext";

interface Props {
  menuItems: TNavMenuItem[];
  forceDarkStyle?: boolean;
}

const DesktopMenu = ({ menuItems, forceDarkStyle = false }: Props) => {
  const { isScrolled, isInWhiteNavbarPageType } = useNavigation();
  const { hideNavFooter } = usePageContext();

  const shouldUseDarkStyle =
    forceDarkStyle || isScrolled || isInWhiteNavbarPageType;

  if (hideNavFooter) {
    return null;
  }

  return (
    <PopoverGroup
      as="nav"
      className="hidden lg:ml-6 lg:flex lg:space-x-8 xl:ml-8 2xl:ml-10"
    >
      {menuItems.map((item) => {
        return (
          <Popover className="relative flex items-center" key={item.label}>
            {({ open, close }) => (
              <PopoverContent
                item={item}
                open={open}
                close={close}
                isScrolled={isScrolled}
                shouldUseDarkStyle={shouldUseDarkStyle}
              />
            )}
          </Popover>
        );
      })}
    </PopoverGroup>
  );
};

const PopoverContent = ({
  item,
  open,
  close,
  isScrolled,
  shouldUseDarkStyle,
}: {
  item: TNavMenuItem;
  open: boolean;
  close: () => void;
  isScrolled: boolean;
  shouldUseDarkStyle: boolean;
}) => {
  useEffect(() => {
    if (!open) return;

    const handleScroll = () => {
      close();
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [open, close]);

  return (
    <>
      {item.id !== "events" && (
        <PopoverButtonItem
          item={item}
          isScrolled={isScrolled}
          open={open}
          shouldUseDarkStyle={shouldUseDarkStyle}
        />
      )}

      {item.id === "events" && (
        <NavbarLink
          item={item}
          isScrolled={isScrolled}
          open={open}
          shouldUseDarkStyle={shouldUseDarkStyle}
        />
      )}

      {item.subItems.length > 0 && (
        <PopoverPanel
          transition
          anchor="bottom start"
          className="z-20 mt-4 rounded bg-white shadow outline-none"
          unmount={false}
        >
          <ul className="max-w-[600px] p-1">
            {item.subItems.map((subItem) => (
              <li key={subItem.title + item.label}>
                <StoryblokLink
                  key={subItem.title + item.label}
                  className="group block p-xl transition-all duration-300 ease-in-out hover:bg-white"
                  link={subItem.href}
                >
                  <p className="font-display-sans text-sans-base text-primary01-100 transition-colors duration-300 ease-in-out group-hover:text-primary01-50">
                    {subItem.title}
                  </p>
                  {subItem.description && (
                    <p className="pt-[0.31rem] font-body-single text-body-single-sm text-grays-G4 transition-colors duration-300 ease-in-out group-hover:text-primary01-100">
                      {subItem.description}
                    </p>
                  )}
                </StoryblokLink>
              </li>
            ))}
          </ul>
        </PopoverPanel>
      )}
    </>
  );
};

const NavbarLink = ({
  item,
  open,
  shouldUseDarkStyle,
}: {
  item: TNavMenuItem;
  isScrolled: boolean;
  open: boolean;
  shouldUseDarkStyle: boolean;
}) => {
  return (
    <StoryblokLink
      link={
        item.href ?? {
          url: "",
          linktype: "",
          cached_url: "",
        }
      }
      className={cn(
        "flex items-end justify-center gap-2.5 font-display-sans text-sans-sm",
        shouldUseDarkStyle ? "text-primary01-100" : "text-white",
      )}
    >
      <div
        className={cn(
          "group relative cursor-pointer font-display-sans text-sans-sm xl:text-sans-base",
          {
            "text-white": !shouldUseDarkStyle,
            "text-primary01-100": shouldUseDarkStyle,
          },
        )}
      >
        {item.label}
        <span
          className={cn(
            "absolute bottom-[-1px] left-0 h-[1px] w-0 transition-all duration-300 group-hover:w-full",
            {
              "w-full": open,
              "bg-white":
                !shouldUseDarkStyle && (open || "group-hover:bg-white"),
              "bg-primary01-100":
                shouldUseDarkStyle && (open || "group-hover:bg-primary01-100"),
            },
          )}
        />
      </div>
    </StoryblokLink>
  );
};

const PopoverButtonItem = ({
  item,
  open,
  shouldUseDarkStyle,
}: {
  item: TNavMenuItem;
  isScrolled: boolean;
  open: boolean;
  shouldUseDarkStyle: boolean;
}) => {
  return (
    <PopoverButton
      className={cn(
        "flex items-end justify-center gap-2.5 font-display-sans text-sans-sm outline-none",
        shouldUseDarkStyle ? "text-primary01-100" : "text-white",
      )}
    >
      {({ active }) => (
        <>
          <div
            className={cn(
              "group relative cursor-pointer font-display-sans text-sans-sm xl:text-sans-base",
              {
                "text-white": !shouldUseDarkStyle,
                "text-primary01-100": shouldUseDarkStyle,
              },
            )}
          >
            {item.label}
            <span
              className={cn(
                "absolute bottom-[-1px] left-0 h-[1px] w-0 transition-all duration-300 group-hover:w-full",
                {
                  "w-full": active || open,
                  "bg-white":
                    !shouldUseDarkStyle &&
                    (active || open || "group-hover:bg-white"),
                  "bg-primary01-100":
                    shouldUseDarkStyle &&
                    (active || open || "group-hover:bg-primary01-100"),
                },
              )}
            />
          </div>
          {item.subItems.length > 0 && (
            <ChevronDownIcon
              className={cn("size-5", {
                "rotate-180": open,
              })}
            />
          )}
        </>
      )}
    </PopoverButton>
  );
};

export default DesktopMenu;
