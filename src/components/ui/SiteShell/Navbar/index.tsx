"use client";
import DesktopMenu from "./DesktopMenu";
import MobileMenuButton from "./MobileMenuButton";
import dynamic from "next/dynamic";
import DesktopMenuEnd from "./DesktopMenuEnd";
import Container from "@/components/ui/Container";
import { IHeaderBlock } from "@/components/ui/SiteShell/types";
import { IStoryblokLink } from "@/common/types";
import FallbackNavbar from "./FallbackNavbar";
import CrimsonLogo from "./CrimsonLogo";

const MobileMenu = dynamic(
  () => import("./MobileMenu").then((mod) => mod.default),
  {
    ssr: false,
  },
);

const NavigationDisclosure = dynamic(
  () => import("./NavigationDisclosure").then((mod) => mod.default),
  {
    ssr: false,
  },
);

export type TNavMenuItem = {
  label: string;
  id: string;
  href?: IStoryblokLink;
  subItems: {
    title: string;
    description: string;
    href: IStoryblokLink;
    id: string;
  }[];
};

const createMenuItems = (sbMenuItems: IHeaderBlock[]): TNavMenuItem[] => {
  return sbMenuItems.map((menuItem) => {
    return {
      label: menuItem.title,
      id: menuItem._uid,
      subItems: menuItem?.items?.map((item) => ({
        title: item.text,
        description: item.description ?? "",
        href: item.link,
        id: item._uid,
      })),
    };
  });
};
interface Props {
  ourServicesHeader: IHeaderBlock[];
  aboutCrimsonHeader: IHeaderBlock[];
  admissionsResourcesHeader: IHeaderBlock[];
  eventsLabel: string;
  pageType: string;
  stickyNavigationMessage: string;
  stickyCtaLabel: string;
  stickyPageUrl: IStoryblokLink;
  ctaButtonLabel: string;
}

export default function Navbar(props: Props) {
  const {
    ourServicesHeader,
    aboutCrimsonHeader,
    admissionsResourcesHeader,
    eventsLabel = "Events",
    pageType,
    stickyNavigationMessage,
    stickyCtaLabel,
    stickyPageUrl,
    ctaButtonLabel,
  } = props;

  const menuItems = createMenuItems([
    ...ourServicesHeader,
    ...aboutCrimsonHeader,
    ...admissionsResourcesHeader,
  ] as IHeaderBlock[]);

  // Append events to the end of the menuItems array because its url is hardcoded
  menuItems.push({
    label: eventsLabel,
    id: "events",
    href: {
      url: "/events",
      linktype: "url",
      cached_url: "/events",
    },
    subItems: [],
  });

  return (
    <>
      <NavigationDisclosure pageType={pageType}>
        <Container className="!p-0 3xl:max-w-full">
          <div>
            <div className="relative flex h-[4.6875rem] justify-between">
              <MobileMenuButton />
              <div className="flex flex-1 items-stretch justify-start">
                <CrimsonLogo />
                <DesktopMenu menuItems={menuItems} />
              </div>

              <DesktopMenuEnd buttonLabel={ctaButtonLabel} />
            </div>
          </div>
        </Container>

        <MobileMenu
          menuItems={menuItems}
          stickyNavigationMessage={stickyNavigationMessage}
          stickyCtaLabel={stickyCtaLabel}
          stickyPageUrl={stickyPageUrl}
          ctaButtonLabel={ctaButtonLabel}
        />
      </NavigationDisclosure>
      <div className="hidden noscript:block">
        <FallbackNavbar menuItems={menuItems} ctaButtonLabel={ctaButtonLabel} />
      </div>
    </>
  );
}
