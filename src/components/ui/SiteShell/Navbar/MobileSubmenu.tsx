"use client";
import { DisclosureButton } from "@headlessui/react";
import {
  ArrowLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/solid";
import React, { useState } from "react";
import { TNavMenuItem } from ".";
import StoryblokLink from "../../StoryblokLink";

type Props = {
  item: TNavMenuItem;
};

type SubMenuProps = {
  item: TNavMenuItem;
  setIsOpen: (isOpen: boolean) => void;
};

const MobileSubmenu = ({ item }: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  if (isOpen) {
    return <SubMenu item={item} setIsOpen={setIsOpen} />;
  }

  if (item.id === "events") {
    return (
      <StoryblokLink
        link={
          item?.href ?? {
            url: "/events",
            linktype: "url",
            cached_url: "/events",
          }
        }
        className="text-sans-md flex w-full items-center justify-between p-xl font-display-sans text-primary01-100"
      >
        <span>{item.label}</span>
      </StoryblokLink>
    );
  }

  return (
    <div
      onClick={() => setIsOpen(true)}
      className="text-sans-md flex w-full items-center justify-between p-xl font-display-sans text-primary01-100"
    >
      <span>{item.label}</span>
      <ChevronRightIcon className="size-2xl" />
    </div>
  );
};

const SubMenu = ({ item, setIsOpen }: SubMenuProps) => {
  return (
    <div className="fixed inset-0 z-20 mt-0 h-screen w-full bg-neutral01-0">
      <div className="flex h-full flex-col">
        <div className="flex items-center justify-between px-xl py-lg">
          <button
            onClick={() => setIsOpen(false)}
            className="flex items-center text-primary01-100"
          >
            <ArrowLeftIcon className="mr-sm size-xl" />
            <span className="sr-only">Back</span>
          </button>
          <DisclosureButton className="text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:ring-indigo-500 group relative inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset">
            <span className="absolute -inset-0.5" />
            <span className="sr-only">Close main menu</span>
            <XMarkIcon
              aria-hidden="true"
              className="hidden size-6 group-data-[open]:block"
            />
          </DisclosureButton>
        </div>
        <div className="flex-1 overflow-auto bg-white">
          {item.subItems.map((subItem) => {
            return (
              <StoryblokLink
                key={subItem.title}
                link={subItem.href}
                className="block rounded-md p-xl"
              >
                <p className="text-sans-md font-display-sans text-primary01-100">
                  {subItem.title}
                </p>
                {subItem?.description && (
                  <p className="mt-[0.31rem] font-body-single text-body-single-sm text-grays-G4">
                    {subItem.description}
                  </p>
                )}
              </StoryblokLink>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default MobileSubmenu;
