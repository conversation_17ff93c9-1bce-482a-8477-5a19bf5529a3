"use client";
import SocialMediaSection from "./SocialMediaSection";
import SmallPrintSection from "./SmallPrintSection";
import Container from "../Container";
import { IHeaderBlock, IHeaderBlockItem, ISocialMediaIcon } from "./types";
import FooterDisclosure from "./FooterDisclosure";
import LocalePicker from "@/components/ui/LocaleModal/LocalePicker";
import { usePageContext } from "@/components/context/PageContext";

type Props = {
  locale: string;
  eventsLabel: string;
  ourServicesFooter: IHeaderBlockItem[];
  aboutCrimsonFooter: IHeaderBlockItem[];
  ourServicesHeader: IHeaderBlock[];
  aboutCrimsonHeader: IHeaderBlock[];
  admissionsResourcesHeader: IHeaderBlock[];
  upcomingEventsLabel: string;
  followUsLabel: string;
  socialMediaIcons: ISocialMediaIcon[];
  sitemapLabel: string;
  termsOfUseLabel: string;
  privacyPolicyLabel: string;
  cookiePreferencesLabel: string;
  declarationLabel: string;
};

const createLinks = (navigationLinks: IHeaderBlock[]) => {
  return navigationLinks.map((link) => ({
    label: link.title,
    id: link._uid,
    links: link.items.map((item) => ({
      label: item.text,
      link: item.link.url,
      id: item._uid,
    })),
  }));
};

const appendFooterLinksToNavigation = (
  navigation: IHeaderBlock[],
  footerLinks: IHeaderBlockItem[],
) => {
  return navigation.map((navItem) => ({
    ...navItem,
    items: [...navItem.items, ...footerLinks],
  }));
};

const Footer = (props: Props) => {
  const {
    locale,
    eventsLabel,
    ourServicesFooter,
    aboutCrimsonFooter,
    ourServicesHeader,
    aboutCrimsonHeader,
    admissionsResourcesHeader,
    upcomingEventsLabel,
    followUsLabel,
    socialMediaIcons,
    sitemapLabel,
    termsOfUseLabel,
    privacyPolicyLabel,
    cookiePreferencesLabel,
    declarationLabel,
  } = props;
  const { hideNavFooter } = usePageContext();

  try {
    // const removeLocaleFromLinks = (link: string) => {
    //   try {
    //     if (!link?.trim()) {
    //       return "";
    //     }

    //     const localeRegex = /^[^/]+\//;
    //     return link.replace(localeRegex, "");
    //   } catch (error) {
    //     console.error("Error removing locale from link:", error);
    //     return link || "";
    //   }
    // };

    const ourServicesLinks = appendFooterLinksToNavigation(
      ourServicesHeader,
      ourServicesFooter,
    );
    const aboutCrimsonLinks = appendFooterLinksToNavigation(
      aboutCrimsonHeader,
      aboutCrimsonFooter,
    );

    const fullLinks = createLinks([
      ...ourServicesLinks,
      ...aboutCrimsonLinks,
      ...admissionsResourcesHeader,
    ]);

    // Push events into the end of the fullLinks array
    fullLinks.push({
      label: eventsLabel ?? "Events",
      id: "events",
      links: [
        {
          label: upcomingEventsLabel ?? "Upcoming Events",
          link: "/en/events",
          id: "upcoming-events",
        },
      ],
    });

    return (
      <footer className="bg-white !pt-[5px]">
        <Container className="!pt-0">
          <div className="border-t border-grays-G5 pt-4xl">
            {!hideNavFooter && (
              <nav className="relative md:grid md:grid-cols-2 md:gap-x-[1.88rem] md:gap-y-[1.875rem] lg:grid-cols-4 lg:gap-x-[0.94rem] xl:gap-x-[3.32rem]">
                {fullLinks.map((link, index) => {
                  return (
                    <div key={link.label + link.id}>
                      <FooterDisclosure label={link.label} items={link.links} />
                      {index === fullLinks.length - 1 && (
                        <div className="hidden pb-5xl md:block">
                          <SocialMediaSection
                            socialMediaIcons={socialMediaIcons}
                            followUsLabel={followUsLabel}
                          />
                        </div>
                      )}
                    </div>
                  );
                })}

                <div className="pb-5xl md:hidden">
                  <SocialMediaSection
                    socialMediaIcons={socialMediaIcons}
                    followUsLabel={followUsLabel}
                  />
                </div>
              </nav>
            )}

            <div className="flex flex-col md:flex-row md:items-center md:gap-5 lg:gap-[50px]">
              {/*  TODO: Locale selector implementation here */}
              <LocalePicker />
              <div className="pt-xl md:pt-0">
                <SmallPrintSection
                  sitemapLabel={sitemapLabel}
                  termsOfUseLabel={termsOfUseLabel}
                  cookiePreferencesLabel={cookiePreferencesLabel}
                  privacyPolicyLabel={privacyPolicyLabel}
                  declarationLabel={declarationLabel}
                  hideNavFooter={hideNavFooter}
                />
              </div>
            </div>
          </div>
        </Container>
      </footer>
    );
  } catch (err: any) {
    return (
      <>
        <p>SLUG: {locale}</p>
        <p>ERROR: {err}</p>
      </>
    );
  }
};

export default Footer;
