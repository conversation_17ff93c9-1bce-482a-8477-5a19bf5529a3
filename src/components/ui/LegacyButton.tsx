import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";
import type { TButtonColour } from "@/common/types";

const buttonVariants = cva(
  "cursor-pointer text-body2 font-bold px-[30px] py-[0.63rem] transition-all border border-solid border-transparent rounded-full inline-flex justify-center items-center gap-2 focus:outline-none focus:ring-0 min-w-[120px] max-w-[300px]",
  {
    variants: {
      colour: {
        salmon: "",
        navy: "",
        mulberry: "",
        yellow: "",
        white: "",
        indigo: "",
        black: "",
        charcoal: "",
        salmonCoral: "",
        orangeYellow: "",
        navyIndigo: "",
        indigoSalmon: "",
      },
      appearance: {
        outline: "",
        solid: "",
      },
      disabled: {
        true: "opacity-50 cursor-not-allowed",
        false: "",
      },
    },
    compoundVariants: [
      {
        colour: "salmon",
        appearance: "outline",
        class:
          "text-red-300 bg-transparent border-red-300 hover:bg-red-300 hover:text-white",
      },
      {
        colour: "salmon",
        appearance: "solid",
        class: "text-white bg-red-300 hover:bg-red-500",
      },
      {
        colour: "navy",
        appearance: "outline",
        class:
          "text-blue-700 border-blue-700 hover:bg-blue-700 hover:text-white",
      },
      {
        colour: "navy",
        appearance: "solid",
        class: "bg-blue-700 text-white hover:bg-blue-900",
      },
      {
        colour: "mulberry",
        appearance: "outline",
        class:
          "text-maroon-700 border-maroon-700 hover:bg-maroon-700 hover:text-white",
      },
      {
        colour: "mulberry",
        appearance: "solid",
        class: "bg-maroon-700 text-white hover:bg-purple-900",
      },
      {
        colour: "yellow",
        appearance: "outline",
        class:
          "text-yellow-500 border-yellow-500 hover:bg-yellow-500 hover:text-white",
      },
      {
        colour: "yellow",
        appearance: "solid",
        class: "bg-yellow-500 text-white hover:bg-yellow-700",
      },
      {
        colour: "white",
        appearance: "outline",
        class: "text-white border-white hover:bg-white hover:text-blue-700",
      },
      {
        colour: "white",
        appearance: "solid",
        class: "bg-white text-blue-700 hover:bg-grey-100",
      },
      {
        colour: "indigo",
        appearance: "outline",
        class:
          "text-blue-500 border-blue-500 hover:bg-blue-500 hover:text-white",
      },
      {
        colour: "indigo",
        appearance: "solid",
        class: "bg-blue-500 text-white hover:bg-blue-700",
      },
      {
        colour: "black",
        appearance: "outline",
        class: "text-black border-black hover:bg-black hover:text-white",
      },
      {
        colour: "black",
        appearance: "solid",
        class: "bg-black text-white hover:bg-blue-900",
      },
      {
        colour: "charcoal",
        appearance: "outline",
        class:
          "text-blue-900 border-blue-900 hover:bg-blue-900 hover:text-white",
      },
      {
        colour: "charcoal",
        appearance: "solid",
        class: "bg-blue-900 text-white hover:bg-blue-700",
      },
      {
        colour: "salmonCoral",
        appearance: "outline",
        class:
          "bg-transparent border-orange-500 text-orange-500 hover:bg-gradient-to-r hover:from-red-300 hover:to-orange-500 hover:text-white",
      },
      {
        colour: "salmonCoral",
        appearance: "solid",
        class:
          "bg-gradient-to-r from-red-300 to-orange-500 border-0 text-white hover:from-red-500 hover:to-red-500 hover:border-transparent",
      },
      {
        colour: "orangeYellow",
        appearance: "outline",
        class:
          "bg-transparent border-orange-500 text-yellow-500 hover:bg-gradient-to-r hover:from-orange-500 hover:to-yellow-500 hover:text-white hover:border-transparent",
      },
      {
        colour: "orangeYellow",
        appearance: "solid",
        class:
          "bg-gradient-to-r from-orange-500 to-yellow-500 border-0 text-white hover:from-red-500 hover:to-red-500",
      },
      {
        colour: "navyIndigo",
        appearance: "outline",
        class:
          "bg-transparent border-blue-700 text-blue-500 hover:bg-gradient-to-r hover:from-blue-700 hover:to-blue-500 hover:text-white hover:border-transparent",
      },
      {
        colour: "navyIndigo",
        appearance: "solid",
        class:
          "bg-gradient-to-r from-blue-700 to-blue-500 border-0 text-white hover:from-blue-700 hover:to-blue-700",
      },
      {
        colour: "indigoSalmon",
        appearance: "solid",
        class:
          "bg-gradient-to-r from-blue-500 to-red-300 border-0 text-white hover:from-red-500 hover:to-red-500",
      },
    ],
    defaultVariants: {
      colour: "salmon",
      appearance: "solid",
      disabled: false,
    },
  },
);

interface Props {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  appearance?: "solid" | "outline";
  colour?: TButtonColour;
  type?: "button" | "submit" | "reset";
  onClick?: () => void;
}

/**
 *
 * @deprecated Use the Button component instead
 */
const LegacyButton = ({
  className,
  disabled,
  appearance,
  colour,
  type = "button",
  ...props
}: Props) => (
  <button
    type={type}
    className={cn(buttonVariants({ colour, appearance, disabled }), className)}
    {...props}
  />
);

export default LegacyButton;
