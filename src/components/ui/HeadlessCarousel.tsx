// WARNING: Generally you SHOULD NOT use this component directly.
// Consider using the SlideCarousel component.

import { useEffect, useCallback, useState } from "react";
import { useWindowSize } from "@uidotdev/usehooks";

export interface HeadlessCarouselProps {
  containerRef: React.RefObject<HTMLDivElement>;
  children: React.ReactNode[];
  currentIndex: number;
}

const HeadlessCarousel = ({
  containerRef,
  children,
  currentIndex,
}: HeadlessCarouselProps) => {
  const updateScrollPosition = useCallback(() => {
    if (containerRef.current) {
      const cardElement = containerRef.current.firstElementChild as HTMLElement;
      if (cardElement) {
        const width = cardElement.offsetWidth;
        const cardStyle = window.getComputedStyle(cardElement);
        const gap = parseFloat(cardStyle.marginRight);

        containerRef.current.style.left = `${-(currentIndex * (width + gap))}px`;
      }
    }
  }, [currentIndex, containerRef]);

  // Update scroll position when currentIndex changes
  useEffect(() => {
    updateScrollPosition();
  }, [currentIndex, updateScrollPosition]);

  // Handle resize events
  useEffect(() => {
    window.addEventListener("resize", updateScrollPosition);
    return () => window.removeEventListener("resize", updateScrollPosition);
  }, [updateScrollPosition]);

  // Prevent overscroll behavior
  useEffect(() => {
    document.body.style.overscrollBehavior = "none";
    return () => {
      document.body.style.overscrollBehavior = "";
    };
  }, []);

  return children;
};

export default HeadlessCarousel;

export const useCardsIndex = (
  numCardsGenerator: (width: number) => number,
  numCards: number,
  paginateByPage = false,
) => {
  const { width } = useWindowSize();
  const [currentIndex, setCurrentIndex] = useState(0);
  const cardsPerView = numCardsGenerator(width ?? 0);

  const maxPages = Math.ceil(numCards / cardsPerView);
  const maxIndex = paginateByPage
    ? (maxPages - 1) * cardsPerView
    : Math.max(numCards - cardsPerView, 0);

  const currentPage = Math.floor(currentIndex / cardsPerView);

  const setCurrentPage = useCallback(
    (page: number | ((prev: number) => number)) => {
      setCurrentIndex((prevIndex) => {
        const targetPage =
          typeof page === "function"
            ? page(Math.floor(prevIndex / cardsPerView))
            : page;
        return targetPage * cardsPerView;
      });
    },
    [cardsPerView],
  );

  useEffect(() => {
    setCurrentIndex(Math.max(0, Math.min(currentIndex, maxIndex)));
  }, [currentIndex, maxIndex]);

  return {
    maxPages,
    currentIndex,
    setCurrentIndex,
    currentPage,
    setCurrentPage,
    maxIndex,
    cardsPerView,
    windowWidth: width,
  };
};

export const useWindowResize = (callback: (...args: any[]) => unknown) => {
  const { width, height } = useWindowSize();
  useEffect(() => {
    callback();
  }, [width, callback, height]);
};
