import { Theme } from "@/common/types";
import { cn } from "@/common/utils";
import { cva } from "class-variance-authority";

interface Props {
  className?: string;
  theme?: Theme | "transparent";
}

const themeStyles = cva("bg-muted animate-pulse rounded-md", {
  variants: {
    theme: {
      dark: "bg-white/10",
      light: "bg-secondary01-100/30",
      transparent: "bg-transparent",
    },
  },
});

export default function Skeleton({
  className,
  theme = "dark",
  ...props
}: Props) {
  return <div className={cn(themeStyles({ theme }), className)} {...props} />;
}
