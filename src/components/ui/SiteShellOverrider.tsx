"use client";

import { usePageContext } from "@/components/context/PageContext";
import { useEffect } from "react";

interface Props {
  title?: string;
  buttonText?: string;
  hideNavFooter?: boolean;
  navLogoOverride?: string;
}

/**
 * This component is used to override the site shell with custom values.
 * Things like the Bottom CTA Banner, Navbar, and Footer, mainly using page context.
 */

const SiteShellOverrider = ({
  title,
  buttonText,
  hideNavFooter,
  navLogoOverride,
}: Props) => {
  const { stickyBannerActions, navLogoOverrideActions, hideNavFooterActions } =
    usePageContext();

  useEffect(() => {
    if (title && buttonText) {
      stickyBannerActions?.update(title, buttonText);
    }

    if (hideNavFooter) {
      hideNavFooterActions?.update(hideNavFooter);
    }

    if (navLogoOverride) {
      navLogoOverrideActions?.update(navLogoOverride);
    }

    // Reset sticky banner when component unmounts
    // Because we don't want the changes to persist after the user leaves the page
    return () => {
      stickyBannerActions?.reset();
      hideNavFooterActions?.reset();
      navLogoOverrideActions?.reset();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return null;
};

export default SiteShellOverrider;
