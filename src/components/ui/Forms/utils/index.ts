import checkDuplicateStoryblokFields from "./checkDuplicateStoryblokFields";
import checkPartnershipStoryblokFields from "./checkPartnershipStoryblokFields";
import getFormFieldComponent from "./getFormFieldComponent";
import generateInputFieldProps from "./generateInputFieldProps";
import consolidateFormFields from "./consolidateFormFields";
import generateInitialValues from "./generateInitialValues";
import { prepareFormValuesForFormatting } from "./prepareFormValuesForFormatting";
import { handlePrefilledUserData } from "./handlePrefilledUserData";
import normalizeSchoolPrioritisationList from "./normalizeSchoolPrioritisationList";
import formatValuesForMarketo from "./formatValuesForMarketo";
import { getMarketoActivityData } from "./getMarketoActivityData";
import { formatToMarketoActivityData } from "./formatMarketoActivityData";
import { hasCorrectUserFormFields } from "./hasCorrectUserFormFields";
import { getFieldNamesBySection } from "./getFieldNamesBySection";
import { countVisibleFields } from "./countVisibleFields";

export {
  checkDuplicateStoryblokFields,
  checkPartnershipStoryblokFields,
  consolidateFormFields,
  formatToMarketoActivityData,
  formatValuesForMarketo,
  generateInitialValues,
  generateInputFieldProps,
  getFormFieldComponent,
  getMarketoActivityData,
  handlePrefilledUserData,
  hasCorrectUserFormFields,
  normalizeSchoolPrioritisationList,
  prepareFormValuesForFormatting,
  getFieldNamesBySection,
  countVisibleFields,
};
