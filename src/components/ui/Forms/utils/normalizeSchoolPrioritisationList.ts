const getSchoolValues = (stringOfValues: string) => stringOfValues.split(";");

interface ISchoolPrioritisationList {
  label?: string;
  value?: string;
}

const normalizeSchoolPrioritisationList = (
  prioritisationList: ISchoolPrioritisationList[],
) =>
  prioritisationList
    ?.map((item) => {
      if (!item?.value || !item?.label) {
        return;
      }
      return { label: item.label, value: getSchoolValues(item.value) };
    })
    .filter((item) => item) as { label: string; value: string[] }[];

export default normalizeSchoolPrioritisationList;
