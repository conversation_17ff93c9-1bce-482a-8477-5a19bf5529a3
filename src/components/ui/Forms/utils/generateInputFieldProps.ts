/** eslint-disable @typescript-eslint/no-unused-vars */
import { fieldNames, fieldTypes } from "@/common/constants";
import { IField } from "@/common/types";
import { IFieldPropParams, IFieldProps, IFormFieldComponent } from "../types";

const generateInputFieldProps = (
  FormFieldComponent: IForm<PERSON>ieldComponent,
  inputField: IField,
  rest: IFieldPropParams,
) => {
  const {
    touched = {},
    values = {},
    errors = {},
    theme,
    setFieldTouched,
    setFieldValue,
    schoolPrioritisation = "",
  } = rest;

  let fieldProps: IFieldProps = {};
  const validFieldTypes = [
    fieldTypes.TEXT_FIELD,
    fieldTypes.DROPDOWN_FIELD,
    fieldTypes.COUNTRY_FIELD,
    fieldTypes.TOGGLE_FIELD,
    fieldTypes.CITIZENSHIP_FIELD,
    fieldTypes.LANGUAGE_FIELD,
    fieldTypes.CGA_SUBJECT_INTEREST_FIELD,
    fieldTypes.AGE_FIELD,
    fieldTypes.CHECKBOX_FIELD,
    fieldTypes.GOOGLE_LOCATION_FIELD,
    fieldTypes.DIVISION_FIELD,
    fieldTypes.PRIMARY_INTEREST_FIELD,
    fieldTypes.WECHAT_ID,
    fieldTypes.REFERRAL_CODE,
    fieldTypes.TEXT_AREA_FIELD,
  ];

  if (validFieldTypes.includes(FormFieldComponent.name)) {
    const { component: nameOfField } = inputField;

    fieldProps = {
      nameOfField,
      theme,
      setFieldTouched,
      setFieldValue,
      touched: touched[nameOfField] ?? false,
      value: values[nameOfField] ?? "",
      error: errors[nameOfField] ?? "",
      ...inputField,
    };

    if (nameOfField === fieldNames.REMEMBER_ME_OPT_IN)
      fieldProps.style = "maroon";
    fieldProps.hasTooltip = true;

    if (nameOfField === fieldNames.PRIVACY_POLICY) {
      fieldProps.style = "maroon";
      fieldProps.renderMarkdown = true;
    }
  } else if (FormFieldComponent.name === "Partnership_School_Field") {
    const { component: nameOfField } = inputField;

    fieldProps = {
      nameOfField,
      theme,
      setFieldTouched,
      setFieldValue,
      filterValue: values?.partnership_name__c,
      touched: touched[nameOfField],
      value: values[nameOfField],
      error: errors[nameOfField],
      ...inputField,
    };
  } else if (FormFieldComponent.name === fieldTypes.PHONE_FIELD) {
    const localNumberFieldName = "localNumber";
    const countryCodeFieldName = "countryCode";
    const phoneNumberFieldName = "phoneNumber";

    const phoneValues: Record<string, string | number | undefined> = {
      [localNumberFieldName]: values[localNumberFieldName],
      [countryCodeFieldName]: values[countryCodeFieldName],
    };
    const phoneTouched: Record<string, boolean | undefined> = {
      [localNumberFieldName]: touched[localNumberFieldName],
      [countryCodeFieldName]: touched[countryCodeFieldName],
    };

    const phoneErrors: Record<string, string | undefined> = {
      [localNumberFieldName]: errors[localNumberFieldName],
      [countryCodeFieldName]: errors[countryCodeFieldName],
    };

    fieldProps = {
      values: phoneValues,
      touched: phoneTouched,
      errors: phoneErrors,
      localNumberFieldName,
      countryCodeFieldName,
      phoneNumberFieldName,
      theme,
      setFieldTouched,
      setFieldValue,
      ...inputField,
    };
  } else if (FormFieldComponent.name === fieldTypes.DATE_OF_BIRTH_FIELD) {
    const birthDayFieldName = "birthDay";
    const birthMonthFieldName = "birthMonth";
    const birthYearFieldName = "birthYear";

    const dobValues = {
      [birthDayFieldName]: values[birthDayFieldName],
      [birthMonthFieldName]: values[birthMonthFieldName],
      [birthYearFieldName]: values[birthYearFieldName],
    };
    const dobTouched = {
      [birthDayFieldName]: touched[birthDayFieldName],
      [birthMonthFieldName]: touched[birthMonthFieldName],
      [birthYearFieldName]: touched[birthYearFieldName],
    };

    const dobErrors = {
      [birthDayFieldName]: errors[birthDayFieldName],
      [birthMonthFieldName]: errors[birthMonthFieldName],
      [birthYearFieldName]: errors[birthYearFieldName],
    };

    fieldProps = {
      values: dobValues,
      setFieldTouched,
      setFieldValue,
      touched: dobTouched,
      errors: dobErrors,
      theme,
      birthDayFieldName,
      birthMonthFieldName,
      birthYearFieldName,
      ...inputField,
    };
  } else if (FormFieldComponent.name === fieldTypes.SCHOOL_PRIORITISATION) {
    const { component: nameOfField } = inputField;

    fieldProps = {
      nameOfField,
      theme,
      setFieldTouched,
      setFieldValue,
      schoolPrioritisation,
      touched: touched[nameOfField],
      error: errors[nameOfField],
      ...inputField,
    };
  } else {
    // ConditionalPhoneField, ConditionalTextField, and CommsOptInField
    fieldProps = {
      touched,
      values,
      errors,
      setFieldTouched,
      setFieldValue,
      theme,
      ...inputField,
    };
  }

  return fieldProps;
};

export default generateInputFieldProps;
