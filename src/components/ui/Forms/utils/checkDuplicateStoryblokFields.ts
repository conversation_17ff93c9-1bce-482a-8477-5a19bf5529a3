import { IField } from "@/common/types";

const checkDuplicateStoryblokFields = (inputFields: IField[]) => {
  const componentSet = new Set();
  const duplicateComponents: string[] = [];

  inputFields.forEach(({ component }) => {
    if (componentSet.has(component)) {
      duplicateComponents.push(component);
    } else {
      componentSet.add(component);
    }
  });

  return duplicateComponents;
};

export default checkDuplicateStoryblokFields;
