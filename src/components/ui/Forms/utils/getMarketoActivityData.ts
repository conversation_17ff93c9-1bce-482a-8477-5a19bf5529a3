import dayjs from "dayjs";

import {
  IMarketoLeadBody,
  INormalizedSchoolPrioritisationList,
} from "../types";
import { formatToMarketoActivityData } from "./formatMarketoActivityData";
import { hasCorrectUserFormFields } from "./hasCorrectUserFormFields";
import { getSlugWithoutLocale } from "@/common/utils";

export type IUserFormAttributes = Record<string, string>;

export const getMarketoActivityData = (
  marketoFormData: IMarketoLeadBody,
  currentLocale: string,
  buildEnv: string,
  fullSlug: string,
  schoolPrioritisationList: INormalizedSchoolPrioritisationList[],
  userFormAttributes: IUserFormAttributes,
  setUserFormAttributes: (userFormAttributes: IUserFormAttributes) => void,
) => {
  const activityList = [];
  const formValues = marketoFormData.input[0];

  if (formValues && "schoolPrioritisation" in formValues) {
    // check if they match
    const schoolPrioritisationFieldMatches = Boolean(
      schoolPrioritisationList.find(
        (schoolPrioritisation: INormalizedSchoolPrioritisationList) =>
          schoolPrioritisation.label === formValues.schoolPrioritisation,
      ),
    );

    const activity = formatToMarketoActivityData(
      formValues.email,
      schoolPrioritisationFieldMatches
        ? "SchoolPrioritisationFormMatch"
        : "SchoolPrioritisationForm",
      currentLocale,
      dayjs().toISOString(),
      buildEnv,
      getSlugWithoutLocale(fullSlug),
    );

    activityList.push(activity);
  }

  if (hasCorrectUserFormFields(userFormAttributes)) {
    const { featureType = "", location = "" } = userFormAttributes;

    const activity = formatToMarketoActivityData(
      formValues?.email,
      featureType,
      location,
      dayjs().toISOString(),
      buildEnv,
    );

    activityList.push(activity);
    setUserFormAttributes({});
  }

  const utmTerm = formValues?.MKT_UTM_Term__c ?? formValues?.mkt_utm_term__c;

  const utmMedium =
    formValues?.mkt_utm_medium__c ?? formValues?.MKT_UTM_Medium__c;

  if (utmMedium && utmTerm) {
    const activity = formatToMarketoActivityData(
      formValues?.email,
      "PerformanceMarketingAdIdSubmission",
      currentLocale,
      dayjs().toISOString(),
      buildEnv,
      getSlugWithoutLocale(fullSlug),
      utmMedium,
      utmTerm,
    );
    activityList.push(activity);
  }

  return activityList;
};
