import { string, bool, object, mixed, addMethod, array } from "yup";

addMethod(string, "email", function validateEmail(message: string) {
  const strongerEmailRegex =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return this.matches(strongerEmailRegex, message);
});

const emailValidation = (
  validationCondition: boolean,
  isRequiredValidation: boolean,
  isEmailValidation: boolean,
  isEmailErrorMessage: string,
  requiredErrorMessage: string,
) => {
  if (validationCondition) {
    if (isRequiredValidation && isEmailValidation) {
      return string()
        .required(requiredErrorMessage)
        .email(isEmailErrorMessage)
        .trim();
    } else if (isRequiredValidation) {
      return string().required(requiredErrorMessage).trim();
    } else if (isEmailValidation) {
      return string().email(isEmailErrorMessage).trim();
    }
  } else {
    return string().trim();
  }
};

const checkBoxValidation = (
  validationCondition: boolean,
  requiredErrorMessage: string,
) => {
  return validationCondition
    ? bool().oneOf([true], requiredErrorMessage)
    : bool();
};

const textValidation = (
  validationCondition: boolean,
  requiredErrorMessage: string,
) => {
  return validationCondition
    ? string().required(requiredErrorMessage)
    : string();
};

const textAreaValidation = (
  validationCondition: boolean,
  requiredErrorMessage: string,
  maxLength = 225,
  maxLengthErrorMessage?: string,
) => {
  const defaultMaxLengthMessage = `Maximum ${maxLength} characters allowed`;
  const message = maxLengthErrorMessage ?? defaultMaxLengthMessage;

  return validationCondition
    ? string().required(requiredErrorMessage).max(maxLength, message)
    : string().max(maxLength, message);
};

const stringArrayValidation = (
  validationCondition: boolean,
  requiredErrorMessage: string,
) => {
  return validationCondition
    ? array()
        .of(string().required(requiredErrorMessage))
        .min(1, requiredErrorMessage)
    : array().of(string());
};

const wechatIdValidation = (
  validationCondition: boolean,
  invalidErrorMessage = "Invalid format",
  requiredErrorMessage: string,
) => {
  let schema = string();

  if (validationCondition) {
    schema = schema.required(requiredErrorMessage);
  }

  return schema.test("isValidWechatId", invalidErrorMessage, (value) => {
    if (value === undefined || value === "") {
      return true;
    }
    const wechatIdRegex = /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/;
    return wechatIdRegex.test(value);
  });
};

const localNumberValidation = (
  validationCondition: boolean,
  isNumberErrorMessage: string,
  requiredErrorMessage: string,
) => {
  let schema = mixed();

  if (validationCondition) {
    schema = schema.required(requiredErrorMessage);
  }

  return schema
    .test("isNumber", isNumberErrorMessage, (value: any) => {
      // Deal with edge cases when one conditional phone field is hidden and not required
      // (Unfortunately `schema.transform()` won't help here)
      if (value === undefined) {
        return true;
      }

      return /^\d+$/.test(value as string);
    })
    .test(
      "noAllSameDigits",
      "${originalValue} is not allowed",
      (value: any) => !/^(\d)\1*$/.test(value as string),
    )
    .test(
      "no123456789",
      "${originalValue} is not allowed.",
      (value: any) => !/^123456789$/.test(value as string),
    );
};

const countryCodeValidation = (
  validationCondition: boolean,
  hasCountryCodeErrorMessage: string,
) => {
  return validationCondition
    ? object().shape({
        value: string().required(hasCountryCodeErrorMessage),
        label: string(),
      })
    : object();
};

const referralCodeValidation = (
  validationCondition: boolean,
  isInvalidErrorMessage: string,
  requiredErrorMessage: string,
) => {
  let schema = string();

  if (validationCondition) {
    schema = schema.required(
      requiredErrorMessage ?? "Referral code is required.",
    );
  }

  return schema.test(
    "isValidReferralCode",
    isInvalidErrorMessage || "Invalid format.",
    (value) => {
      if (value === undefined || value === "") {
        return true;
      }
      const referralCodePattern = /^CRMSN-[A-Za-z0-9]{9,}$/;
      return referralCodePattern.test(value);
    },
  );
};

export {
  checkBoxValidation,
  emailValidation,
  textValidation,
  textAreaValidation,
  countryCodeValidation,
  localNumberValidation,
  stringArrayValidation,
  wechatIdValidation,
  referralCodeValidation,
};
