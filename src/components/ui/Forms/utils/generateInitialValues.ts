import { fieldNames, fieldTypes } from "@/common/constants";
import { COUNTRY_CODES, FORM_FIELDS_MAP } from "../constants";
import { showCommsOptIn } from "@/common/utils";
import { IField } from "@/common/types";
import { IFormValues } from "../types";

const generateInitialValues = (
  inputFields: IField[],
  currentLocale = "",
  prefilledFormValues = "",
) => {
  const initialValues = {} as IFormValues;

  inputFields.forEach((field) => {
    const fieldName = field.component;
    const fieldType = FORM_FIELDS_MAP.get(fieldName)?.name;

    const fieldsThatCanBePrefilled = [
      fieldNames.PERSON_TYPE,
      fieldNames.PRIMARY_INTEREST,
      fieldNames.COUNTRY,
      fieldNames.PRIMARY_LANGUAGE,
      fieldNames.DIVISION,
    ];

    const shouldHideFieldAndSetValue =
      fieldsThatCanBePrefilled.includes(field.component) &&
      field.removeFieldAndSetValue &&
      field.removeFieldAndSetValue !== "Keep";

    if (shouldHideFieldAndSetValue) {
      initialValues[field.component] = field.removeFieldAndSetValue;
    } else if (fieldType === fieldTypes.CHECKBOX_FIELD) {
      initialValues[field.component] = false;
    } else if (field.component === fieldNames.PHONE) {
      if (field?.defaultPhoneCountryCode) {
        const defaultCountryCode = COUNTRY_CODES.find(
          (option) => option.value === field.defaultPhoneCountryCode,
        );

        initialValues.localNumber = "";
        initialValues.countryCode = defaultCountryCode ?? {
          value: "",
          label: "",
        };
      } else {
        initialValues.localNumber = "";
        initialValues.countryCode = { value: "", label: "" };
      }
    } else if (field.component === fieldNames.BIRTH_DATE) {
      initialValues.birthDay = "";
      initialValues.birthMonth = "";
      initialValues.birthYear = "";
    } else if (fieldType === fieldTypes.CONDITIONAL_PHONE_FIELD) {
      if (field?.defaultPhoneCountryCode) {
        const defaultCountryCode = COUNTRY_CODES.find(
          (option) => option.value === field.defaultPhoneCountryCode,
        );

        initialValues.studentLocalNumber = "";
        initialValues.studentCountryCode = defaultCountryCode ?? {
          value: "",
          label: "",
        };
        initialValues.guardianLocalNumber = "";
        initialValues.guardianCountryCode = defaultCountryCode ?? {
          value: "",
          label: "",
        };
      } else {
        initialValues.studentLocalNumber = "";
        initialValues.studentCountryCode = { value: "", label: "" };
        initialValues.guardianLocalNumber = "";
        initialValues.guardianCountryCode = { value: "", label: "" };
      }
    } else if (fieldType === fieldTypes.CONDITIONAL_TEXT_FIELD) {
      initialValues[field.guardianField.marketoField] = "";
      initialValues[field.studentField.marketoField] = "";
    } else if (fieldType === fieldTypes.COMMS_OPT_IN_FIELD) {
      initialValues[field.component] = !showCommsOptIn(currentLocale, "");
    } else if (fieldType === fieldTypes.CGA_SUBJECT_INTEREST_FIELD) {
      initialValues[field.component] = [];
    } else {
      initialValues[field.component] = "";
    }

    // Default the Remember me opt in to true for non-GDPR locales
    if (
      field.component === "rememberMeOptIn" &&
      !showCommsOptIn(currentLocale, "")
    ) {
      initialValues[field.component] = true;
    }
  });

  if (prefilledFormValues && Object.keys(prefilledFormValues).length > 0) {
    const prefilledFormValuesParsed = JSON.parse(
      prefilledFormValues,
    ) as IFormValues;

    const untickRememberMe = showCommsOptIn(
      currentLocale,
      prefilledFormValuesParsed?.country ?? "",
    );

    if (untickRememberMe) {
      prefilledFormValuesParsed.rememberMeOptIn = false;
    }

    return { ...initialValues, ...prefilledFormValuesParsed };
  }

  return initialValues;
};

export default generateInitialValues;
