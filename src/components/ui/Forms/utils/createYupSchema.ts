import * as Yup from "yup";
import { FORM_FIELDS_MAP } from "../constants";
import {
  checkBoxValidation,
  emailValidation,
  textValidation,
  textAreaValidation,
  countryCodeValidation,
  localNumberValidation,
  stringArrayValidation,
  wechatIdValidation,
  referralCodeValidation,
} from "./validationRules";
import { fieldTypes, fieldNames } from "@/common/constants";
import { IField } from "@/common/types";
import { IFormValues } from "../types";

const { number, object } = Yup;

const createYupSchema = (
  fields: IField[],
  values: IFormValues,
  isNumberErrorMessage: string,
  requiredErrorMessage: string,
  isEmailErrorMessage: string,
  hasCountryCodeErrorMessage: string,
  showCommsOptIn: (arg1: string, arg2: string) => boolean,
  currentLocale: string,
  isInvalidErrorMessage: string,
) => {
  const schema = fields.reduce((schema, field) => {
    const { component, validation, guardianValidation, studentValidation } =
      field;

    const isRequiredField = Boolean(
      validation && validation[0] === "isRequired",
    );

    const isGuardianPersonType = values && values.person_type__c === "Guardian";
    const isStudentPersonType = values && values.person_type__c === "Student";
    const studentFieldName = field?.studentField?.marketoField;
    const guardianFieldName = field?.guardianField?.marketoField;

    const formFieldType = FORM_FIELDS_MAP?.get(component)?.name;

    switch (formFieldType) {
      case fieldTypes.COMMS_OPT_IN_FIELD:
        if (showCommsOptIn(currentLocale, values?.country ?? "")) {
          return {
            ...schema,
            [component]: checkBoxValidation(
              isRequiredField,
              requiredErrorMessage,
            ),
          };
        } else {
          return {
            ...schema,
          };
        }
      case fieldTypes.CHECKBOX_FIELD:
        if (component === fieldNames.PRIVACY_POLICY) {
          return {
            ...schema,
            [component]: checkBoxValidation(true, requiredErrorMessage),
          };
        } else {
          return {
            ...schema,
            [component]: checkBoxValidation(
              isRequiredField,
              requiredErrorMessage,
            ),
          };
        }
      case fieldTypes.TEXT_FIELD:
      case fieldTypes.AGE_FIELD:
      case fieldTypes.DROPDOWN_FIELD:
      case fieldTypes.SCHOOL_PRIORITISATION:
      case fieldTypes.COUNTRY_FIELD:
      case fieldTypes.TOGGLE_FIELD:
      case fieldTypes.CITIZENSHIP_FIELD:
      case fieldTypes.LANGUAGE_FIELD:
      case fieldTypes.PARTNERSHIP_SCHOOL_FIELD:
      case fieldTypes.GOOGLE_LOCATION_FIELD:
      case fieldTypes.DIVISION_FIELD:
      case fieldTypes.PRIMARY_INTEREST_FIELD:
        if (component === fieldNames.EMAIL) {
          return {
            ...schema,
            [field.component]: emailValidation(
              true,
              true,
              true,
              isEmailErrorMessage,
              requiredErrorMessage,
            ),
          };
        } else if (component === fieldNames.YEAR_OF_GRADUATION) {
          return {
            ...schema,
            [component]: isRequiredField
              ? number()
                  .typeError(isNumberErrorMessage)
                  .required(requiredErrorMessage)
              : number().typeError(isNumberErrorMessage),
          };
        } else if (
          component === fieldNames.PERSON_TYPE ||
          component === fieldNames.COUNTRY ||
          component === fieldNames.PRIMARY_INTEREST ||
          component === fieldNames.FIRST_NAME ||
          component === fieldNames.LAST_NAME ||
          component === fieldNames.PARTNERSHIP_SCHOOL
        ) {
          return {
            [component]: textValidation(true, requiredErrorMessage),
            ...schema,
          };
        } else {
          return {
            [component]: textValidation(isRequiredField, requiredErrorMessage),
            ...schema,
          };
        }
      case fieldTypes.PHONE_FIELD:
        return {
          ...schema,
          countryCode: countryCodeValidation(true, hasCountryCodeErrorMessage),
          localNumber: localNumberValidation(
            true,
            isNumberErrorMessage,
            requiredErrorMessage,
          ),
        };
      case fieldTypes.CONDITIONAL_PHONE_FIELD:
        return {
          ...schema,
          studentLocalNumber: localNumberValidation(
            isGuardianPersonType ?? studentValidation?.includes("isRequired"),
            isNumberErrorMessage,
            requiredErrorMessage,
          ),
          studentCountryCode: countryCodeValidation(
            isGuardianPersonType ?? values.studentLocalNumber,
            hasCountryCodeErrorMessage,
          ),
          guardianLocalNumber: localNumberValidation(
            isStudentPersonType ?? guardianValidation?.includes("isRequired"),
            isNumberErrorMessage,
            requiredErrorMessage,
          ),
          guardianCountryCode: countryCodeValidation(
            isStudentPersonType ?? values.guardianLocalNumber,
            hasCountryCodeErrorMessage,
          ),
        };
      case fieldTypes.CONDITIONAL_TEXT_FIELD:
        if (component === fieldNames.CONDITIONAL_EMAIL) {
          return {
            ...schema,
            [studentFieldName]: emailValidation(
              isGuardianPersonType,
              guardianValidation[0] === "isRequired",
              guardianValidation[1] === "isValidEmail",
              isEmailErrorMessage,
              requiredErrorMessage,
            ),
            [guardianFieldName]: emailValidation(
              isStudentPersonType,
              studentValidation[0] === "isRequired",
              studentValidation[1] === "isValidEmail",
              isEmailErrorMessage,
              requiredErrorMessage,
            ),
          };
        } else {
          return {
            ...schema,
            [studentFieldName]: textValidation(
              isGuardianPersonType && studentValidation.includes("isRequired"),
              requiredErrorMessage,
            ),
            [guardianFieldName]: textValidation(
              isStudentPersonType && guardianValidation.includes("isRequired"),
              requiredErrorMessage,
            ),
          };
        }
      case fieldTypes.DATE_OF_BIRTH_FIELD:
        return {
          ...schema,
          birthDay: number().required(requiredErrorMessage),
          birthMonth: number().required(requiredErrorMessage),
          birthYear: number().required(requiredErrorMessage),
        };
      case fieldTypes.CGA_SUBJECT_INTEREST_FIELD:
        return {
          [component]: stringArrayValidation(
            isRequiredField,
            requiredErrorMessage,
          ),
          ...schema,
        };
      case fieldTypes.WECHAT_ID:
        return {
          ...schema,
          [component]: wechatIdValidation(
            isRequiredField,
            isInvalidErrorMessage,
            requiredErrorMessage,
          ),
        };
      case fieldTypes.REFERRAL_CODE:
        return {
          [component]: referralCodeValidation(
            isRequiredField,
            isInvalidErrorMessage,
            requiredErrorMessage,
          ),
          ...schema,
        };
      case fieldTypes.TEXT_AREA_FIELD:
        return {
          ...schema,
          [component]: textAreaValidation(
            isRequiredField,
            requiredErrorMessage,
          ),
        };
    }
    return schema;
  }, {});
  return object().shape(schema);
};

export default createYupSchema;
