import { IField } from "@/common/types";

/**
 * If the form contains the partnership_school__c field then the
 * partnership_name__c field is also required
 */
const checkPartnershipStoryblokFields = (inputFields: IField[]) => {
  const containsPartnershipSchoolField = !!inputFields.filter(
    ({ component }) => component === "Partnership_School__c",
  ).length;
  const containsPartnershipNameField = !!inputFields.filter(
    ({ component }) => component === "partnership_name__c",
  ).length;
  return containsPartnershipSchoolField && !containsPartnershipNameField;
};

export default checkPartnershipStoryblokFields;
