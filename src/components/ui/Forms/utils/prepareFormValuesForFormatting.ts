import { HAS_PREFILLED_DATA } from "../constants";
import { IFormValues } from "../types";
import { handlePrefilledUserData } from "./handlePrefilledUserData";

export const prepareFormValuesForFormatting = (
  formValues: IFormValues,
  disableRememberMeOptIn?: boolean,
) => {
  const formHasRememberMeOptIn = "rememberMeOptIn" in formValues;

  if (
    formValues.rememberMeOptIn &&
    formHasRememberMeOptIn &&
    !disableRememberMeOptIn
  ) {
    handlePrefilledUserData(formValues);
    formValues.storyblokRememberMyDetails = true;
  } else if (HAS_PREFILLED_DATA && formHasRememberMeOptIn) {
    localStorage.removeItem("prefilledUserData");
    formValues.storyblokRememberMyDetails = false;
  }

  if (!formHasRememberMeOptIn) {
    formValues.storyblokRememberMyDetails = false;
  }

  if (HAS_PREFILLED_DATA) {
    formValues.storyblokPrefill = true;
  } else {
    formValues.storyblokPrefill = false;
  }

  return formValues;
};
