import { IStoryblokLinkProps } from "@/common/types";

export const getSuccessPagePath = (
  sbLink: IStoryblokLinkProps[],
  fbEvent: string,
  currentLocale: string,
  alwaysNewTab = false,
  urlParams = "",
) => {
  const sbLinkToUse = sbLink[0];

  const thankYouPageSlug = fbEvent === "Lead" ? "ltypage" : "ctypage";

  // Add ?typage and facebook event as query param for tracking success page.
  if (sbLinkToUse?.link.cached_url)
    sbLinkToUse.link.cached_url += `?${thankYouPageSlug}`;

  if (sbLinkToUse?.link.url) sbLinkToUse.link.url += `?${thankYouPageSlug}`;

  if (sbLinkToUse?.link?.story?.url) {
    sbLinkToUse.link.story.url += `?${thankYouPageSlug}`;
  }

  let openInNewTab = alwaysNewTab ? "_blank" : "_self";

  if (sbLinkToUse?.link.linktype === "story") {
    const url = getInternalUrl(sbLinkToUse);
    const fullUrl = `${url}${urlParams}`;
    return {
      url: fullUrl,
      openInNewTab,
    };
  }

  const { url, target } = getExternalUrl(sbLinkToUse!, currentLocale);
  openInNewTab = target;

  return { url, openInNewTab };
};

export const getInternalUrl = (sbLink: IStoryblokLinkProps) => {
  const siteBaseUrl =
    process.env.NEXT_PUBLIC_SITE_URL ?? "https://www.crimsoneducation.org";

  // remove slashes from the end of the siteBaseUrl
  const siteBaseUrlWithoutSlashes = siteBaseUrl.replace(/\/$/, "");

  const {
    link: { cached_url, story },
  } = sbLink;

  if (story?.url) {
    return `${siteBaseUrlWithoutSlashes}/${story.url}`;
  }

  if (cached_url) {
    return `${siteBaseUrlWithoutSlashes}/${cached_url}`;
  }
};

export const getExternalUrl = (
  sbLink: IStoryblokLinkProps,
  currentLocale: string,
) => {
  const {
    link: { url },
    newTab,
  } = sbLink;

  const target = newTab ? "_blank" : "_self";
  const relation = newTab ? "noopener" : "";

  const appendHttp = (url: string) =>
    url.startsWith("http") ? url : `http://${url}`;

  // If the url is a v2 website path then just replace the locale tag with the current locale
  const fullUrl = url.startsWith("/")
    ? url.replace("<locale>", currentLocale)
    : appendHttp(url);
  return {
    url: fullUrl,
    target,
    relation,
  };
};
