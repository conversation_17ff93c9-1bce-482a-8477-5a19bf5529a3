const userFormFields = ["featureType", "location"];

type TUserFormAttributes = Record<string, string>;

export const hasCorrectUserFormFields = (
  userFormAttributes: TUserFormAttributes,
): boolean => {
  const formKeys = Object.keys(userFormAttributes);

  const eachKeyExists = formKeys.every((key) => userFormFields.includes(key));

  return eachKeyExists && formKeys.length === userFormFields.length;
};
