import {
  gtmEvents,
  triggerCustomEvent,
  triggerEnhancedConversionEvent,
  triggerFacebookConversionEvent,
} from "@/common/analytics";
import { IFacebookCAPIFields, IFormValues } from "../types";

export const handleAnalyticsTriggers = async (
  fbCapiFields: IFacebookCAPIFields,
  formValues: IFormValues,
) => {
  const { facebookEventId, facebookEventType } = fbCapiFields;

  triggerCustomEvent(gtmEvents.FORM_BUILDER_SUMMISSION, "submit");

  const gecTriggerType =
    facebookEventType === "Lead"
      ? gtmEvents.GEC_LEAD_TRIGGER
      : gtmEvents.GEC_CONTACT_TRIGGER;

  const phoneNumber =
    formValues?.countryCode?.value && formValues?.localNumber
      ? formValues?.countryCode?.value + formValues?.localNumber
      : "";

  triggerEnhancedConversionEvent(
    gecTriggerType,
    formValues?.email ?? "",
    phoneNumber,
  );

  triggerFacebookConversionEvent(
    gtmEvents.FB_FORM_TRIGGER,
    facebookEventId,
    facebookEventType === "Lead" ? "ltypage" : "ctypage",
  );
};
