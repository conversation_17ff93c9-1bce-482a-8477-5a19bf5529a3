import { IFieldSection } from "@/common/types";

export const getFieldNamesBySection = (fieldSections: IFieldSection[]) =>
  fieldSections.map((section) =>
    section.fields.flatMap((field) => {
      if (field.component === "phone") {
        return ["localNumber", "countryCode"];
      } else if (field.component === "birth_date__c") {
        return ["birthDay", "birthMonth", "birthYear"];
      } else {
        return field.component;
      }
    }),
  );
