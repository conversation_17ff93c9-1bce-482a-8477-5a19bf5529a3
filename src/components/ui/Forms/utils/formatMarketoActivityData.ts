const stagingActivityTypeId = 100004;
const productionActivityTypeId = 100017;

const activityTypeId: Record<string, number> = {
  development: stagingActivityTypeId,
  staging: stagingActivityTypeId,
  "staging-preview": stagingActivityTypeId,
  prod: productionActivityTypeId,
  "prod-preview": productionActivityTypeId,
};

export const createAttributeCategory = (
  apiName: string,
  name: string,
  value: string | number | undefined,
) => {
  return {
    apiName,
    name,
    value,
  };
};

export const formatToMarketoActivityData = (
  email: string | undefined,
  featureType: string,
  location: string,
  createdAtDate: string,
  buildEnv: string,
  assetName?: string | null,
  platformName = "",
  platformAdId = "",
) => {
  if (typeof window === "undefined") {
    return;
  }

  const activityDate = new Date();

  // TODO: add later -> 'tiktok', 'snap', 'ga_ppc'
  const supportedPlatformNames = ["fb_ig"];

  if (supportedPlatformNames.includes(platformName) && platformAdId) {
    const activityTypeId: Record<string, number> = {
      development: 100009,
      staging: 100009,
      "staging-preview": 100009,
      prod: 100020,
      "prod-preview": 100020,
    };

    const currentDate = new Date().toISOString();

    return {
      email,
      activityTypeId: activityTypeId[buildEnv],
      activityDate,
      primaryAttributeValue: platformAdId,
      attributes: [
        createAttributeCategory("platformName", "platformName", platformName),
        createAttributeCategory("createdAtDate", "createdAtDate", currentDate),
      ],
    };
  }

  return {
    email,
    activityTypeId: activityTypeId[buildEnv],
    activityDate,
    primaryAttributeValue: featureType,
    attributes: [
      createAttributeCategory("location", "Location", location),
      createAttributeCategory(
        "assetName",
        "Asset Name",
        assetName ?? window.location.pathname,
      ),
      createAttributeCategory(
        "createdAtDate",
        "Created At Date",
        createdAtDate,
      ),
    ],
  };
};
