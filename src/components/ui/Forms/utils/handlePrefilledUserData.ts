import { IFormValues } from "../types";

/**
 * Take the users form data and store it in their cache for the next time they visit the page.
 */
export function handlePrefilledUserData(userFormData: IFormValues) {
  // Exclude these fields from prefilled data
  /* eslint-disable @typescript-eslint/no-unused-vars */
  const {
    enquiry,
    enquiry__c,
    CGA_Subjects_Interest__c,
    school__c,
    schoolPrioritisation,
    location,
    ...rest
  } = userFormData;

  let oldUserData: IFormValues = {};
  const prefilledUserData = localStorage.getItem("prefilledUserData");

  if (prefilledUserData) {
    try {
      oldUserData = JSON.parse(prefilledUserData) as IFormValues;
    } catch (error) {
      console.error("Error parsing prefilled user data:", error);
    }
  }

  const newUserData = { ...oldUserData, ...rest };

  localStorage.setItem("prefilledUserData", JSON.stringify(newUserData));
}
