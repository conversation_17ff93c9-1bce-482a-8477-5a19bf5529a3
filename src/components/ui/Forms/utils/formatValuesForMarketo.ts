import { fieldNames } from "@/common/constants";
import { IFacebookCAPIFields, IFormValues } from "../types";
import { showCommsOptIn } from "@/common/utils";
import { getCookie } from "@/common/analytics";

const formatValuesForMarketo = (
  formValues: IFormValues,
  marketoProgrammeName: string,
  currentLocale: string,
  facebookCAPIFields: IFacebookCAPIFields,
  reasonForSubmission?: string,
) => {
  try {
    const commonMarketoFields = getCommonFields(formValues, currentLocale);
    const cookieMarketoFields = getCookieFields();

    return {
      munchkinId: getCookie("_mkto_trk"),
      marketoLeadBody: {
        programName: marketoProgrammeName,
        source: "Crimson Website",
        reason: reasonForSubmission ?? "Filled form builder form",
        lookupField: "Email",
        input: [
          {
            ...commonMarketoFields,
            ...cookieMarketoFields,
          },
        ],
        ...facebookCAPIFields,
      },
    };
  } catch (error) {
    const errorMessage = `FormHandler formatValuesForMarketo: ${JSON.stringify(error)}`;
    throw new Error(errorMessage);
  }
};

const getCommonFields = (formValues: IFormValues, currentLocale: string) => {
  const {
    birthDay,
    birthMonth,
    birthYear,
    commsOptIn,
    country,
    countryCode,
    email,
    guardianCountryCode,
    guardianLocalNumber,
    localNumber,
    parent_email__c,
    parent_first_name__c,
    parent_last_name__c,
    partnership_name__c,
    primary_interest__c,
    schoolPrioritisation,
    schoolYearGradeLevel,
    student_email__c,
    student_first_name__c,
    student_last_name__c,
    studentCountryCode,
    studentLocalNumber,
    division,
    location,
    referralCode,
    rememberMeOptIn, // eslint-disable-line
    weChatId,
    ...rest
  } = formValues;

  type IPrimaryInterestMap = Record<string, string>;

  const primaryInterestMap: IPrimaryInterestMap = {
    "University Admission Support": "University Admission Support",
    "Curriculum Tutoring": "Curriculum Tutoring",
    "Standardised Testing Tutoring": "Standardised Test Tutoring",
    "Med School Admission Support": "Med School Admission Support",
    "US Athletic Recruitment": "US Undergraduate Athletic Scholarships",
    "Educational Tours": "International Experiences - Tours",
    "US Boarding School Program": "US Boarding School Program",
    "Crimson Rise": "Crimson Rise",
    "Crimson Global Academy": "CGA",
    "Crimson App and Online Resources": "Crimson App and Online Resources",
  };

  let primaryInterest;

  if (primary_interest__c) {
    primaryInterest =
      primaryInterestMap[primary_interest__c] ?? primary_interest__c;
  }

  const leadObject = {
    [fieldNames.EMAIL]: email?.trim(),
    [fieldNames.COUNTRY]: country ?? location,
    [fieldNames.PRIMARY_INTEREST]:
      primaryInterest ?? division?.replace(/\(.*\)$/g, "") ?? "",
    ...rest,
  };

  if (showCommsOptIn(currentLocale, country ?? "")) {
    leadObject[fieldNames.UNSUBSCRIBED] = !commsOptIn;
  } else {
    leadObject[fieldNames.UNSUBSCRIBED] = false;
  }

  if (countryCode && localNumber) {
    leadObject[fieldNames.PHONE] = phoneNumber(countryCode, localNumber);
  }

  if (guardianCountryCode && guardianLocalNumber) {
    leadObject[fieldNames.PARENT_PHONE] = phoneNumber(
      guardianCountryCode,
      guardianLocalNumber,
    );
  }

  if (studentCountryCode && studentLocalNumber) {
    leadObject[fieldNames.STUDENT_PHONE] = phoneNumber(
      studentCountryCode,
      studentLocalNumber,
    );
  }

  if (birthDay && birthMonth && birthYear)
    leadObject[fieldNames.BIRTH_DATE] =
      `${birthDay}/${birthMonth}/${birthYear}`;

  if (student_first_name__c)
    leadObject[fieldNames.STUDENT_FIRST_NAME] = student_first_name__c;
  if (parent_first_name__c)
    leadObject[fieldNames.PARENT_FIRST_NAME] = parent_first_name__c;
  if (student_last_name__c)
    leadObject.student_last_name__c = student_last_name__c;
  if (parent_last_name__c) leadObject.parent_last_name__c = parent_last_name__c;
  if (parent_email__c) leadObject.parent_email__c = parent_email__c.trim();
  if (student_email__c) leadObject.student_email__c = student_email__c.trim();

  if (partnership_name__c) {
    leadObject[fieldNames.PARTNERSHIP_NAME] = partnership_name__c;
    leadObject[fieldNames.PARTNERSHIP] = "yes";
  }

  if (schoolPrioritisation) {
    leadObject[fieldNames.SCHOOL] = schoolPrioritisation;
  }

  if (schoolYearGradeLevel) {
    leadObject[fieldNames.SCHOOL_YEAR_GRADE_LEVEL] = schoolYearGradeLevel.slice(
      0,
      9,
    );
  }

  if (weChatId) {
    leadObject.wechat_open_id__c = weChatId;
  }

  if (referralCode) {
    leadObject.Referral_Code__c = referralCode;
  }

  return leadObject;
};

interface ICountryCode {
  value: string;
}

const phoneNumber = (countryCode: ICountryCode, localNumber: string) =>
  `${countryCode?.value}${localNumber}`.replace(/[() -]/g, "");

const getCookieFields = () => {
  return {
    [fieldNames.MKT_UTM_CAMPAIGN]: getCookie("x-crimson-utm_campaign"),
    [fieldNames.MKT_UTM_MEDIUM]:
      getCookie("x-crimson-utm_medium") ?? "organic_search",
    [fieldNames.MKT_UTM_SOURCE]: getCookie("x-crimson-utm_source"),
    [fieldNames.MKT_UTM_TERM]: getCookie("x-crimson-utm_term"),
    [fieldNames.MKT_UTM_CONTENT]: getCookie("x-crimson-utm_content"),
    [fieldNames.FBCLID]: getCookie("x-crimson-fbclid"),
    [fieldNames.GCLID]: getCookie("x-crimson-gclid"),
    fbp: getCookie("_fbp"),
  };
};

export default formatValuesForMarketo;
