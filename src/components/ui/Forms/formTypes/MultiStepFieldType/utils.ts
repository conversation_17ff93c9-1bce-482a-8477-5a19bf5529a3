import { fieldNames } from "@/common/constants";
import { IFieldSection } from "@/common/types";
import { countVisibleFields } from "../../utils";

// TODO: This function is a bit messy and could be refactored to be more readable. Can create separate helper functions for each check now that its getting more complex. Post milestone task - Christiane
export const checkMultiPageSectionsForErrors = (
  fieldSections: IFieldSection[],
) => {
  const errorList: string[] = [];

  const sectionLength = fieldSections.length;
  if (sectionLength < 2 || sectionLength > 3) {
    errorList.push(
      "There must be at least 2 field sections and no more than 3 for dynamic forms",
    );
    return errorList;
  }

  const firstSection = fieldSections[0]!;
  const firstSectionVisibleCount = countVisibleFields(firstSection.fields);
  if (firstSectionVisibleCount !== 4) {
    errorList.push("The first section must contain exactly 4 visible fields");
  }

  const requiredFirstFiveFields = [
    fieldNames.PERSON_TYPE,
    fieldNames.FIRST_NAME,
    fieldNames.LAST_NAME,
    fieldNames.EMAIL,
    fieldNames.PHONE,
  ];

  // Check that the first 5 fields across all field sections are in the correct order
  const allSections = fieldSections.flatMap((section) => section.fields);
  const firstFiveFields = allSections
    .slice(0, 5)
    .map((field) => field.component);

  for (let i = 0; i < 5; i++) {
    if (firstFiveFields[i] !== requiredFirstFiveFields[i]) {
      errorList.push(
        "The first 5 fields must be in order: Person Type, First Name, Last Name, Email, Phone Number",
      );
      break;
    }
  }

  const secondSection = fieldSections[1]!;
  const secondSectionVisibleCount = countVisibleFields(secondSection.fields);
  if (secondSectionVisibleCount > 5) {
    errorList.push(
      "The second section can only contain a maximum of 5 visible fields",
    );
  }

  // If there's a third section, check that the fields are at a max of 5
  if (sectionLength === 3) {
    const thirdSection = fieldSections[2]!;
    const thirdSectionVisibleCount = countVisibleFields(thirdSection.fields);
    if (thirdSectionVisibleCount > 5) {
      errorList.push(
        "The third section can only contain a maximum of 5 visible fields",
      );
    }
  }

  // Check that the last section of the form contains the privacy policy and comms opt in (if it exists) as the last 2 fields
  const lastSection = fieldSections[sectionLength - 1]!;
  const lastSectionFieldNames = lastSection.fields.map(
    (field) => field.component,
  );

  // Check if comms opt in exists in any section
  const commsOptInExists = fieldSections.some((section) => {
    return section.fields.some(
      (field) => field.component === fieldNames.COMMS_OPT_IN,
    );
  });

  // Determine the required last fields based on whether comms opt in exists
  const requiredLastFields = commsOptInExists
    ? [fieldNames.PRIVACY_POLICY, fieldNames.COMMS_OPT_IN]
    : [fieldNames.PRIVACY_POLICY];

  const lastFieldsCount = requiredLastFields.length;
  const actualLastFields = lastSectionFieldNames.slice(-lastFieldsCount);

  for (let i = 0; i < lastFieldsCount; i++) {
    if (actualLastFields[i] !== requiredLastFields[i]) {
      errorList.push(
        `The last ${lastFieldsCount} field(s) must be in this order: Privacy Policy and Contact Agreement`,
      );
      break;
    }
  }

  // Also check that privacy policy exists in the last section
  if (!lastSectionFieldNames.includes(fieldNames.PRIVACY_POLICY)) {
    errorList.push("The privacy policy must be on the last page of the form");
  }

  // Check that the last section has 3 or more visible fields to prevent insufficient multi-page layouts
  const lastSectionVisibleCount = countVisibleFields(lastSection.fields);
  if (lastSectionVisibleCount < 3) {
    errorList.push(
      "The last section must contain at least 3 visible fields. If the form doesn't have enough fields, use Single-section instead of Multi-step layout.",
    );
  }

  const requiredFields = [
    fieldNames.PERSON_TYPE,
    fieldNames.FIRST_NAME,
    fieldNames.LAST_NAME,
    fieldNames.EMAIL,
    fieldNames.PHONE,
    fieldNames.COUNTRY,
  ];

  // Check that the required fields are in any of the fieldSections
  requiredFields.forEach((field) => {
    const fieldInAnySection = fieldSections.some((section) => {
      const fieldInSection = section.fields.some(
        (fieldInSection) => fieldInSection.component === field,
      );
      return fieldInSection;
    });

    if (!fieldInAnySection) {
      errorList.push(`The field ${field} is required in the form`);
    }
  });

  return errorList;
};
