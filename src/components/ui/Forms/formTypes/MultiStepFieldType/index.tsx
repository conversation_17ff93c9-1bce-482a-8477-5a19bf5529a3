import { IFieldSection, Theme } from "@/common/types";
import React, { useEffect } from "react";
import { checkMultiPageSectionsForErrors } from "./utils";
import Alert from "@/components/ui/Alert";
import FormErrorMessage from "@/components/ui/FormErrorMessage";
import InputFieldGenerator from "../../InputFieldGenerator";
import { cn } from "@/common/utils";
import { useAtom } from "jotai";
import { currentDynamicPageAtom } from "../../atoms";

interface Props {
  formId: string;
  sections: IFieldSection[];
  theme: Theme;
  dynamicErrorLabel?: string;
  setShowHeaders?: (showHeaders: boolean) => void;
}

const MultiStepFieldType = (props: Props) => {
  const { sections, formId, theme, setShowHeaders } = props;
  const hasFormErrors = checkMultiPageSectionsForErrors(sections);
  const [currentDynamicPage] = useAtom(currentDynamicPageAtom);

  const currentFormPage = currentDynamicPage[formId] ?? 0;

  // Show headers only on the first page
  useEffect(() => {
    setShowHeaders?.(currentFormPage === 0);
  }, [currentFormPage, setShowHeaders]);

  if (hasFormErrors.length > 0) {
    return (
      <>
        <Alert theme="error">
          ERROR: The following errors need to be resolved for the form to be
          visible:
        </Alert>
        {hasFormErrors.map((error, index) => {
          return (
            <div key={index + error}>
              <FormErrorMessage isIndependent={true}>{error}</FormErrorMessage>
            </div>
          );
        })}
      </>
    );
  }

  return (
    <div className="min-h-[375px]">
      {sections.map((section, index) => {
        const { fields, _uid } = section;
        return (
          <div
            key={_uid + index}
            className={cn(
              "hidden gap-4",
              index === currentFormPage && "flex flex-col",
            )}
          >
            <InputFieldGenerator
              formId={formId}
              inputFields={fields}
              theme={theme}
            />
          </div>
        );
      })}
    </div>
  );
};

export default MultiStepFieldType;
