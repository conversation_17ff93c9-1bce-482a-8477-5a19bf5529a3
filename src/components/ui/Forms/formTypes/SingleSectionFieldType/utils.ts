import { IFieldSection } from "@/common/types";
import { fieldNames } from "@/common/constants";
import {
  checkDuplicateStoryblokFields,
  checkPartnershipStoryblokFields,
} from "../../utils";

// TODO same as the multi step field type, this function is a bit messy and could be refactored to be more readable. Will create separate helper functions for each check now that its getting more complex. Post milestone task - Christiane
export const checkSingleSectionForErrors = (singleSection: IFieldSection[]) => {
  let errorList: string[] = [];

  if (singleSection.length !== 1) {
    errorList.push("There must be exactly 1 section for this form type");
    return errorList;
  }

  const singleFieldSection = singleSection[0];

  if (!singleFieldSection) {
    errorList.push("There must be at least one section for this form type");
    return errorList;
  }

  const validateSectionOneResult = validateSectionOne(singleFieldSection);

  errorList = [...errorList, ...validateSectionOneResult];

  return errorList;
};

const validateSectionOne = (section: IFieldSection) => {
  const errorList: string[] = [];
  const fieldList = section?.fields;

  if (!fieldList || fieldList.length === 0) {
    errorList.push("There must be at least one field");
  }

  const requiredFirstFiveFields = [
    fieldNames.PERSON_TYPE,
    fieldNames.FIRST_NAME,
    fieldNames.LAST_NAME,
    fieldNames.EMAIL,
    fieldNames.PHONE,
  ];

  const firstFiveFields = fieldList.slice(0, 5).map((field) => field.component);

  for (let i = 0; i < 5; i++) {
    if (firstFiveFields[i] !== requiredFirstFiveFields[i]) {
      errorList.push(
        "The first 5 fields must be in order: Person Type, First Name, Last Name, Email, Phone Number",
      );
      break;
    }
  }

  const hasDuplicates = checkDuplicateStoryblokFields(fieldList);
  if (hasDuplicates.length > 0) {
    const message = `The following fields are duplicated in the form: ${hasDuplicates.join(
      ", ",
    )}`;
    errorList.push(message);
  }

  // The plan for now is to set the long form as the default (preset in storyblok),
  // but editors will be allowed to remove the fields that aren't required
  // (being phone and primary interest)
  const requiredSingleSectionFields = [
    fieldNames.PERSON_TYPE,
    fieldNames.FIRST_NAME,
    fieldNames.LAST_NAME,
    fieldNames.EMAIL,
    fieldNames.COUNTRY,
    fieldNames.PRIVACY_POLICY,
  ];

  const hasAllRequiredFields = requiredSingleSectionFields.every((field) =>
    fieldList.some(({ component }) => component === field),
  );

  if (!hasAllRequiredFields) {
    const missingFields = requiredSingleSectionFields.filter(
      (field) => !fieldList.some(({ component }) => component === field),
    );
    const message = `The following fields are required for this form type: ${missingFields.join(
      ", ",
    )}`;
    errorList.push(message);
  }

  const partnershipFieldsValid = checkPartnershipStoryblokFields(fieldList);
  if (partnershipFieldsValid) {
    const message =
      "Partnership school field requires the partnership name field to be added above it.";
    errorList.push(message);
  }

  const commsOptInExists = fieldList.some(
    (field) => field.component === fieldNames.COMMS_OPT_IN,
  );

  const requiredLastFields = commsOptInExists
    ? [fieldNames.PRIVACY_POLICY, fieldNames.COMMS_OPT_IN]
    : [fieldNames.PRIVACY_POLICY];

  // Check that the last 2 fields are in the correct order
  const lastTwoFields = fieldList.slice(-2).map((field) => field.component);
  const lastFieldsCount = requiredLastFields.length;

  if (fieldList.length < lastFieldsCount) {
    errorList.push(
      `The last ${lastFieldsCount} field(s) must be in this order: Privacy Policy and Contact Agreement`,
    );
  } else {
    const actualLastFields = lastTwoFields.slice(-lastFieldsCount);
    const isCorrectOrder = requiredLastFields.every(
      (field, index) => actualLastFields[index] === field,
    );

    if (!isCorrectOrder) {
      const expectedFields = requiredLastFields.join(" and ");
      errorList.push(
        `The last ${lastFieldsCount} field(s) must be: ${expectedFields}`,
      );
    }
  }

  return errorList;
};
