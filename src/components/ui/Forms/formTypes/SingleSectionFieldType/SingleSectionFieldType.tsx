import { IFieldSection, Theme } from "@/common/types";
import { checkSingleSectionForErrors } from "./utils";
import FormErrorMessage from "@/components/ui/FormErrorMessage";
import Alert from "@/components/ui/Alert";
import InputFieldGenerator from "../../InputFieldGenerator";

interface Props {
  theme: Theme;
  formId: string;
  sections: IFieldSection[];
}

const SingleSectionFieldType = (props: Props) => {
  const { theme, formId, sections = [] } = props;

  const hasStoryblokErrors = checkSingleSectionForErrors(sections);

  if (hasStoryblokErrors.length > 0) {
    return (
      <div className="flex flex-col gap-2">
        <Alert theme="error">
          ERROR: The following errors need to be resolved for the form to be
          visible:
        </Alert>
        {hasStoryblokErrors.map((error, index) => {
          return (
            <div key={index + error}>
              <FormErrorMessage isIndependent={true}>{error}</FormErrorMessage>
            </div>
          );
        })}
      </div>
    );
  }

  if (sections.length < 1 || !sections[0]) {
    return (
      <FormErrorMessage isIndependent={true}>No fields found</FormErrorMessage>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <InputFieldGenerator
        inputFields={sections[0].fields}
        theme={theme}
        formId={formId}
      />
    </div>
  );
};

export default SingleSectionFieldType;
