import { atom } from "jotai";

type TCurrentPage = Record<string, number>;

export const currentDynamicPageAtom = atom<TCurrentPage>({});
export const totalDynamicPagesAtom = atom<TCurrentPage>({});
export const canShowDynamicErrorAtom = atom<boolean>(false);

export const setCurrentDynamicPageAtom = (formId: string) =>
  atom(null, (get, set, pageNumber: number) => {
    const prev = get(currentDynamicPageAtom); // Get the current state
    set(currentDynamicPageAtom, {
      ...prev,
      [formId]: pageNumber, // Update only the specific formId's page number
    });
  });

export const setTotalDynamicPagesAtom = (formId: string) =>
  atom(null, (get, set, totalPages: number) => {
    const prev = get(totalDynamicPagesAtom); // Get the current state
    set(totalDynamicPagesAtom, {
      ...prev,
      [formId]: totalPages, // Update only the specific formId's page number
    });
  });
