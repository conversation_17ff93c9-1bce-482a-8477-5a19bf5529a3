import { useState } from "react";
import toast from "react-hot-toast";
import {
  IFacebookCAPIFields,
  IFormValues,
  INormalizedSchoolPrioritisationList,
} from "../types";
import { ISubmissionParams } from "@/common/types";
import useCSVParser from "@/common/hooks/useCSVParser";
import { getFacebookEventData } from "@/common/analytics";
import {
  formatValuesForMarketo,
  getMarketoActivityData,
  normalizeSchoolPrioritisationList,
  prepareFormValuesForFormatting,
} from "../utils";
import { usePathname } from "next/navigation";
import { useLocalStorage } from "@/common/hooks/useLocalStorageValues";
import { useRouter } from "next/navigation";
import { getSuccessPagePath } from "../utils/getSuccessPagePath";
import { usePageContext } from "@/components/context/PageContext";
import { handleAnalyticsTriggers } from "../utils/handleAnalyticsTriggers";
import {
  removeHoneypotFields,
  validateHoneypot,
} from "@/components/ui/FormInputFields/HoneypotFields";

const useSubmitForm = (schoolPrioritisationPath?: string) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();

  const { locale: currentLocale } = usePageContext();

  const prioritisationList = useCSVParser(schoolPrioritisationPath) ?? [];
  const schoolPrioritisationList: INormalizedSchoolPrioritisationList[] =
    normalizeSchoolPrioritisationList(prioritisationList);

  const [userFormAttributes, setUserFormAttributes] = useLocalStorage(
    "userFormAttributes",
    {},
  );

  const submitForm = async (
    formValues: IFormValues,
    submissionParams: ISubmissionParams,
  ) => {
    setIsLoading(true);

    try {
      const {
        fbEvent,
        marketoProgrammeName,
        successPage,
        reason: reasonForSubmission,
        fbEventIdPrefix,
        disableRememberMeOptIn,
      } = submissionParams;

      // Navigate to the success page
      const successPagePath = getSuccessPagePath(
        successPage,
        fbEvent,
        currentLocale,
      );

      if (!validateHoneypot(formValues)) {
        console.info("Validated...Navigating to the success page");
        router.push(successPagePath.url);
        return;
      }
      formValues = removeHoneypotFields(formValues);

      // Get event data for the Facebook Conversion API record
      const fbCapiFields: IFacebookCAPIFields = getFacebookEventData(
        fbEvent,
        fbEventIdPrefix,
      );

      // Add any external fields that need to be passed to marketo
      const preparedFormValues = prepareFormValuesForFormatting(
        formValues,
        disableRememberMeOptIn,
      );

      // Format the form values for the marketo API
      const marketoFormData = formatValuesForMarketo(
        preparedFormValues,
        marketoProgrammeName,
        currentLocale,
        fbCapiFields,
        reasonForSubmission,
      );

      const buildEnv = process.env.NEXT_PUBLIC_BUILD_ENV ?? "staging";

      // Check for any marketo activity data
      const marketoActivityData = getMarketoActivityData(
        marketoFormData?.marketoLeadBody,
        currentLocale,
        buildEnv,
        pathname,
        schoolPrioritisationList,
        userFormAttributes,
        setUserFormAttributes,
      );

      // Post the lead data to marketo
      const generalFormData = {
        ...marketoFormData,
        marketoActivityData,
      };

      // create a post request to /api/marketo to send the general form data
      await fetch("/api/marketo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(generalFormData),
      });

      // Trigger analytics for GTM, Segment and Facebook
      await handleAnalyticsTriggers(fbCapiFields, preparedFormValues);

      if (
        successPagePath.openInNewTab === "_blank" &&
        typeof window !== "undefined"
      ) {
        window.open(successPagePath.url, "_blank");
      } else {
        router.push(successPagePath.url);
      }
    } catch (error) {
      setIsLoading(false);
      toast.error(
        "Oops! Something went wrong while submitting the form. Please try again later.",
        {
          position: "bottom-right",
        },
      );
      console.error("Form Submission Error: ", error);
    }

    setIsLoading(false);
  };

  return { submitForm, isLoading };
};

export default useSubmitForm;
