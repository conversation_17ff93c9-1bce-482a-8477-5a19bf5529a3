import { IField } from "@/common/types";
import { useMemo } from "react";

export const useFieldsWithoutDefaults = (fields: IField[]) => {
  const inputFieldsWithoutDefaults = useMemo(
    () =>
      fields.filter(
        ({ removeFieldAndSetValue, component }) =>
          component === "phone" ||
          !removeFieldAndSetValue ||
          removeFieldAndSetValue === "Keep",
      ),
    [fields],
  );

  return inputFieldsWithoutDefaults;
};
