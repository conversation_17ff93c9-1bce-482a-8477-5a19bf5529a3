import * as yup from "yup";

import { consolidateFormFields } from "../utils";
import { useFieldsWithoutDefaults } from "./useFieldsWithoutDefaults";
import { IField, IFieldSection } from "@/common/types";
import { showCommsOptIn } from "@/common/utils";
import createYupSchema from "../utils/createYupSchema";
import { IFormValues } from "../types";

interface IErrorMessages {
  numberErrorMessage?: string;
  emailErrorMessage?: string;
  isRequiredErrorMessage?: string;
  countryCodeErrorMessage?: string;
  isInvalidErrorMessage?: string;
}

export const useYupSchema = (
  fieldSections: IFieldSection[],
  currentLocale: string,
  errorMessages: IErrorMessages,
) => {
  const inputFields: IField[] = consolidateFormFields(fieldSections);

  const {
    numberErrorMessage = "This field must be a number.",
    emailErrorMessage = "This field must be a valid email.",
    isRequiredErrorMessage = "This field is required.",
    countryCodeErrorMessage = "This field requires country code be added.",
    isInvalidErrorMessage = "Invalid format.",
  } = errorMessages;

  const fieldsWithoutDefaults = useFieldsWithoutDefaults(inputFields);

  const yupSchema = yup.lazy((values: IFormValues) => {
    return createYupSchema(
      fieldsWithoutDefaults,
      values,
      numberErrorMessage,
      isRequiredErrorMessage,
      emailErrorMessage,
      countryCodeErrorMessage,
      showCommsOptIn,
      currentLocale,
      isInvalidErrorMessage,
    );
  });

  return yupSchema;
};
