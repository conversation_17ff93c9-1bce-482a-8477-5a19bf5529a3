import { IFieldSection } from "@/common/types";
import { consolidateFormFields, generateInitialValues } from "../utils";
import { PREFILLED_DATA_EXCLUSIONS } from "../constants";
import { useLocalStorageValues } from "@/common/hooks/useLocalStorageValues";
import { usePageContext } from "@/components/context/PageContext";

export const useInitialFormValues = (
  formFields: IFieldSection[],
  disableRememberMeOptIn?: boolean,
) => {
  const { locale: currentLocale } = usePageContext();

  const consolidatedFields = consolidateFormFields(formFields);

  const defaultValues = generateInitialValues(
    consolidatedFields,
    currentLocale,
  );

  const [initialFormValues] = useLocalStorageValues(
    "prefilledUserData",
    defaultValues,
    PREFILLED_DATA_EXCLUSIONS,
  );

  return disableRememberMeOptIn ? defaultValues : initialFormValues;
};
