import { ISubmissionDetails, Theme } from "@/common/types";

type TJustify = "end" | "between" | "start" | "center";

export interface IFormControls extends ISubmissionDetails {
  formType: string;
  isLoading: boolean;
  fullWidth?: boolean;
  theme: Theme;
  fieldNamesBySection?: string[][];
  formId: string;
  justify?: TJustify;
}

export interface IFormControlContainerProps {
  isFullWidth?: boolean;
  isDynamic?: boolean;
  children: React.ReactNode;
  justify?: TJustify;
}
