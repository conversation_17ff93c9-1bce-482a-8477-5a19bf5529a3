import { cva } from "class-variance-authority";
import React from "react";
import { IFormControlContainerProps } from "./types";

const formControlContainerVariants = cva("flex", {
  variants: {
    isFullWidth: {
      true: "w-full",
      false: "w-auto",
    },
    justify: {
      end: "justify-end",
      between: "justify-between",
      start: "justify-start",
      center: "justify-center",
    },
    isDynamic: {
      true: "gap-3",
      false: "",
    },
  },
  defaultVariants: {
    isFullWidth: false,
    justify: "end",
    isDynamic: false,
  },
});

const FormControlContainer = ({
  isFullWidth,
  justify,
  isDynamic,
  children,
}: IFormControlContainerProps) => {
  return (
    <div
      className={formControlContainerVariants({
        isFullWidth,
        justify,
        isDynamic,
      })}
    >
      {children}
    </div>
  );
};

export default FormControlContainer;
