import FormControlContainer from "./FormControlContainer";
import {
  canShowDynamicErrorAtom,
  currentDynamicPageAtom,
  setCurrentDynamicPageAtom,
  totalDynamicPagesAtom,
} from "../atoms";
import { useAtom, useSetAtom } from "jotai";
import StepperDots from "./StepperDots";
import { useFormikContext } from "formik";
import Button from "@/components/ui/Button";
import { IFormControls } from "./types";

function FormControls(props: IFormControls) {
  const {
    nextButtonLabel,
    prevButtonLabel,
    submitButtonLabel,
    submittingLabel,
    formType,
    isLoading,
    fullWidth,
    justify,
    fieldNamesBySection = [],
    // theme = "dark", // will need this later, just not supported yet
    formId,
  } = props;

  const singleSubmitOptions = ["singleSection", "multiSection"];
  const { validateForm, errors, setFieldTouched } = useFormikContext();

  const [totalDynamicPages] = useAtom(totalDynamicPagesAtom);
  const [currentDynamicPage] = useAtom(currentDynamicPageAtom);
  const setCurrentDynamicPage = useSetAtom(setCurrentDynamicPageAtom(formId));

  const totalPages = totalDynamicPages[formId] ?? 0;
  const currentFormPage = currentDynamicPage[formId] ?? 0;

  const setRenderDynamicError = useSetAtom(canShowDynamicErrorAtom);

  const getFieldsToValidateOnPageChange = (currentPage: number) => {
    const fieldsToValidate = fieldNamesBySection[currentPage];

    if (fieldsToValidate) {
      fieldsToValidate.forEach((fieldName) => {
        setFieldTouched(fieldName, true).catch((err) => {
          console.error("Field touched error: ", err);
        });
      });

      validateForm().catch((err) => {
        console.error("Validate form error: ", err);
      });

      // check whether any of the fields in fieldsToValidate have errors
      const hasErrors = Object.keys(errors).some((fieldName) =>
        fieldsToValidate.includes(fieldName),
      );
      if (hasErrors) {
        return false;
      }
    }

    return true;
  };

  const onHandleNext = () => {
    // Check that the current page is fully valid before moving to the next page
    const allowNextPage = getFieldsToValidateOnPageChange(currentFormPage);

    if (currentFormPage < totalPages - 1 && allowNextPage) {
      setCurrentDynamicPage(currentFormPage + 1);
    }
  };

  const onHandlePrev = () => {
    if (currentFormPage > 0) setCurrentDynamicPage(currentFormPage - 1);
  };

  return (
    <>
      {singleSubmitOptions.includes(formType) && (
        <FormControlContainer isFullWidth={fullWidth} justify={justify}>
          <Button
            type="submit"
            disabled={isLoading}
            data-test-id={`submit-button`}
            theme="primary"
            colour="maroon"
          >
            {isLoading ? submittingLabel : submitButtonLabel}
          </Button>
        </FormControlContainer>
      )}

      {/* Multi section form */}
      {formType === "dynamic" && (
        <>
          <FormControlContainer justify={justify} isDynamic={true}>
            <div>
              {currentFormPage > 0 && (
                <Button
                  type="button"
                  onClick={onHandlePrev}
                  theme="secondary"
                  colour="maroon"
                >
                  {prevButtonLabel}
                </Button>
              )}
            </div>

            <div>
              {currentFormPage !== totalPages - 1 && (
                <Button
                  type="button"
                  onClick={onHandleNext}
                  theme="primary"
                  colour="maroon"
                >
                  {nextButtonLabel}
                </Button>
              )}

              {currentFormPage === totalPages - 1 && (
                <Button
                  type="submit"
                  theme="primary"
                  colour="maroon"
                  disabled={isLoading}
                  onClick={() => setRenderDynamicError(true)}
                >
                  {isLoading ? submittingLabel : submitButtonLabel}
                </Button>
              )}
            </div>
          </FormControlContainer>

          <StepperDots count={totalPages} activeIndex={currentFormPage} />
        </>
      )}
    </>
  );
}

export default FormControls;
