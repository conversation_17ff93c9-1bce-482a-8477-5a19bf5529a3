import { cn } from "@/common/utils";
import React from "react";

type Props = {
  count: number;
  activeIndex: number;
};

const StepperDots = (props: Props) => {
  const { count = 3, activeIndex } = props;
  return (
    <div className="flex justify-center gap-2">
      {Array.from({ length: count }).map((_, index) => {
        const isActive = index === activeIndex;
        return (
          <div
            key={index}
            className={cn(
              "h-1 w-14 rounded-full bg-grays-G5 transition-all duration-200 ease-in-out",
              isActive && "bg-primary01-75",
            )}
          />
        );
      })}
    </div>
  );
};

export default StepperDots;
