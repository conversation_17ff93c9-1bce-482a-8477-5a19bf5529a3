import Text from "../Text";

type Props = {
  heading?: string;
  subheading?: string;
};

export default function FormHeaders({ heading, subheading }: Props) {
  return (
    <>
      {heading && (
        <Text tag="h3" style="mh1.5" mdStyle="h3" className="mb-md">
          {heading}
        </Text>
      )}
      {subheading && (
        <Text tag="p" style="mb1" mdStyle="b2">
          {subheading}
        </Text>
      )}
    </>
  );
}
