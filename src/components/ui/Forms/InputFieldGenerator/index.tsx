import { IField, Theme } from "@/common/types";
import { useFormikContext } from "formik";
import { useFieldsWithoutDefaults } from "@/components/ui/Forms/hooks/useFieldsWithoutDefaults";
import { IFieldPropParams, IFormValues } from "../types";
import { generateInputFieldProps, getFormFieldComponent } from "../utils";

interface Props {
  theme?: Theme;
  formId: string;
  inputFields: IField[];
}

const InputFieldGenerator = ({
  theme = "light",
  formId,
  inputFields,
}: Props) => {
  const { values, errors, touched, setFieldTouched, setFieldValue } =
    useFormikContext<IFormValues>();

  const fieldsToRender = useFieldsWithoutDefaults(inputFields);

  const fieldPropParams: IFieldPropParams = {
    errors,
    values,
    touched,
    setFieldTouched,
    setFieldValue,
    theme,
    // schoolPrioritisation: settingsContent?.schoolPrioritisation,
  };

  return (
    <>
      {fieldsToRender.map((field) => {
        const { component: nameOfField } = field;
        const FormFieldComponent = getFormFieldComponent(field);

        if (FormFieldComponent?.Component) {
          const fieldProps = generateInputFieldProps(
            FormFieldComponent,
            field,
            fieldPropParams,
          );

          return (
            <FormFieldComponent.Component
              key={nameOfField}
              formId={formId}
              {...fieldProps}
            />
          );
        }

        return null;
      })}
    </>
  );
};

export default InputFieldGenerator;
