import { Theme } from "@/common/types";
import { FormikErrors, FormikTouched, FormikValues } from "formik";

export interface IFormFieldComponent {
  name: string;
  Component?: React.ComponentType<any> | React.FunctionComponent<any>;
  key?: string;
  formId?: string;
}

export type TDynamicFormValues = Record<
  string,
  string | number | boolean | object | null | undefined | (() => void)
>;

export interface IFormValues extends TDynamicFormValues {
  email?: string;
  rememberMeOptIn?: boolean;
  country?: string;
  person_type__c?: string;
  partnership_name__c?: string;
  countryCode?: {
    value: string;
    label: string;
  };
  localNumber?: string;
  storyblokRememberMyDetails?: boolean;
  storyblokPrefill?: boolean;
  guardianCountryCode?: {
    value: string;
    label: string;
  };
  guardianLocalNumber?: string;
  studentCountryCode?: {
    value: string;
    label: string;
  };
  studentLocalNumber?: string;
  primary_interest__c?: string;
  birthDay?: string;
  birthMonth?: string;
  birthYear?: string;
  parent_email__c?: string;
  student_email__c?: string;
  schoolYearGradeLevel?: string;
  division?: string;
  mkt_utm_medium__c?: string;
  mkt_utm_term__c?: string;
  MKT_UTM_Medium__c?: string;
  MKT_UTM_Term__c?: string;
}

export interface IMarketoLeadBody extends IFacebookCAPIFields {
  programName: string;
  source: string;
  reason: string;
  lookupField: string;
  input: IFormValues[];
}

export interface IMarketoSubmission {
  munchkinId: string;
  marketoLeadBody: IMarketoLeadBody;
}

export interface IFieldPropParams extends IFormValues {
  theme?: Theme;
  schoolPrioritisation?: string;
  setFieldTouched?: (
    field: string,
    isTouched?: boolean,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
  setFieldValue?: (
    field: string,
    value: any,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
  touched?: FormikTouched<IFormValues>;
  errors?: FormikErrors<IFormValues>;
  values?: FormikValues;
}

type OptionalIFieldProps = Partial<IFieldPropParams>;

export interface IFieldProps
  extends Omit<OptionalIFieldProps, "touched | errors | values"> {
  hasTooltip?: boolean;
  touched?:
    | boolean
    | Record<string, string>
    | Record<string, boolean | undefined>;
  errors?: string | Record<string, string | undefined>;
  error?: string | Record<string, string>;
  values?: string | Record<string, string | number | undefined>;
  value?: TDynamicFormValues | Record<string, string>;
  nameOfField?: string;
  theme?: Theme;
  schoolPrioritisation?: string;
  localNumberFieldName?: string;
  countryCodeFieldName?: string;
  phoneNumberFieldName?: string;
  filterValue?: string;
  birthDayFieldName?: string;
  birthMonthFieldName?: string;
  birthYearFieldName?: string;
  setFieldTouched?: (
    field: string,
    isTouched?: boolean,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
  setFieldValue?: (
    field: string,
    value: any,
    shouldValidate?: boolean,
  ) => Promise<void | FormikErrors<IFormValues>>;
}

export interface IFormFieldComponent {
  name: string;
  Component?: React.ComponentType<any> | React.FunctionComponent<any>;
  key?: string;
  formId?: string;
}

export interface IFacebookCAPIFields {
  facebookEventType: string;
  facebookEventId: string;
  facebookEventSourceUrl: string;
  clientUserAgent: string;
}

export interface INormalizedSchoolPrioritisationList {
  label?: string;
  value?: string[];
}
