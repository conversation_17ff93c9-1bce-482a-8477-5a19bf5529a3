"use client";

import { Formik, Form as FormikForm } from "formik";
import { IForm, ISubmissionParams } from "@/common/types";
import SingleSectionFieldType from "./formTypes/SingleSectionFieldType/SingleSectionFieldType";
import FormControls from "./FormControls";
import { useInitialFormValues } from "./hooks/useInitialValues";
import { useYupSchema } from "./hooks/useYupSchema";
import { IFormValues } from "./types";
import useSubmitForm from "./hooks/useSubmitForm";
import MultiStepFieldType from "./formTypes/MultiStepFieldType";
import { useEffect, useState } from "react";
import { useSetAtom } from "jotai";
import { setTotalDynamicPagesAtom } from "./atoms";
import { getFieldNamesBySection } from "./utils";
import { usePageContext } from "@/components/context/PageContext";
import HoneypotFields, {
  getHoneypotInitialValues,
} from "@/components/ui/FormInputFields/HoneypotFields";

interface Props {
  form: IForm[];
  formId: string;
  reason?: string;
  fbEventIdPrefix?: string;
  disableRememberMeOptIn?: boolean;
  setShowHeaders?: (showHeaders: boolean) => void;
}

const Form = (props: Props) => {
  const {
    form,
    formId = "",
    reason,
    fbEventIdPrefix,
    disableRememberMeOptIn = false,
    setShowHeaders,
  } = props;

  // const { googleMapsApiKey } = useSiteConfig();
  const setTotalDynamicPages = useSetAtom(setTotalDynamicPagesAtom(formId));

  const { locale } = usePageContext();

  const {
    type = "singleSection",
    submissionDetails = [],
    fieldSections = [],
    numberErrorMessage,
    isRequiredErrorMessage,
    emailErrorMessage,
    countryCodeErrorMessage,
    fbEvent,
    marketoProgrammeName,
    dynamicErrorLabel = "Looks like you are missing some fields:",
    isInvalidErrorMessage,
    theme = "light",
  } = form[0]!;

  const initialValues: IFormValues = useInitialFormValues(
    fieldSections,
    disableRememberMeOptIn,
  );

  const validationSchema = useYupSchema(fieldSections, locale, {
    numberErrorMessage,
    isRequiredErrorMessage,
    emailErrorMessage,
    countryCodeErrorMessage,
    isInvalidErrorMessage,
  });

  const submissionParams: ISubmissionParams = {
    successPage: submissionDetails[0]?.successPage ?? [],
    fbEvent,
    marketoProgrammeName,
    reason,
    fbEventIdPrefix,
    disableRememberMeOptIn,
  };

  const { submitForm, isLoading } = useSubmitForm();

  // const isLocationVisible = !LOCALES_WITHOUT_LOCATION.includes(currentLocale);
  const showForm = fieldSections?.length > 0;

  useEffect(() => {
    if (type === "dynamic") {
      setTotalDynamicPages(fieldSections.length);
    }
  }, [fieldSections, setTotalDynamicPages, type]);

  const fieldNamesBySection =
    type === "dynamic" && fieldSections.length > 1
      ? getFieldNamesBySection(fieldSections)
      : [];

  return (
    <Formik
      initialValues={{
        ...getHoneypotInitialValues(),
        ...initialValues,
      }}
      initialErrors={{
        person_type__c: "error",
      }}
      validationSchema={validationSchema}
      onSubmit={(values) => submitForm(values, submissionParams)}
      enableReinitialize
    >
      {() => {
        return (
          <FormikForm className="grid grid-cols-1 gap-3xl">
            <HoneypotFields />
            {showForm && type === "singleSection" && (
              <SingleSectionFieldType
                theme={theme}
                sections={fieldSections}
                formId={formId}
              />
            )}

            {showForm && type === "dynamic" && (
              <MultiStepFieldType
                theme={theme}
                sections={fieldSections}
                formId={formId}
                dynamicErrorLabel={dynamicErrorLabel}
                setShowHeaders={setShowHeaders}
              />
            )}

            {/* Form Submissions */}
            {submissionDetails?.length > 0 && (
              <FormControls
                formType={type}
                isLoading={isLoading}
                fullWidth={type === "singleSection"}
                justify={type === "dynamic" ? "between" : "end"}
                theme={theme}
                fieldNamesBySection={fieldNamesBySection}
                formId={formId}
                {...submissionDetails[0]}
              />
            )}
          </FormikForm>
        );
      }}
    </Formik>
  );
};

export default Form;
