"use client";

import { cn } from "@/common/utils";
import { useEffect, useState, useRef } from "react";
import { testConnectionSpeed } from "./utils";

type Props = {
  src: string;
};

export default function NextVideoWrapper({ src }: Props) {
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [shouldLoadVideo, setShouldLoadVideo] = useState(false);
  const isMountedRef = useRef(false);

  const handleVideoLoaded = () => {
    setIsVideoLoading(false);
  };

  useEffect(() => {
    let timeout: NodeJS.Timeout | undefined;

    isMountedRef.current = true;

    const checkConnectionAndLoadVideo = async () => {
      let connectionIsFast = false;

      interface INavigatorConnection {
        effectiveType: string;
        downlink: number;
      }

      // First try the Network Information API (currently only fully supported in Chrome)
      if ("connection" in navigator && navigator.connection) {
        const connection = navigator.connection as INavigatorConnection;

        const slowConnectionTypes = ["slow-2g", "2g", "3g"];

        const isSlow =
          slowConnectionTypes.includes(connection.effectiveType) ||
          (connection.downlink !== undefined && connection.downlink < 0.5);

        connectionIsFast = !isSlow;
      } else {
        // Fallback to the connection/speed test for unsupported browsers
        connectionIsFast = await testConnectionSpeed();
      }

      if (isMountedRef.current && connectionIsFast) {
        timeout = setTimeout(() => {
          if (isMountedRef.current) {
            setShouldLoadVideo(true);
          }
        }, 3000);
      }
    };

    void checkConnectionAndLoadVideo();

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, []);

  return (
    <>
      {shouldLoadVideo && (
        <video
          className={cn(
            "absolute inset-0 size-full object-cover transition-opacity duration-300 z-[1]",
            isVideoLoading ? "opacity-0" : "opacity-100",
          )}
          muted
          loop
          playsInline
          preload="auto"
          autoPlay
          onLoadedData={handleVideoLoaded}
        >
          <source src={src} type="video/mp4" />
        </video>
      )}
    </>
  );
}
