export const testConnectionSpeed = () => {
  const testDownloadSize = 1024;
  const speedThreshold = 6000;

  return new Promise<boolean>((resolve) => {
    const startTime = new Date().getTime();

    // In this case I just use a random placeholder test image from an open source service
    const img = new Image();
    const randomSize = testDownloadSize * 1024;
    img.src = `https://picsum.photos/200/300?random=${startTime}&bytes=${randomSize}`;

    img.onload = () => {
      const endTime = new Date().getTime();
      const duration = (endTime - startTime) / 1000; // in seconds
      const bitsLoaded = testDownloadSize * 8; // in kilobits
      const speedKbps = bitsLoaded / duration;
      console.log("Speed test result", speedKbps, " / ", speedThreshold);
      resolve(speedKbps > speedThreshold);
    };

    img.onerror = () => {
      // Assume the connection is slow on a failure
      console.error("Speed test failed");
      resolve(false);
    };

    // If the image takes more than 5 seconds to load, just assume its a slow connection
    setTimeout(() => {
      console.error("Speed test timed out");
      resolve(false);
    }, 5000);
  });
};
