import { cn } from "@/common/utils";
import { ButtonColour, ButtonTheme, IStoryblokLink } from "@/common/types";
import GeneralLink from "@/components/bloks/GeneralLink";
import { cva } from "class-variance-authority";
import Text from "@/components/ui/Text";
import type { ButtonHTMLAttributes } from "react";

const buttonVariants = cva(
  "rounded px-[1.88rem] py-md leading-[18px] fit-content border hover:shadow-[0px_5px_10px_rgba(0,0,0,0.2)] transition-all duration-150 min-w-[6.25rem] max-w-[17.625rem]",
  {
    variants: {
      colour: {
        red: "",
        navy: "",
        maroon: "",
        white: "",
        darkGrey: "",
      },
      theme: {
        primary: "border-transparent text-white",
        secondary: "bg-transparent hover:text-white",
        link1: "hover:shadow-none px-0 text-left",
        link2: "hover:shadow-none px-0 underline text-left",
        icon: "flex p-0 min-w-[42px] size-[42px] items-center justify-center rounded-[4px] border border-neutral01-75 hover:shadow-none",
      },
      disabled: {
        true: "cursor-not-allowed",
        false: "cursor-pointer",
      },
    },
    compoundVariants: [
      // Icon
      {
        colour: "maroon",
        theme: "icon",
        className: "border border-neutral01-75",
      },
      {
        colour: "white",
        theme: "icon",
        className: "border border-white",
      },
      // Primary
      {
        colour: "red",
        theme: "primary",
        className: "bg-primary01-50 hover:bg-primary01-75",
      },
      {
        colour: "maroon",
        theme: "primary",
        className:
          "bg-primary01-75 hover:bg-primary01-100 active:bg-red-950 active:border-red-950",
      },
      {
        colour: "navy",
        theme: "primary",
        className: "bg-secondary01-75 hover:bg-secondary01-100 ",
      },
      {
        colour: "white",
        theme: "primary",
        className:
          "bg-white text-primary01-50 hover:text-primary01-75 active:bg-grey-150 ",
      },
      {
        colour: "darkGrey",
        theme: "primary",
        className:
          "bg-grays-G1 text-white hover:bg-primary01-75 active:bg-primary01-50",
      },
      // Secondary
      {
        colour: "red",
        theme: "secondary",
        className:
          "border-primary01-50 text-primary01-50 hover:bg-primary01-50 active:bg-red-400 active:border-red-400",
      },
      {
        colour: "maroon",
        theme: "secondary",
        className:
          "border-primary01-75 text-primary01-75 hover:bg-primary01-75 active:bg-red-800 active:border-red-800",
      },
      {
        colour: "navy",
        theme: "secondary",
        className:
          "border-secondary01-75 text-secondary01-75 hover:bg-secondary01-75 ",
      },
      {
        colour: "white",
        theme: "secondary",
        className:
          "border-white text-white hover:text-primary01-50 hover:bg-white active:bg-grey-150 ",
      },
      // Disabled State
      {
        disabled: true,
        theme: "secondary",
        className:
          "text-white border-neutral01-25 bg-transparent hover:bg-transparent",
      },
      {
        disabled: true,
        theme: "primary",
        className:
          "bg-grays-G5 text-white hover:text-white hover:bg-grays-G5 active:bg-grays-G5",
      },
      {
        disabled: true,
        theme: "icon",
        className: "opacity-25 border-gray-G3",
      },
      // Link1
      {
        colour: "maroon",
        theme: "link1",
        className:
          "text-primary01-75 hover:text-primary01-50 border-transparent",
      },
    ],
    defaultVariants: {
      colour: "red",
      theme: "primary",
    },
  },
);

interface Props extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  link?: IStoryblokLink;
  isNew?: boolean;
  colour?: ButtonColour;
  theme?: ButtonTheme;
  disabled?: boolean;
  className?: string;
  openInNewTab?: boolean;
  targetAnchorId?: string;
  onClick?: () => void;
  type?: "submit" | "reset" | "button";
}

const ButtonComponent = ({
  children,
  className,
  onClick,
  type,
  disabled,
}: {
  children: React.ReactNode;
  className: string;
  onClick?: () => void;
  type: "submit" | "reset" | "button";
  disabled?: boolean;
}) => {
  return (
    <button className={className} onClick={onClick} type={type ?? "button"} disabled={disabled} >
      <Text tag="span" style="button">
        {children}
      </Text>
    </button>
  );
};

const Button = ({
  children,
  link,
  openInNewTab = false,
  colour = "red",
  theme = "primary",
  disabled = false,
  className,
  onClick,
  targetAnchorId,
  type,
}: Props) => {
  if (link || targetAnchorId) {
    return (
      <GeneralLink
        blok={{
          link: link ?? {
            url: "",
            linktype: "",
            cached_url: "",
          },
          newTab: openInNewTab,
          targetAnchorId,
        }}
      >
        <ButtonComponent
          type={type ?? "button"}
          disabled={disabled}
          className={cn(className, buttonVariants({ colour, theme, disabled }))}
        >
          {children}
        </ButtonComponent>
      </GeneralLink>
    );
  }

  return (
    <ButtonComponent
      onClick={onClick}
      type={type ?? "button"}
      disabled={disabled}
      className={cn(className, buttonVariants({ colour, theme, disabled }))}
    >
      {children}
    </ButtonComponent>
  );
};

export default Button;
