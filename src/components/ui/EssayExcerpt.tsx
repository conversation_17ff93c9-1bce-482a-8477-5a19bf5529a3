"use client";

import { useEffect, useRef, useState } from "react";
import {
  HandThumbDownIcon,
  HandThumbUpIcon,
  ChatBubbleOvalLeftEllipsisIcon,
} from "@heroicons/react/24/outline";

type SlateNode = {
  id?: string;
  type: string;
  text?: string;
  children?: SlateNode[];
  highlight?: boolean;
  highlightColor?: string;
  lacks_specificity_text?: string;
  drive_empathy_text?: string;
  [key: string]: any;
};

interface EssayExcerptBlockProps {
  excerpt: SlateNode;
  index: number;
}

export const EssayExcerptBlock = ({
  excerpt,
  index,
}: EssayExcerptBlockProps) => {
  const [expanded, setExpanded] = useState(false);
  const [activeCommentMeta, setActiveCommentMeta] = useState<{
    comment: string;
    icon: JSX.Element;
    title: string;
    color: string;
    commentStyle: string;
  } | null>(null);
  const [popupStyle, setPopupStyle] = useState<{
    top: number;
    left: number;
  } | null>(null);

  const popupRef = useRef<HTMLDivElement | null>(null);

  const children = excerpt.children ?? [];
  const titleNode = children.find((n) => n.type === "essay_excerpt_title");
  const subtitleNode = children.find(
    (n) => n.type === "essay_excerpt_subtitle",
  );
  const bodyNode = children.find((n) => n.type === "essay_excerpt_body");

  const extractText = (node?: SlateNode): string => {
    if (!node) return "";
    if ("text" in node && typeof node.text === "string") return node.text;
    if (Array.isArray(node.children))
      return node.children.map(extractText).join("");
    return "";
  };

  const fullBodyText = extractText(bodyNode);
  const charCount = fullBodyText.length;
  const shouldCollapse = charCount > 500;

  const getCommentMeta = (leaf: SlateNode) => {
    if (leaf.highlightColor === "red") {
      return {
        icon: <HandThumbDownIcon className="size-5 text-primary01-75" />,
        title: "Lacks Specificity",
        comment: leaf.lacks_specificity_text ?? null,
        color: "text-primary01-75",
        commentStyle:
          "text-sm font-normal font-display-serif leading-normal text-black",
      };
    }
    if (leaf.highlightColor === "green") {
      return {
        icon: <HandThumbUpIcon className="size-5 text-[#1B5C0C]" />,
        title: "Drive Empathy",
        comment: leaf.drive_empathy_text ?? null,
        color: "text-[#1B5C0C]",
        commentStyle:
          "text-sm font-normal font-['Lato'] leading-tight text-black",
      };
    }
    return {
      icon: <ChatBubbleOvalLeftEllipsisIcon className="text-gray-500 size-5" />,
      title: "Comment",
      comment: leaf.lacks_specificity_text ?? leaf.drive_empathy_text ?? null,
      color: "text-gray-500",
      commentStyle: "text-sm text-black",
    };
  };

  const handleLeafClick = (e: React.MouseEvent, leaf: SlateNode) => {
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    const popupWidth = 260;
    const padding = 16;
    const left = Math.min(
      Math.max(rect.left + rect.width / 2 - popupWidth / 2, padding),
      window.innerWidth - popupWidth - padding,
    );
    const top = rect.bottom + 6;

    const meta = getCommentMeta(leaf);
    if (meta.comment) {
      setActiveCommentMeta({ ...meta, comment: meta.comment ?? "" });
      setPopupStyle({ top, left });
    }
  };

  const renderLeaf = (leaf: SlateNode, idx: number) => {
    const { text, highlight, highlightColor } = leaf;
    if (!highlight || !highlightColor) return <span key={idx}>{text}</span>;

    const meta = getCommentMeta(leaf);
    if (!meta.comment) return <span key={idx}>{text}</span>;

    const isActive = activeCommentMeta?.comment === meta.comment;

    return (
      <span
        key={idx}
        className="inline cursor-pointer underline"
        style={{ color: highlightColor }}
        onClick={(e) => handleLeafClick(e, leaf)}
      >
        {text}
        {isActive && popupStyle && activeCommentMeta && (
          <div
            ref={popupRef}
            className="comment-tooltip border-gray-300 fixed z-50 w-[260px] rounded bg-white px-4 py-3 text-sm shadow-lg"
            style={{
              top: popupStyle.top,
              left: popupStyle.left,
            }}
          >
            <div
              className={`mb-2 flex items-center gap-2 text-xl font-normal ${activeCommentMeta.color}`}
            >
              <span>{activeCommentMeta.icon}</span>
              <span>{activeCommentMeta.title}</span>
            </div>
            <div className={activeCommentMeta.commentStyle}>
              {activeCommentMeta.comment}
            </div>
            <div className="absolute -top-1 left-6">
              <div className="border-gray-300 size-2 rotate-45 bg-white" />
            </div>
          </div>
        )}
      </span>
    );
  };

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(e.target as Node)) {
        setActiveCommentMeta(null);
        setPopupStyle(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div
      key={excerpt.id ?? index}
      className="my-6 rounded bg-[#F5F3F0] p-4 transition-all duration-300"
    >
      <h2 className="mb-2 mt-4 font-display-sans text-2xl font-bold text-primary01-75">
        {extractText(titleNode)}
      </h2>
      <p className="text-gray-500 mb-4 font-body-single text-base font-normal">
        {extractText(subtitleNode)}
      </p>

      <div
        className={`overflow-hidden border-t border-neutral01-25 transition-all duration-300 ${shouldCollapse && !expanded ? "max-h-[629px]" : "max-h-full"
          }`}
      >
        {bodyNode?.children?.map((para, idx) => (
          <p key={para.id ?? idx} className="mb-2">
            {para.children?.map((leaf, i) => renderLeaf(leaf, i))}
          </p>
        ))}
      </div>

      {shouldCollapse && (
        <div className="mt-4 text-center">
          <button
            className="text-gray-600 text-sm underline"
            onClick={() => setExpanded((prev) => !prev)}
          >
            {expanded ? "Show less ⌃" : "Show more ⌄"}
          </button>
        </div>
      )}
    </div>
  );
};
