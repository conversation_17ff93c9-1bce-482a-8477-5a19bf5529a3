import React from "react";
import { ISocialMediaIcon } from "./SiteShell/types";
import { fetchStoryblokStories } from "@/common/storyblok";

type Props = {
  locale: string;
  metaDescription?: string;
  socialMediaLinks?: ISocialMediaIcon[];
};

const HomePageStructuredData = async (props: Props) => {
  const { locale, metaDescription, socialMediaLinks } = props;
  const { data } = await fetchStoryblokStories({
    starts_with: `${locale}/`,
    filter_query: {
      component: {
        in: "settings",
      },
    },
  });

  const socialMediaLinksList = socialMediaLinks?.map(
    (link) => link.socialMediaLink.cached_url ?? link.socialMediaLink.url,
  );

  const phoneNumber =
    (data?.stories[0]?.content as { phoneNumber?: string })?.phoneNumber ??
    undefined;

  const url = `https://www.crimsoneducation.org/${locale}/`;
  const logo =
    "https://a.storyblok.com/f/64062/112x112/aa42569943/crimson_logo_square_whitebg.png";

  const contactPoint = {
    "@type": "ContactPoint",
    telephone: phoneNumber,
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    url,
    logo,
    name: "Crimson Education",
    description: metaDescription,
    telephone: phoneNumber,
    contactPoint: contactPoint,
    sameAs: socialMediaLinksList ?? [],
  };

  return (
    <script type="application/ld+json">{JSON.stringify(structuredData)}</script>
  );
};

export default HomePageStructuredData;
