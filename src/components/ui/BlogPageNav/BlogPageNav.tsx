"use client";

import { useRef, useState, useEffect } from "react";
import AudioContainer from "../AudioPlayer/AudioPlayer";
import PlayIcon from "@/components/icons/Play";
import ShareButton from "@/components/ui/ShareButton";

interface BlogPageNavProps {
  title: string;
  audioSrc?: string;
  fullSlug: string;
}
const BlogPageNav = ({ title, audioSrc = "", fullSlug }: BlogPageNavProps) => {
  const playerBtnRef = useRef(null);
  const [mounted, setMounted] = useState<boolean>(false);
  useEffect(() => {
    setTimeout(() => {
      setMounted(true);
    }, 0);
  }, []);

  return (
    <nav className="mt-6 flex h-16 items-center justify-start gap-sm border-b border-grays-G5 md:mt-8">
      <ShareButton title={title} fullSlug={fullSlug} />

      {audioSrc && (
        <>
          <div
            ref={playerBtnRef}
            className={`group flex cursor-pointer items-center gap-2 fill-black text-black hover:text-primary01-50 hover:[&_.path]:fill-primary01-50`}
          >
            <div>
              <PlayIcon className="size-6.5" />
            </div>
            <span className="font-body-single text-base font-normal leading-[120%] tracking-normal">
              Listen
            </span>
          </div>
          {mounted && (
            <AudioContainer
              title={title}
              playerBtnRef={playerBtnRef}
              containerClassname="fixed left-0 md:left-auto right-0 md:right-[3.125rem] md:bottom-[3.125rem] bottom-0 z-[100] bg-white"
              src={audioSrc}
            />
          )}
        </>
      )}
    </nav>
  );
};

export default BlogPageNav;
