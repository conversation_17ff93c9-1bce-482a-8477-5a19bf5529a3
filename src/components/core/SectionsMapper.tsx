import { StoryblokComponent, type SbBlokData } from "@storyblok/react/rsc";

interface Props {
  sections: SbBlokData[]; // Use SbBlokData properties. Otherwise this prop is too variable to give a more defined type to
  locale?: string;
  getWrapperProps?: (
    blok: SbBlokData,
    index: number,
  ) => React.HTMLAttributes<HTMLDivElement>;
}

const SectionsMapper = ({ sections, getWrapperProps, locale }: Props) => {
  return (
    <>
      {sections?.map((nestedBlok, index) => {
        const wrapperProps = getWrapperProps?.(nestedBlok, index) ?? {};
        return (
          <div key={nestedBlok._uid} {...wrapperProps}>
            <StoryblokComponent
              blok={nestedBlok}
              sectionIndex={index}
              locale={locale}
            />
          </div>
        );
      })}
    </>
  );
};

export default SectionsMapper;
