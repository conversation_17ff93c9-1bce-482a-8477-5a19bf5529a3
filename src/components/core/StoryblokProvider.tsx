"use client";

import { storyblokInit, apiPlugin } from "@storyblok/react/rsc";
import { storyblokComponents } from "./storyblok.components";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

storyblokInit({
  accessToken: process.env.NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN,
  use: [apiPlugin],
  apiOptions: {
    region: process.env.REGION === "ap" ? "ap" : "eu",
  },
  components: storyblokComponents,
});

interface Props {
  children: React.ReactNode;
}

const queryClient = new QueryClient();

export default function StoryblokProvider({ children }: Props) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
