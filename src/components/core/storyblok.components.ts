import WebinarEventPageV2 from "@/components/pages/WebinarEventPageV2";
import BlogPage from "@/components/pages/BlogPage";
import HomePage from "@/components/pages/HomePage";
import WebinarEventType from "@/components/bloks/WebinarEventType";
import WebinarEventHeader from "@/components/bloks/WebinarEventHeader";
import WebinarEventDetailsBanner from "@/components/bloks/WebinarEventDetailsBanner";
import CtaButton from "@/components/bloks/CtaButton";
import StatsGrid from "@/components/bloks/StatsGrid";
import SideTextCarousel from "@/components/bloks/SideTextCarousel";
import ImageWithAltText from "@/components/bloks/ImageWithAltText";
import TextLink from "@/components/bloks/TextLink";
import BlogImageSection from "@/components/bloks/BlogImageSection";
import EmbeddedCodeSection from "@/components/bloks/EmbeddedCodeSection";
import CTABanner from "@/components/bloks/CTABanner";
import StudentResultsCarousel from "@/components/bloks/StudentResultsCarousel";
import MarkdownSection from "@/components/bloks/MarkdownSection";
import Author from "@/components/bloks/Author";
import QAContent from "@/components/bloks/QAContent";
import TableSection from "@/components/bloks/TableSection";
import CalloutBox from "@/components/bloks/CalloutBox";
import Quote from "@/components/bloks/Quote";
import FullWidthVideoSection from "@/components/bloks/FullWidthVideoSection";
import SingleColumnForm from "@/components/bloks/SingleColumnForm";
import SideImageForm from "@/components/bloks/SideImageForm";
import SideTextImageForm from "@/components/bloks/SideTextImageForm";
import SideTextImageGridForm from "@/components/bloks/SideTextImageGridForm";
import ResultsPage from "@/components/pages/ResultsPage";
import NceaCalculatorPage from "@/components/pages/NceaCalculatorPage";
import NceaResultPage from "@/components/pages/NceaResultPage";
import UshGpaCalculator from "@/components/pages/UshCalculatorPage";
import UshGpaResult from "@/components/pages/UshResultPage";
import IBALevelCalculatorPage from "@/components/pages/IbAlevelCalculatorPage";
import IBALevelResult from "@/components/pages/IbAlevelResultPage";
import InfoPage from "@/components/pages/InfoPage";
import StudentAwardsSection from "@/components/bloks/StudentAwardsSection";
import GridSection from "@/components/bloks/GridSection";
import HeaderAndServicesGrid from "@/components/bloks/HeaderAndServices";
import FeaturedBannerAndCTA from "@/components/bloks/FeaturedBannerAndCTA";
import H2BodyAndQuote from "@/components/bloks/h2BodyAndQuote";
import FeaturedQuoteSection from "@/components/bloks/FeaturedQuoteSection";
import ResearchPapersSection from "@/components/bloks/StudentSuccessfulResearchPapers";
import { WrappedCaseStudiesSection } from "@/components/bloks/CaseStudiesSection";
import H2TextGridImageCards from "@/components/bloks/H2TextGridImageCards";
import CrimsonRichText from "@/components/bloks/RichText";
import H2BodyImageGrid from "@/components/bloks/H2BodyImageGrid";
import StudentSuccessCapstones from "@/components/bloks/StudentCapstones";
import CompanyLogoCarousel from "@/components/bloks/CompanyLogoCarousel";
import PageDivider from "@/components/bloks/PageDivider";
import MiscHeader from "@/components/bloks/MiscHeader";
import profilePage from "@/components/pages/ProfilePage";
import QuoteAndImage from "@/components/bloks/QuoteAndImage";
import TestPage from "../pages/test";
import InfographicSchoolResults from "@/components/bloks/InfographicSchoolResults";
import ShortCardProfilePreviewSection from "@/components/bloks/CardProfilePreviewSection/ShortCardProfilePreviewSection";
import GeneralPage from "@/components/pages/GeneralPage";
import CampaignPage from "@/components/pages/CampaignPage";
import EventsPreviewSection from "../bloks/EventsPreviewSection";
import ResultStackSection from "../bloks/ResultStackSection";
import NewsAndArticlesM from "../bloks/NewsAndArticles";
import AllTimeAdmissionsResultsTable from "@/components/bloks/AllTimeAdmissionsResultsTable";
import EbookPage from "@/components/pages/EbookPage";
import TagPage from "@/components/pages/TagPage";
import H3SubheaderBodyGridImage from "@/components/bloks/H3SubheaderBodyGridImage";
import SideRichTextAndImage from "@/components/bloks/SideRichTextAndImage";

import CaseStudyExtracurricular from "@/components/bloks/CaseStudyExtracurricular";

import CaseStudyHighlights from "@/components/bloks/CaseStudyHighlights";
import CaseStudyHonor from "@/components/bloks/CaseStudyHighlights/CaseStudyHonor";
import FAQ from "@/components/bloks/FAQ";
import { FAQItem } from "@/components/bloks/FAQ/FAQItem";
import CaseStudyPage from "@/components/pages/CaseStudyPage";
import CaseStudyHeader from "@/components/bloks/CaseStudyHeader";
import CaseStudyResults from "../bloks/CaseStudyResults";
import CaseStudyBeforeAfter from "@/components/bloks/CaseStudyBeforeAfter";
import LongCardProfilePreviewSection from "@/components/bloks/CardProfilePreviewSection/LongCardProfilePreviewSection";
import ServicesSection from "@/components/bloks/ServicesSection";
import CaseStudiesTestScores from "@/components/bloks/CaseStudiesTestScores";
import Testimonials from "@/components/bloks/Testimonials";

// TODO & TO NOTE: For now, we can't use dynamic imports here,
// it will break the SEO crawl-ability of the pages because all the components
// simply won't exist if JS is disabled. Will need to find a better solution
// (maybe we can use Reacts native lazy loading, but that's a problem for another day)

export const storyblokComponents = {
  // Pages
  WebinarEventPageV2,
  blogPageV2: BlogPage,
  homePage: HomePage,
  resultsPage: ResultsPage,
  infoPage: InfoPage,
  profilePage,
  generalpageV2: GeneralPage,
  campaignptcpage: CampaignPage,
  caseStudyPage: CaseStudyPage,
  caseStudyResults: CaseStudyResults,
  caseStudiesBeforeAfterSection: CaseStudyBeforeAfter,
  NceaToGpaCalculatorV2: NceaCalculatorPage,
  NceaToGpaCacResultPageV2: NceaResultPage,
  UshToGpaCalculatorV2: UshGpaCalculator,
  UshToGpaResultPageV2: UshGpaResult,
  ALevelIBToGpaCalculatorV2: IBALevelCalculatorPage,
  ALevelIBToGpaCalculatorResultsV2: IBALevelResult,
  "Next-demo-our-page": TestPage,
  TagPageV2: TagPage,

  // Blok Components
  WebinarEventType,
  webinarEventHeaderSection: WebinarEventHeader,
  webinarEventDetailsBannerSection: WebinarEventDetailsBanner,
  button: CtaButton,
  imageWithAltText: ImageWithAltText,
  textLink: TextLink,
  formBuilder: SingleColumnForm,
  sideImageFormSection: SideImageForm,
  sideTextImageFormSection: SideTextImageForm,
  sideTextImageGridFormSection: SideTextImageGridForm,
  embeddedCodeSection: EmbeddedCodeSection,
  nextHeaderAndCta: CTABanner,
  studentResultsCarousel: StudentResultsCarousel,
  markdownSection: MarkdownSection,
  nextResearchPapersSection: ResearchPapersSection,
  author: Author,
  faq: QAContent,
  tableSection: TableSection,
  calloutBox: CalloutBox,
  quote: Quote,
  fullWidthVideoSection: FullWidthVideoSection,
  statsGridSection: StatsGrid,
  sideTextCarouselSection: SideTextCarousel,
  blogImageSection: BlogImageSection,
  nextGridSection: GridSection,
  nextHeaderAndServicesGrid: HeaderAndServicesGrid,
  featuredQuoteSection: FeaturedQuoteSection,
  infographicSchoolResults: InfographicSchoolResults,
  nextSuccessfulStudentCapstones: StudentSuccessCapstones,
  h2TextGridImageCards: H2TextGridImageCards,
  featuredBannerWithCTA: FeaturedBannerAndCTA,
  h2BodyQuote: H2BodyAndQuote,
  crimsonRichText: CrimsonRichText,
  studentAwardsSection: StudentAwardsSection,
  h2BodyImageGrid: H2BodyImageGrid,
  nextResultsCarousel: StudentResultsCarousel,
  nextAutoScrollCarousel: CompanyLogoCarousel,
  PageDivider: PageDivider,
  quoteAndImage: QuoteAndImage,
  shortCardProfilePreviewSection: ShortCardProfilePreviewSection,
  longCardProfilePreviewSection: LongCardProfilePreviewSection,
  ebookPage: EbookPage,
  eventsPreviewSection: EventsPreviewSection,
  nextNewsAndArticlesSection: NewsAndArticlesM,
  caseStudiesSection: WrappedCaseStudiesSection,
  nextResultsBlocks: ResultStackSection,
  sideRichTextAndImage: SideRichTextAndImage,
  caseStudyHighlights: CaseStudyHighlights,
  caseStudyHonor: CaseStudyHonor,
  FAQ: FAQ,
  FAQItem: FAQItem,
  allTimeAdmissionsResultsTable: AllTimeAdmissionsResultsTable,
  h3SubheaderBodyGridImage: H3SubheaderBodyGridImage,
  miscHeader: MiscHeader,
  caseStudyHeader: CaseStudyHeader,
  servicesSection: ServicesSection,
  caseStudyExtracurricular: CaseStudyExtracurricular,
  caseStudiesTestScores: CaseStudiesTestScores,
  testimonials: Testimonials,
};
