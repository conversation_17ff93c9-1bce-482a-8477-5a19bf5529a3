"use client";
import React, { createContext, ReactNode, useContext, useState } from "react";
import { IStandardDataSource } from "@/common/types";

interface PageContextType {
  coursesInterestedIn: IStandardDataSource[] | null;
  locale: string;
  isVisualComposer?: boolean;
  lang?: string;

  /**
   * Overrides for the bottom CTA banner (title and button text).
   */
  stickyBannerOverrides?: {
    title?: string;
    buttonText?: string;
    linkUrl?: string;
    onClick?: () => void;
  } | null;

  /**
   * Function to update the sticky banner's title and button text.
   */
  stickyBannerActions?: {
    update: (title: string, buttonText: string) => void;
    reset: () => void;
  };

  /**
   * Override for the navigation logo (URL specifically).
   */
  navLogoOverride?: string | null;

  /**
   * Whether to hide the navigation and footer.
   */
  hideNavFooter?: boolean;

  /**
   * Actions to update or reset the navigation logo override.
   */
  navLogoOverrideActions?: {
    update: (navLogoOverride: string) => void;
    reset: () => void;
  };

  /**
   * Actions to update or reset the hideNavFooter flag
   */
  hideNavFooterActions?: {
    update: (hideNavFooter: boolean) => void;
    reset: () => void;
  };
}

interface IContextValues {
  values: PageContextType;
}

const PageContext = createContext<IContextValues | undefined>(undefined);

export const PageContextProvider = ({
  children,
  values,
}: IContextValues & { children: ReactNode }) => {
  const [stickyBannerOverrides, setStickyBannerOverrides] = useState<{
    title?: string;
    buttonText?: string;
    linkUrl?: string;
    onClick?: () => void;
  } | null>(null);
  const [navLogoOverride, setNavLogoOverride] = useState<string | null>(null);
  const [hideNavFooter, setHideNavFooter] = useState<boolean>(false);

  const updateStickyBanner = (
    title: string,
    buttonText: string,
    linkUrl?: string,
    onClick?: () => void
  ) => {
    setStickyBannerOverrides({ title, buttonText, linkUrl, onClick });
  };
  const resetStickyBanner = () => {
    setStickyBannerOverrides(null);
  };

  const updateNavLogoOverride = (navLogoOverride: string) => {
    setNavLogoOverride(navLogoOverride);
  };

  const resetNavLogoOverride = () => {
    setNavLogoOverride(null);
  };

  const updateHideNavFooter = (hideNavFooter: boolean) => {
    setHideNavFooter(hideNavFooter);
  };

  const resetHideNavFooter = () => {
    setHideNavFooter(false);
  };

  const hideNavFooterActions = {
    update: updateHideNavFooter,
    reset: resetHideNavFooter,
  };

  const navLogoOverrideActions = {
    update: updateNavLogoOverride,
    reset: resetNavLogoOverride,
  };

  const stickyBannerActions = {
    update: updateStickyBanner,
    reset: resetStickyBanner,
  };

  const contextValue = {
    values: {
      ...values,
      stickyBannerOverrides,
      navLogoOverride,
      hideNavFooter,
      navLogoOverrideActions,
      hideNavFooterActions,
      stickyBannerActions,
    },
  };

  return (
    <PageContext.Provider value={contextValue}>{children}</PageContext.Provider>
  );
};

export const usePageContext = () => {
  const context = useContext(PageContext);
  if (!context) {
    throw new Error("usePageContext must be used within a PageContextProvider");
  }
  return context.values;
};
