/**
 * Special padding for the blog page， padding is different from the <Container>
 */

import { cva } from "class-variance-authority";
import { cn } from "@/common/utils";

const blogPageContainerVariants = cva("mx-auto w-full", {
    variants: {
        size: {
            default:
                "px-6 md:px-[6.6rem] lg:px-20 xl:px-[7.5rem] 2xl:px-[9.4rem] pt-[4.7rem]",
            full: "w-full max-w-full px-0 mx-0",
            large: "w-full max-w-screen-2xl px-6 md:px-[6.6rem] lg:px-20 xl:px-[7.5rem] 2xl:px-[9.4rem]",
        },
    },
    defaultVariants: {
        size: "default",
    },
});

type Props = {
    children: React.ReactNode;
    className?: string;
};

const BlogPageContainer = ({ children, className }: Props) => {
    return (
        <div className={cn(blogPageContainerVariants(), className)}>
            {children}
        </div>
    );
};

export default BlogPageContainer;