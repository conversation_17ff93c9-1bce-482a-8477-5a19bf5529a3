import { SummaryProps } from "./types";
import Text from "@/components/ui/Text";

export default function SummaryCard({ excerpt }: SummaryProps) {
  if (!excerpt) return null;

  return (
    <div className="my-8 inline-flex w-full items-center justify-center gap-5 rounded bg-neutral01-0 p-6 md:mb-11 lg:mt-12">
      <div className="inline-flex flex-1 flex-col items-start justify-center gap-2.5 border-l-2 border-primary01-75 pl-5">
        <Text
          tag="p"
          style="h5"

          className="w-28 justify-start text-primary01-100"
        >
          Summary
        </Text>

        <Text
          tag="p"
          style="b2"
          className="justify-end self-stretch text-primary01-100"
        >
          {excerpt}
        </Text>
      </div>
    </div>
  );
}
