import { blogTagsList } from "./types";
import Text from "@/components/ui/Text";

type ExploreTopicsProps = {
  blogTagsList?: blogTagsList;
};

export default function ExploreTopics({ blogTagsList }: ExploreTopicsProps) {
  if (!blogTagsList || blogTagsList?.length === 0) return null;

  return (
    <div className="mt-8 sm:mt-10">
      <div className="mb-8 h-px w-full bg-grays-G5 sm:mb-12 sm:w-32"></div>
      <Text 
      tag="h5"
      style="n2"
      className="mb-2 justify-start text-primary01-100 sm:mb-4 md:mb-6 font-normal">
        {/* TODO need translation */}
        Explore More Topics:
      </Text>
      <div className="mt-4 flex flex-wrap items-start gap-3">
        {blogTagsList?.map((tag, index) => (
          <a
            key={index}
            className="h-9 cursor-pointer rounded-full bg-primary01-75 px-4 font-display-sans text-base leading-9 text-white no-underline transition-colors duration-300 hover:bg-primary01-50"
            href={`/blog/tags/${tag.value.toLowerCase()}`}
          >
            {tag.name}
          </a>
        ))}
      </div>
    </div>
  );
}
