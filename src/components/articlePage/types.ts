import {
  ILearningCollectionContent,
  IPopulatedLearningCollectionContent,
  IStory,
  IStoryContent,
  SectionItem,
} from "@/common/types";

export type blok = {
  title: string;
  pageTitle: string;
  description: string;
  coverImage: string;
  excerpt: string;
  subtitle: string;
  blogTags: {
    tag: string;
  }[];
  sections: SectionItem[];
  MarkDown?: string;
  titleImage?: string;
};

export type SummaryProps = {
  excerpt: string;
};

export type ExploreTopicTag = {
  tag: string;
  _uid?: string;
  component: string;
};

export type blogTagsList = {
  name: string;
  value: string;
}[];

export type IPopulatedLearningCollectionStory =
  IStory<IPopulatedLearningCollectionContent>;

export interface LearningCollectionItem {
  name: string;
  full_slug: string;
  published_at: string;
  shareImage: string;
  uuid: string;
  content: IStoryContent;
}

export type ILearningCollectionStory = IStory<ILearningCollectionContent>;
