import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import Button from "@/components/ui/Button";
import { cn } from "@/common/utils";
import { blogTagsList } from "../types";
import StoryblokLink from "@/components/ui/StoryblokLink";
import Text from "@/components/ui/Text";

interface Props {
    isSticky: boolean;
    isMenuOpen: boolean;
    setIsMenuOpen: (v: boolean) => void;
    title: string;
    blogTagsList?: blogTagsList;
    ctaLabel?: string;
}

const StickyHeaderBar = ({
    isSticky,
    isMenuOpen,
    setIsMenuOpen,
    title,
    blogTagsList,
    ctaLabel,
}: Props) => {
    return (
        <div
            className={cn(
                "z-50 fixed top-0 left-0 w-screen bg-white h-16 shadow transition-transform duration-300 ease-in-out",
                "hidden lg:block",
                isSticky ? "translate-y-0" : "-translate-y-full"
            )}
        >
            <div className="flex items-center justify-between size-full px-6">

                <button
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                    aria-expanded={isMenuOpen}
                    aria-controls="sticky-header-menu"
                    className="relative inline-flex items-center justify-center rounded-md focus:outline-none"
                >
                    {ctaLabel && (<><span className="sr-only">Toggle Menu</span>
                        {isMenuOpen ? (
                            <XMarkIcon className="size-8 text-primary01-100" />
                        ) : (
                            <Bars3Icon className="size-8 text-primary01-100" />
                        )}</>)}

                </button>

                <div className="w-[70%] absolute left-1/2 -translate-x-1/2 flex items-center justify-center text-center">
                    {blogTagsList && blogTagsList?.length > 0 && (
                        <Text tag="span" style="t2" className="pr-2 mr-2 text-primary01-50  border-r border-grays-G5">
                            {blogTagsList[0]?.name}
                        </Text>
                    )}

                    <Text tag="h3" style="sh7" className="font-normal">
                        {title}
                    </Text>
                </div>

                <StoryblokLink
                    link={{
                        url: "/en/contact/",
                        linktype: "url",
                        cached_url: "/en/contact/",
                    }}
                >
                    <Button colour="red" theme="primary">
                        {ctaLabel ?? "Contact us"}
                    </Button>
                </StoryblokLink>
            </div>
        </div>
    );
};

export default StickyHeaderBar;