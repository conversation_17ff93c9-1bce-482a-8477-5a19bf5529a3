import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { cn } from "@/common/utils";
import { useEffect } from "react";
import { IHeaderBlock } from "@/components/ui/SiteShell/types";
import StoryblokLink from "@/components/ui/StoryblokLink";

interface Props {
    isSticky: boolean;
    isMenuOpen: boolean;
    setIsMenuOpen: (v: boolean) => void;
    navMenus: IHeaderBlock[];
    activeMenuIndex: number;
    setActiveMenuIndex: (v: number) => void;
    menuRef: React.RefObject<HTMLDivElement>;
}

const StickyHeaderMenu = ({
    isSticky,
    isMenuOpen,
    setIsMenuOpen,
    navMenus,
    activeMenuIndex,
    setActiveMenuIndex,
    menuRef,
}: Props) => {
    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
                setIsMenuOpen(false);
            }
        };

        if (isSticky && isMenuOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        }

        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, [isSticky, isMenuOpen, menuRef, setIsMenuOpen]);

    if (!isSticky || !isMenuOpen) return null;

    return (
        <>
            <div
                className="fixed inset-0 z-30"
                onClick={() => setIsMenuOpen(false)}
                aria-hidden="true"
            />
            <div
                ref={menuRef}
                className="z-40 fixed top-16 left-0 w-auto shadow-md border-gray-200 h-[25rem] flex overflow-y-scroll"
                id="sticky-header-menu"
            >
                <div className="w-64 bg-neutral01-0">
                    {navMenus.map((menu, index) => (
                        <div
                            key={menu._uid}
                            onClick={() => setActiveMenuIndex(index)}
                            className={cn(
                                "w-full cursor-pointer text-left  p-5  transition duration-150 flex items-center justify-between font-display-sans text-base font-semibold",
                                index === activeMenuIndex
                                    ? "bg-white text-primary01-50"
                                    : "text-primary01-100 hover:bg-white hover:text-primary01-50"
                            )}
                        >
                            {menu.title}
                            <ChevronRightIcon className="size-2xl" />
                        </div>
                    ))}
                    {/* TODO: Logout Button */}
                    {/* <div className="px-5 hover:bg-white hover:text-primary01-50 text-primary01-100 cursor-pointer border-t border-grays-G5 w-full text-left h-16 transition duration-150 flex items-center justify-between font-display-sans text-base font-semibold">
                        {siteShellContent?.logoutLabel ?? "Logout"}
                        <ArrowLeftEndOnRectangleIcon className="size-2xl" />
                    </div> */}
                </div>

                <div className="w-64 overflow-y-auto bg-white">
                    {navMenus[activeMenuIndex]?.items.map((item) => (
                        <StoryblokLink
                            key={item._uid}
                            link={item.link}
                            className="transition group flex flex-col justify-center p-5 border-b border-transparent hover:border-primary01-50"
                        >
                            <p className="transition text-base font-semibold text-primary01-100 leading-tight font-display-sans group-hover:text-primary01-50">
                                {item.text}
                            </p>
                            {item.description && (
                                <p className="transition text-sm font-normal text-grays-G4 leading-none font-body-single group-hover:text-primary01-100 mt-1">
                                    {item.description}
                                </p>
                            )}
                        </StoryblokLink>
                    ))}
                </div>
            </div>
        </>
    );
};

export default StickyHeaderMenu;