"use client";

import { ISbSiteShellStoryContent } from "@/components/ui/SiteShell/types";
import { useState, useEffect, useMemo, useRef } from "react";
import { useWindowScroll } from "@uidotdev/usehooks";
import { blogTagsList } from "../types";
import StickyHeaderBar from "./StickyHeaderBar";
import StickyHeaderMenu from "./StickyHeaderMenu";

interface Props {
  title: string;
  siteShellContent: ISbSiteShellStoryContent | null;
  blogTagsList?: blogTagsList;
}

const StickyHeader = ({ title, siteShellContent, blogTagsList }: Props) => {
  const [isSticky, setIsSticky] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeMenuIndex, setActiveMenuIndex] = useState(0);
  const menuRef = useRef<HTMLDivElement>(null);
  const [{ y: scrollY }] = useWindowScroll();

  const initialMenuOpenScrollY = useRef<number | null>(null);

  useEffect(() => {
    if (isMenuOpen && initialMenuOpenScrollY.current === null) {
      initialMenuOpenScrollY.current = scrollY ?? 0;
    } else if (!isMenuOpen) {
      initialMenuOpenScrollY.current = null;
    }
  }, [isMenuOpen, scrollY]);

  useEffect(() => {
    if (!isMenuOpen || initialMenuOpenScrollY.current === null) return;

    const delta = Math.abs((scrollY ?? 0) - initialMenuOpenScrollY.current);
    const threshold = 1500;
    if (delta > threshold) {
      setIsMenuOpen(false);
      initialMenuOpenScrollY.current = null;
    }
  }, [scrollY, isMenuOpen]);

  const navMenus = useMemo(() => {
    if (!siteShellContent) return [];
    return [
      ...(siteShellContent?.admissionsResourcesHeader || []),
      ...(siteShellContent?.ourServicesHeader || []),
      ...(siteShellContent?.aboutCrimsonHeader || []),
    ];
  }, [siteShellContent]);

  useEffect(() => {
    const triggerY = 750;
    setIsSticky((scrollY ?? 0) > triggerY);
  }, [scrollY]);
  if (!siteShellContent) {
    return null;
  }

  return (
    <>
      <StickyHeaderBar
        isSticky={isSticky}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
        title={title}
        blogTagsList={blogTagsList}
        ctaLabel={siteShellContent?.ctaButtonLabel}
      />
      <StickyHeaderMenu
        isSticky={isSticky}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
        navMenus={navMenus}
        activeMenuIndex={activeMenuIndex}
        setActiveMenuIndex={setActiveMenuIndex}
        menuRef={menuRef}
      />
    </>
  );
};

export default StickyHeader;
