import { ButtonTheme, SectionItem, TButtonColour } from "@/common/types";
import { StoryblokComponent } from "@storyblok/react/rsc";

type BlogSectionsProps = {
  sections: SectionItem[];
};

export default function ArticleSections({ sections }: BlogSectionsProps) {
  return (
    <section>
      {sections?.map((section, index) => {
        let wrapperClassName = "";
        if (section.component === "button") {
          section.colour = "red" as TButtonColour;
          section.appearance = "primary" as ButtonTheme;
          wrapperClassName = "flex justify-center";
        }

        return (
          <div
            key={section._uid}
            id={section.anchorId}
            className={wrapperClassName}
          >
            <StoryblokComponent blok={section} sectionIndex={index} />
          </div>
        );
      })}
    </section>
  );
}
