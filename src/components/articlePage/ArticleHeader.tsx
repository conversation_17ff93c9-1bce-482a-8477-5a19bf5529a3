import Image from "next/image";
import { httpsPath, formatLocalTime } from "@/common/utils";
import Text from "@/components/ui/Text";
import { blogTagsList, blok } from "./types";

type Props = {
  blok: blok;
  publishedAt?: string | null;
  blogTagsList?: blogTagsList;
  fullSlug: string;
};

export default function ArticleHeader(props: Props) {
  const { blok, publishedAt, blogTagsList, fullSlug } = props;
  return (
    <div className="relative mb-11">
      {blogTagsList && blogTagsList?.length > 0 && (
        <span className="mt-2 inline-block font-display-sans text-sans-sm md:text-sans-lg font-semibold leading-normal text-primary01-50 transition-colors duration-200 sm:mt-5 ">
          {blogTagsList[0]?.name}
        </span>
      )}

      <div className="mt-1 flex flex-col items-start justify-between sm:mt-[16px]">
        <Text
          tag="h1"
          style="q2"
          mdStyle="h1"
          className="md:text-sans-7xl !font-display-serif text-black  mb-5 lg:!w-[71%] 2xl:!w-full !font-normal"
        >
          {blok.title}
        </Text>

        <Text
          tag="h2"
          style="ph2"
          className="leading-[120%] text-black"
        >
          {blok.subtitle}
        </Text>
      </div>
      <div className="w-full rounded mt-5 md:mt-7">
        {blok.titleImage && (
          <Image
            src={httpsPath(blok.titleImage)}
            alt={blok.pageTitle}
            className="h-96 w-full rounded object-cover"
            width={800}
            height={384}
            sizes="(max-width: 768px) 100vw, 800px"
            priority
          />
        )}
      </div>
      {publishedAt && (
        <Text tag="span" style="b2" className="absolute  -bottom-10 lg:-bottom-11 text-grays-G4 ">
          {formatLocalTime(publishedAt, fullSlug)}
        </Text>
      )}
    </div>
  );
}
