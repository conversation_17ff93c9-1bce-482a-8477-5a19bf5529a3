import { useEffect, useState, useRef } from "react";

export function useSmartTOCState(hasTOC: boolean, hasRelatedArticles: boolean) {
  const [scrollRatio, setScrollRatio] = useState(0);
  const [isOpen, setIsOpen] = useState(true);
  const [mounted, setMounted] = useState(false);
  const lastZoneRef = useRef<"top" | "bottom">("top");

  const isOnlyRelatedArticles = !hasTOC;

  useEffect(() => {
    setMounted(true);
    const handleScroll = () => {
      const scrollY = window.scrollY || window.pageYOffset;
      const documentHeight = document.body.scrollHeight;
      const windowHeight = window.innerHeight;
      const ratio = scrollY / (documentHeight - windowHeight);
      setScrollRatio(ratio);

      const currentZone = ratio > 0.5 ? "bottom" : "top";
      const prevZone = lastZoneRef.current;

      if (hasTOC && hasRelatedArticles) {
        if (currentZone === "bottom" && prevZone === "top") {
          setIsOpen(false);
        }

        if (currentZone === "top" && prevZone === "bottom") {
          setIsOpen(true);
        }
      }

      lastZoneRef.current = currentZone;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [hasTOC, hasRelatedArticles]);

  const shouldShowRelatedArticles = isOnlyRelatedArticles
    ? true
    : scrollRatio > 0.5 && !isOpen;

  return {
    isOpen,
    setIsOpen,
    scrollRatio,
    shouldShowRelatedArticles,
    mounted,
  };
}
