import { useEffect, useState, RefObject } from "react";

export function useCollapsibleHeight(
  isOpen: boolean,
  ref: RefObject<HTMLElement>,
): string {
  const [height, setHeight] = useState("0px");
  useEffect(() => {
    if (isOpen && ref.current) {
      const h = ref.current.scrollHeight;
      const max = window.innerHeight * 0.8;
      setHeight(`${Math.min(h, max)}px`);
    } else {
      setHeight("0px");
    }
  }, [isOpen, ref]);
  return height;
}
