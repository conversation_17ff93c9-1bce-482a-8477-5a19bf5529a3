import { useMemo } from "react";
import { SectionItem, RichEditorChildren } from "@/common/types";

export function useTocSections(sections: SectionItem[]): SectionItem[] {
  return useMemo(() => {
    const toc: SectionItem[] = [];
    sections.forEach((section) => {
      if (
        section.component === "crimsonRichText" &&
        Array.isArray(section.content)
      ) {
        section.content.forEach((node: RichEditorChildren) => {
          if (node.type === "h2" && node.stickyNavTitle?.trim()) {
            toc.push({
              stickyNavTitle: node.stickyNavTitle.trim(),
              anchorId: node.id!,
            });
          }
          if (node.type === "qa_section") {
            const titleNode = node.children?.find(
              (c: RichEditorChildren) =>
                c.type === "qa_section_title" && c.stickyNavTitle?.trim(),
            );
            if (titleNode) {
              toc.push({
                stickyNavTitle: titleNode.stickyNavTitle?.trim() ?? "",
                anchorId: node.id!,
              });
            }
          }
        });
      }
      if (section.stickyNavTitle && section.anchorId) {
        toc.push({
          stickyNavTitle: section.stickyNavTitle,
          anchorId: section.anchorId,
          component: section.component,
        });
      }
    });
    return toc;
  }, [sections]);
}
