import { useState, useEffect } from "react";
import { SectionItem, RichEditorChildren } from "@/common/types";

export function useDuplicateEntries(sections: SectionItem[]) {
  const [dups, setDups] = useState<{ id: string; title: string }[]>([]);

  useEffect(() => {
    if (window.location.pathname !== "/visual-composer") {
      setDups([]);
      return;
    }

    const seen = new Set<string>();
    const list: { id: string; title: string }[] = [];

    sections.forEach((sectionItem) => {
      const check = (id: string, title: string) => {
        if (!id || !title) return;
        const key = `${id}__${title}`;
        if (seen.has(key)) list.push({ id, title });
        else seen.add(key);
      };

      if (
        sectionItem.component === "crimsonRichText" &&
        Array.isArray(sectionItem.content)
      ) {
        sectionItem.content.forEach((node: RichEditorChildren) => {
          if (node.type === "h2")
            check(node.id!, node.stickyNavTitle?.trim() ?? "");
          if (node.type === "qa_section") {
            const tn = node.children?.find(
              (c) => c.type === "qa_section_title",
            );
            check(node.id!, tn?.stickyNavTitle?.trim() ?? "");
          }
        });
      }

      if (sectionItem.anchorId && sectionItem.stickyNavTitle) {
        check(sectionItem.anchorId, sectionItem.stickyNavTitle);
      }
    });

    setDups(list);
  }, [sections]);

  return dups;
}
