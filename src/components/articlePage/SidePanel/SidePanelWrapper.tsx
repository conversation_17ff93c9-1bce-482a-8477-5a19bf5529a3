"use client";

import TableOfContentWrapper from "../SidePanel/TOC/TableOfContentWrapper";
import RelatedArticles from "../SidePanel/RelatedArticles/RelatedArticles";
import { SectionItem } from "@/common/types";
import { cn } from "@/common/utils";
import { useSmartTOCState } from "./hooks/useSmartTOCState";
import LearningCollectionSidebar from "./LC/LearningCollection";
import { IPopulatedLearningCollectionStory } from "../types";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";

interface Props {
  sections: SectionItem[];
  relatedArticlesList: IBlogStory[];
  learningCollection?: IPopulatedLearningCollectionStory;
  currentBlogUuid?: string;
}

const SidePanelWrapper = ({
  sections,
  relatedArticlesList,
  learningCollection,
  currentBlogUuid,
}: Props) => {
  const hasLearningCollection = !!learningCollection;
  const hasTOC = sections.some(
    (section) => section.stickyNavTitle && section.anchorId,
  );
  const hasRelatedArticles = relatedArticlesList.length > 0;

  const { isOpen, setIsOpen, shouldShowRelatedArticles, mounted } =
    useSmartTOCState(hasTOC, hasRelatedArticles);

  return (
    <aside className="sticky top-0 hidden lg:block lg:w-[30%] xl:w-[28%]">
      {hasLearningCollection ? (
        <LearningCollectionSidebar
          learningCollection={learningCollection}
          currentBlogUuid={currentBlogUuid ?? ""}
        />
      ) : (
        <>
          {hasTOC && (
            <TableOfContentWrapper
              sections={sections}
              forceCollapsed={!isOpen}
              isOpen={isOpen}
              setIsOpen={setIsOpen}
            />
          )}

          {hasRelatedArticles && (
            <div
              className={cn(
                "transform transition-all duration-300 ease-in-out",
                {
                  "translate-y-0 opacity-100":
                    hasTOC && (!mounted || shouldShowRelatedArticles),
                  "pointer-events-none translate-y-4 opacity-0":
                    hasTOC && mounted && !shouldShowRelatedArticles,
                  "w-full pt-[7.0625rem]": !hasTOC,
                },
              )}
            >
              <RelatedArticles relatedArticlesList={relatedArticlesList} />
            </div>
          )}
        </>
      )}
    </aside>
  );
};

export default SidePanelWrapper;
