"use client";
import { cn, httpsPath } from "@/common/utils";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";
import Text from "@/components/ui/Text";
import Image from "next/image";
import Link from "next/link";
import React from "react";

interface Props {
  relatedArticlesList?: IBlogStory[];
}

const RelatedArticles = ({ relatedArticlesList = [] }: Props) => {
  return (
    <div className="w-full rounded-md bg-white">
      <div
        className="overflow-y-auto pr-1 scrollbar-hide"
        style={{ maxHeight: "calc(100vh - 12rem)" }}
      >
        <div className="flex flex-col gap-2 pt-5">
          {relatedArticlesList.map((item: IBlogStory) => {
            const shareImage = item.content?.shareImage;
            const title = item.content?.title ?? item.name;
            return (
              <Link
                key={item.uuid}
                href={`/${item.full_slug}`}
                className="group relative mb-5 flex items-center gap-3 rounded-md transition-colors"
              >
                {typeof shareImage === "string" && (
                  <Image
                    src={httpsPath(shareImage)}
                    alt={title}
                    className="size-[105px] shrink-0 rounded object-cover"
                    width={105}
                    height={105}
                    sizes="210px"
                  />
                )}

                <div
                  className={cn(
                    "flex h-[105px] flex-1 items-center overflow-hidden",
                    typeof shareImage !== "string" && "h-auto items-start",
                  )}
                >
                  <Text
                    tag="p"
                    style="sh7"
                    className={cn(
                      "text-black",
                      typeof shareImage === "string" && "line-clamp-4",
                    )}
                  >
                    {title}
                  </Text>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default RelatedArticles;
