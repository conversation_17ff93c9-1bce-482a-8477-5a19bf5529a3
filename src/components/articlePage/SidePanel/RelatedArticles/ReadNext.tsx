"use client";
import Image from "next/image";
import { httpsPath } from "@/common/utils";
import Text from "@/components/ui/Text";
import Link from "next/link";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";
import { useLabelTranslation } from "@/common/hooks/useTranslation";

type Props = {
  relatedArticlesList?: IBlogStory[];
};

const RelatedArticlesReadNext = ({ relatedArticlesList = [] }: Props) => {
  const { t } = useLabelTranslation();
  return (
    <div className="py-8 w-full max-w-full px-0 mx-0 select-none overflow-hidden overscroll-contain">
      <div className="block lg:hidden mt-6 md:mt-10 px-6 md:px-[6.6rem] xl:px-[75px] 4xl:px-0">
        <Text
          tag="h3"
          style="h5"
          className="text-primary01-100 border-b border-grays-G5 pb-4 mt-1 mb-5"
        >
          {t("Read Next")}
        </Text>

        <div className="relative">
          {relatedArticlesList.map((item: IBlogStory) => {
            const shareImage = item.content?.shareImage;
            const title = item.content?.title ?? item.name;
            return (
              <Link
                key={item.uuid}
                href={`/${item.full_slug}`}
                className="group relative flex items-center gap-3 rounded-md mb-5 transition-colors"
              >
                {typeof shareImage === "string" && (<Image
                  src={httpsPath(shareImage)}
                  alt={title}
                  className="size-[105px] rounded object-cover shrink-0"
                  width={105}
                  height={105}
                  sizes="105px"
                />)}

                <Text tag="p" style="sh7" className="text-black">
                  {title}
                </Text>
              </Link>
            );
          })}
        </div>

      </div>
    </div>
  );

};

export default RelatedArticlesReadNext;