import React from "react";
import Link from "next/link";
import Image from "next/image";
import { httpsPath } from "@/common/utils";
import Text from "@/components/ui/Text";
import { cn } from "@/common/utils";
import { IPopulatedLearningCollectionStory } from "../../types";

type Props = {
  learningCollection: IPopulatedLearningCollectionStory | null;
  currentBlogUuid: string;
};

const LearningCollectionSidebar = ({
  learningCollection,
  currentBlogUuid,
}: Props) => {
  if (!learningCollection?.content) return null;
  const LcStoryList = learningCollection.content?.items ?? [];

  return (
    <div className="w-full rounded-md bg-white pt-[7.0625rem]">
      <Text tag="span" style="c1" className="mb-2 !leading-none text-black">
        Featured in:
      </Text>
      <Text
        tag="p"
        style="h5"
        className="mt-1 border-b border-grays-G5 pb-4 text-primary01-100"
      >
        {learningCollection.content.title}
      </Text>

      <div
        className="overflow-y-auto pr-1"
        style={{ maxHeight: "calc(100vh - 12rem)" }}
      >
        <div className="flex flex-col gap-2 pt-5">
          {LcStoryList.map((item) => {
            const isActive = item.uuid === currentBlogUuid;
            const shareImage = item.content.shareImage;
            const title = item.content.pageTitle;
            if (typeof shareImage !== "string" || !shareImage) {
              return null;
            }

            return (
              <Link
                key={item.uuid}
                href={`/${item.full_slug}`}
                className={cn(
                  "group relative flex items-center gap-3 rounded-md py-2 pl-6 pr-3 transition-colors",
                  isActive ? "bg-[#3d42591a]" : "hover:bg-[#3d42591a]",
                )}
              >
                <span
                  className={cn(
                    "absolute left-1.5 top-1/2 h-0 w-0 -translate-y-1/2",
                    "border-y-[6px] border-l-[10px] border-y-transparent border-l-primary01-75",
                    "transition-opacity",
                    isActive ? "opacity-100" : "opacity-0",
                  )}
                />
                <Image
                  src={httpsPath(shareImage)}
                  alt={title}
                  className="size-[4.375rem] shrink-0 rounded object-cover"
                  width={70}
                  height={70}
                  sizes="70px"
                />
                <Text tag="p" style="sh7" className="text-black">
                  {title}
                </Text>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default LearningCollectionSidebar;
