import Image from "next/image";
import Text from "@/components/ui/Text";
import { cn, httpsPath } from "@/common/utils";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";

const LearningCollectionCard = ({ item }: { item: IBlogStory }) => {
  const title = item.content.title;
  const subtitle = item.content.subtitle;
  const shareImage = item.content.shareImage;
  if (typeof shareImage !== "string" || !shareImage) {
    return null;
  }

  return (
    <div
      key={item.uuid}
      className={cn(
        "flex flex-col p-4 md:flex-row md:justify-between",
        "w-full flex-none",
        "rounded-md bg-white shadow-[4px_14px_25px_0px_rgba(86,65,46,0.20)]",
      )}
    >
      <div
        className={cn(
          "relative",
          "aspect-[499/219] w-full",
          "md:aspect-square md:w-[32%] md:min-w-[10.3rem]",
        )}
      >
        <Image
          src={httpsPath(shareImage)}
          alt={title}
          fill
          className="rounded object-cover"
        />
      </div>
      <div className="flex w-full flex-1 flex-col md:w-3/5 md:flex-none">
        <span className="flex h-full flex-col">
          <Text tag="h4" style="sh6" className="mt-4 text-black md:mt-0">
            {title}
          </Text>
          <Text tag="p" style="b4" mdStyle="b2" className="mt-1 text-black">
            {subtitle}
          </Text>
        </span>
        <a
          href={`/${item.full_slug}`}
          className="mt-2 text-primary01-100 group-hover:underline md:mt-auto"
        >
          <Text
            tag="span"
            style="b4"
            mdStyle="b2"
            className="!font-bold !leading-none text-primary01-75"
          >
            Get Started →
          </Text>
        </a>
      </div>
    </div>
  );
};

export default LearningCollectionCard;
