"use client";

import React from "react";
import Text from "@/components/ui/Text";
import { cn } from "@/common/utils";
import { IPopulatedLearningCollectionStory } from "../../types";
import LearningCollectionCard from "./LearningCollectionCard";
import SlideCarousel from "@/components/ui/SlideCarousel";
import Button from "@/components/ui/Button";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/solid";
import { useLabelTranslation } from "@/common/hooks/useTranslation";

type Props = {
  learningCollection: IPopulatedLearningCollectionStory;
  currentBlogUuid: string;
};

const LearningCollectionCarousel = ({
  learningCollection,
  currentBlogUuid,
}: Props) => {
  const { t } = useLabelTranslation();
  const list = learningCollection.content?.items ?? [];
  const learningCollectionItems = list.filter(
    (item) => item.uuid !== currentBlogUuid,
  );

  if (learningCollectionItems.length === 0) return null;

  return (
    <SlideCarousel
      transparent
      buttonsWrapperClassName="mt-9 md:mt-10"
      autoCardWrapper={false}
      buttonsRender={({ currentIndex, setCurrentIndex, maxIndex }) => {
        return (
          <div className="mt-9 flex justify-end gap-md md:mt-10">
            <Button
              onClick={() => setCurrentIndex((prev) => prev - 1)}
              disabled={currentIndex <= 0}
              theme="icon"
            >
              <ChevronLeftIcon className="size-7 fill-neutral01-75 stroke-neutral01-75" />
            </Button>
            <Button
              onClick={() => setCurrentIndex((prev) => prev + 1)}
              disabled={currentIndex >= maxIndex}
              theme="icon"
            >
              <ChevronRightIcon className="size-7 fill-neutral01-75 stroke-neutral01-75" />
            </Button>
          </div>
        );
      }}
      header={
        <Text
          tag="h3"
          style="h5"
          className="mb-7 mt-1 border-b border-grays-G5 pb-4 text-primary01-100"
        >
          {t("Read Next")}
        </Text>
      }
      cardsPerViewGetter={() => 1}
    >
      {learningCollectionItems.map((item, idx) => {
        return (
          <div
            key={item.uuid}
            className={cn(
              "flex w-full flex-none",
              "items-stretch",
              idx !== learningCollectionItems.length - 1 &&
                "mr-8 md:mr-[4.5rem]",
            )}
          >
            <LearningCollectionCard item={item} />
          </div>
        );
      })}
    </SlideCarousel>
  );
};

export default LearningCollectionCarousel;
