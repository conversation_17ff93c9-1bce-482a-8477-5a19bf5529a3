"use client";
import LearningCollectionCarousel from "./LC/LearningCollectionCarousel";
import RelatedArticlesReadNext from "./RelatedArticles/ReadNext";
import { IPopulatedLearningCollectionStory } from "../types";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";

interface Props {
  learningCollection?: IPopulatedLearningCollectionStory;
  currentBlogUuid: string;
  relatedArticlesList: IBlogStory[];
}

const MobileBottomPanel = ({
  learningCollection,
  currentBlogUuid,
  relatedArticlesList,
}: Props) => {
  const hasLearningCollection =
    (learningCollection?.content?.items?.length ?? 0) > 1;

  return (
    <div className="block lg:hidden">
      {hasLearningCollection && learningCollection ? (
        <LearningCollectionCarousel
          learningCollection={learningCollection}
          currentBlogUuid={currentBlogUuid}
        />
      ) : (
        <RelatedArticlesReadNext relatedArticlesList={relatedArticlesList} />
      )}
    </div>
  );
};

export default MobileBottomPanel;