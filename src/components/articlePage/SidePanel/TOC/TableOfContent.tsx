"use client";

import {
  ChevronDownIcon,
} from "@heroicons/react/24/outline";
import { useEffect, useRef } from "react";
import { cn } from "@/common/utils";
import Text from "@/components/ui/Text";
import { useTocSections } from "../hooks/useTableOfContent";
import { useCollapsibleHeight } from "../hooks/useCollapsibleHeight";
import { SectionItem } from "@/common/types";
import { useDuplicateEntries } from "../hooks/useDuplicateEntries";
import { useLabelTranslation } from "@/common/hooks/useTranslation";

interface Props {
  sections: SectionItem[];
  activeSectionId: string | null;
  isOpenTableContent: boolean;
  setIsOpenTableContent: (val: boolean) => void;
  onGenerateTocSections?: (tocSections: SectionItem[]) => void;
}

const DuplicateWarning = ({
  list,
}: {
  list: { id: string; title: string }[];
}) => (
  <div className="fixed top-12 left-1/3 z-50 -translate-x-1/2 bg-[#a9f7d9] rounded-lg">
    <div className="max-w-xl p-6 shadow-lg">
      <div className="text-lg font-semibold text-red-600">
        ⚠️ Duplicate anchor IDs detected
      </div>
      <div className="mb-4 text-lg font-semibold text-red-600">
        must clear them and save page to see the changes
      </div>
      <ul className="max-h-[300px] overflow-auto text-sm list-disc pl-5">
        {list.map((d, i) => (
          <li key={`${d.id}-${i}`}>
            TOC Label: {d.title} <br />
            Anchor ID: {d.id}
          </li>
        ))}
      </ul>
    </div>
  </div>
);

export default function TableOfContent({
  sections,
  activeSectionId,
  isOpenTableContent,
  setIsOpenTableContent,
  onGenerateTocSections,
}: Props) {
  const contentRef = useRef<HTMLUListElement>(null);
  const tocSections = useTocSections(sections);
  const duplicateEntries = useDuplicateEntries(sections);
  const height = useCollapsibleHeight(isOpenTableContent, contentRef);
  const { t } = useLabelTranslation();

  useEffect(() => {
    if (onGenerateTocSections) {
      onGenerateTocSections(tocSections);
    }
  }, [onGenerateTocSections, tocSections]);

  const hasItems = tocSections.length > 0;
  if (!hasItems) return null;

  return (
    <div className="w-full rounded-md bg-white pt-[7.0625rem]">
      {duplicateEntries.length > 0 && (
        <DuplicateWarning list={duplicateEntries} />
      )}

      <button
        className="flex w-full items-center justify-between gap-4 border-b border-grays-G5 pb-2 pt-4 font-display-sans text-2xl font-semibold text-primary01-100"
        onClick={() => setIsOpenTableContent(!isOpenTableContent)}
      >
        <Text tag="span" style="h5">
          {t('Table of Contents')}
        </Text>
        <ChevronDownIcon
          className={cn(
            "text-gray-500 size-6 font-bold transition-transform duration-300",
            isOpenTableContent && "rotate-180"
          )}
        />
      </button>

      <ul
        ref={contentRef}
        className="pt-1 overflow-y-auto scrollbar-hide transition-[height] duration-300 ease-in-out will-change-[height]"
        style={{ height }}
      >
        {tocSections.map((sec) => (
          <li
            key={sec.anchorId}
            className={cn(
              "mt-3 cursor-pointer font-normal text-grays-G5 transition-colors",
              activeSectionId === sec.anchorId
                ? "text-black"
                : "hover:opacity-90"
            )}
          >
            <a
              href={`#${sec.anchorId}`}
              onClick={(e) => {
                e.preventDefault();
                document
                  .getElementById(sec.anchorId)
                  ?.scrollIntoView({ behavior: "smooth", block: "start" });
              }}
              className="flex items-start font-body-single font-normal no-underline"
            >
              {activeSectionId === sec.anchorId && (
                <span className="mr-2 text-primary01-75">•</span>
              )}
              <span className={activeSectionId === sec.anchorId ? "ml-2" : "ml-6"}>
                {sec.stickyNavTitle}
              </span>
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
}