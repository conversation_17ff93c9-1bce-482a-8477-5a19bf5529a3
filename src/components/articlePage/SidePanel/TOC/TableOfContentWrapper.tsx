"use client";

import { useEffect, useState } from "react";
import TableOfContent from "./TableOfContent";
import useScrollSpy from "@/common/hooks/useScrollSpy";
import { SectionItem } from "@/common/types";

interface Props {
    sections: SectionItem[];
    forceCollapsed?: boolean;
    isOpen: boolean;
    setIsOpen: (val: boolean) => void;
}

const TableOfContentWrapper = ({
    sections,
    forceCollapsed = false,
    isOpen,
    setIsOpen,
}: Props) => {
    const [activeSectionId, setActiveSectionId] = useState<string | null>(null);

    useEffect(() => {
        if (forceCollapsed) {
            setIsOpen(false);
        }
    }, [forceCollapsed, setIsOpen]);
    const [tocSections, setTocSections] = useState<SectionItem[]>([]);

    const ids = tocSections.filter((s) => s.anchorId).map((s) => s.anchorId);

    useScrollSpy({
        ids,
        offset: 100,
        onSectionChange: (id: string) => setActiveSectionId(id),
    });

    return (
        <TableOfContent
            sections={sections}
            activeSectionId={activeSectionId}
            isOpenTableContent={isOpen}
            setIsOpenTableContent={setIsOpen}
            onGenerateTocSections={(tocSections) => setTocSections(tocSections)}
        />
    );
};

export default TableOfContentWrapper;