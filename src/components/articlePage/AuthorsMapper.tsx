import { AuthorsList } from "@/common/types";
import Author from "../bloks/Author";

interface Props {
  variant?: "primary" | "compact";
  sections: AuthorsList;
}
const AuthorsMapper = ({ sections, variant }: Props) => {
  return (
    <>
      {sections?.map((section, index) => (
        <Author
          blok={section.content}
          key={index}
          variant={variant}
          mappedPersona={section?.mappedPersona}
          full_slug={section.full_slug}
        />
      ))}
    </>
  );
};

export default AuthorsMapper;
