import { SbBlokData, storyblokEditable } from "@storyblok/react/rsc";
import SectionsMapper from "@/components/core/SectionsMapper";
import InfoPageHeader, {
  IInfoPageHeader,
} from "@/components/bloks/InfoPageHeader/InfoPageHeader";
import { extractValidAnchors, getDuplicateAnchors } from "@/common/anchorsBar/anchorsBar";
import AnchorsBar from "@/components/ui/InfoPageAnchors/AnchorsBar";
import FloatingAnchorsMenu from "@/components/ui/InfoPageAnchors/FloatingAnchorsMenu";
import DuplicateAnchorWarning from "../ui/InfoPageAnchors/DuplicateAnchorWarning";
import { cn } from "@/common/utils";

interface Props {
  locale: string;
  blok: {
    infoHeader: IInfoPageHeader[];
    sections: SbBlokData[];
    component: string;
  };
  fullSlug: string;
}

const InfoPage = (props: Props) => {
  const { blok, locale } = props;
  const infoPageHeader = blok.infoHeader[0]!;

  const validAnchors = extractValidAnchors(blok.sections);
  const anchorsToRender = validAnchors.slice(0, 7);
  const duplicateItems = getDuplicateAnchors(validAnchors)

  return (
    <div {...storyblokEditable(blok)} className="bg-grays-G6">
      <DuplicateAnchorWarning duplicateItems={duplicateItems} />
      {blok?.infoHeader && (
        <InfoPageHeader blok={infoPageHeader} pageType={blok.component} />
      )}

      {anchorsToRender.length >= 2 && <>
        <AnchorsBar anchors={anchorsToRender} className="hidden xl:block" />
        <FloatingAnchorsMenu anchors={anchorsToRender} className="block xl:hidden" />
      </>}
      {blok?.sections && <SectionsMapper
        locale={locale}
        sections={blok.sections}
        getWrapperProps={(blok) => {
          const anchorId = typeof blok.anchorId === "string" ? blok.anchorId.trim() : undefined;
          return {
            id: anchorId,
            className: cn(anchorId && "scroll-mt-28"),
          };
        }}
      />}
    </div>
  );
};

export default InfoPage;
