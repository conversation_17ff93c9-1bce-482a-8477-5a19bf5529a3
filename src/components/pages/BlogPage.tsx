import dynamic from "next/dynamic";
import { AuthorsList, IStory, SectionItem } from "@/common/types";
import { Suspense } from "react";
import { storyblokEditable } from "@storyblok/react/rsc";
import ArticleHeader from "../articlePage/ArticleHeader";
import { IBlogStory, IBlogStoryContent } from "../bloks/NewsAndArticles/utils";
import {
  blogTagsList,
  IPopulatedLearningCollectionStory,
} from "../articlePage/types";
import BlogPageNav from "../ui/BlogPageNav/BlogPageNav";
import AuthorsMapper from "../articlePage/AuthorsMapper";
import SummaryCard from "../articlePage/SummaryCard";
import ArticleSections from "../articlePage/ArticleSections";
import { ISbSiteShellStoryContent } from "../ui/SiteShell/types";
import StickyHeader from "../articlePage/StickyBar/StickyHeader";
import BlogPageContainer from "../articlePage/BlogPageContainer";
import { getAuthorsList } from "@/common/article/getAuthorsList";
import { getBlogTagNames } from "@/common/article/getBlogTagNames";
import { getLearningCollectionForBlogPage } from "@/common/article/getLearningCollectionsList";
import { getRelatedArticles } from "@/common/article/getRelatedArticles";
import SidePanelWrapper from "../articlePage/SidePanel/SidePanelWrapper";
import Skeleton from "@/components/ui/Skeleton";

const MobileBottomPanel = dynamic(
  () => import("../articlePage/SidePanel/MobileBottomPanel"),
  {
    ssr: true,
    loading: () => null,
  },
);
const ExploreTopics = dynamic(() => import("../articlePage/ExploreTopics"), {
  ssr: true,
  loading: () => null,
});
import Author from "../bloks/Author";

interface Props {
  locale: string;
  slug: string;
  siteShellContent: ISbSiteShellStoryContent | null;
  uuid: string;
  story: IStory<IBlogStoryContent>;
  rawAuthorRelation: {
    content: {
      name: string;
      school?: string;
      image: {
        image: string;
        altText: string;
      }[];
    };
  };
  blok: {
    audio?: string;
    pageTitle: string;
    description: string;
    coverImage: string;
    excerpt: string;
    title: string;
    subtitle: string;
    blogTags: {
      tag: string;
      component: string;
      blogTags: string[];
    }[];
    sections: SectionItem[];
    MarkDown?: string;
    titleImage?: string;
  };
}
const ALLOWED_SECTIONS = [
  "markdownSection",
  "crimsonRichText",
  "faq",
  "tableSection",
  "calloutBox",
  "quote",
  "button",
  "blogImageSection",
  "fullWidthVideoSection",
];

const BlogPage = async ({
  blok,
  story,
  siteShellContent,
  locale,
  slug,
}: Props) => {
  const { sections, audio = "", title } = blok;
  const { published_at, full_slug } = story;

  let AuthorsList: AuthorsList = [] as AuthorsList;
  let blogTagsList: blogTagsList = [];
  let learningCollectionsList: IPopulatedLearningCollectionStory | null = null;
  let relatedArticlesList: IBlogStory[] = [];

  const [authors, tags, lcData, relatedArticles] = await Promise.all([
    getAuthorsList(story, slug),
    getBlogTagNames(story.content.blogTags),
    getLearningCollectionForBlogPage(story.uuid, locale),
    getRelatedArticles(story, locale),
  ]);

  AuthorsList = authors;
  blogTagsList = tags;
  learningCollectionsList = lcData;
  const LcStoryList = lcData?.content?.items ?? [];
  relatedArticlesList = LcStoryList.length > 0 ? [] : relatedArticles;

  const _sections = sections.filter(
    (section) =>
      section.component && ALLOWED_SECTIONS.includes(section.component),
  );

  return (
    <div
      {...storyblokEditable(blok)}
      className="container mx-auto mb-12 w-full"
    >
      <BlogPageContainer>
        <ArticleHeader
          publishedAt={published_at}
          blok={blok}
          blogTagsList={blogTagsList}
          fullSlug={full_slug}
        />

        <StickyHeader
          title={blok.title}
          blogTagsList={blogTagsList}
          siteShellContent={siteShellContent}
        />

        <div className="flex w-full flex-row items-start justify-between">
          <div className="w-full lg:w-[58%] 2xl:w-3/5">
            <BlogPageNav title={title} audioSrc={audio} fullSlug={full_slug} />
            {AuthorsList &&
              AuthorsList.length > 0 &&
              AuthorsList[0]?.content && (
                <Author
                  blok={AuthorsList[0].content}
                  mappedPersona={AuthorsList[0].mappedPersona}
                  full_slug={AuthorsList[0].full_slug}
                />
              )}

            <SummaryCard excerpt={blok.excerpt} />

            <ArticleSections sections={_sections} />

            {AuthorsList && AuthorsList.length > 0 && (
              <div className="mt-10 flex w-full flex-col items-start justify-between gap-4 border-t border-grays-G5 pt-8 md:pt-12">
                <AuthorsMapper sections={AuthorsList} variant="compact" />
              </div>
            )}

            <ExploreTopics blogTagsList={blogTagsList} />
          </div>

          <Suspense
            fallback={
              <aside className="hidden lg:block lg:w-[30%] xl:w-[28%]">
                <Skeleton className="h-[80vh]" />
              </aside>
            }
          >
            <SidePanelWrapper
              sections={_sections}
              relatedArticlesList={relatedArticlesList}
              learningCollection={
                learningCollectionsList?.content && LcStoryList.length > 1
                  ? learningCollectionsList
                  : undefined
              }
              currentBlogUuid={story.uuid}
            />
          </Suspense>
        </div>
      </BlogPageContainer>

      <Suspense
        fallback={
          <div className="block lg:hidden">
            <Skeleton className="h-40" />
          </div>
        }
      >
        <MobileBottomPanel
          learningCollection={
            learningCollectionsList?.content && LcStoryList.length > 1
              ? learningCollectionsList
              : undefined
          }
          currentBlogUuid={story.uuid}
          relatedArticlesList={relatedArticlesList}
        />
      </Suspense>
    </div>
  );
};

export default BlogPage;
