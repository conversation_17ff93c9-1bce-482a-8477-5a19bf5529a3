import { storyblokEditable, StoryblokComponent } from "@storyblok/react/rsc";
import SectionsMapper from "../core/SectionsMapper";
import { ICtaButtonProps } from "@/common/types";
import { getFormCount } from "@/common/utils/getFormCount";
import EventCard from "../bloks/EventsPreviewSection/EventCard";
import { IEventHosts } from "../bloks/EventsPreviewSection";
import { IWebinarEventHeader } from "../bloks/WebinarEventHeader";
import SiteShellOverrider from "../ui/SiteShellOverrider";
import Container from "../ui/Container";

interface Props {
  blok: {
    eventHeader: IWebinarEventHeader[];
    eventDetailsBanner: [{ _uid: string }];
    type: [
      {
        _uid: string;
        eventType: "In Person" | "Online";
        textOverride?: string;
      },
    ];
    dateTime: string;
    sections: [];
    isPreview?: boolean;
    previewCtaButton: ICtaButtonProps[];
    stickyNavigationMessage?: string;
    stickyCtaLabel?: string;
    hostedByLabel?: string;
    signUpLabel?: string;
    eventHosts?: IEventHosts[];
    component: string;
  };
}

const WebinarEventPageV2 = ({ blok }: Props) => {
  const formCount = getFormCount(blok?.sections);

  if (formCount > 1) {
    return (
      <div className="container mx-auto w-full px-4 py-24 text-center text-3xl">
        <p className="text-primary01-50">
          Multiple Web-lead forms detected. This page only allows 1 web lead
          form. Please remove the duplicate forms.
        </p>
      </div>
    );
  }

  if (blok?.isPreview) {
    return (
      <Container>
        <div className="mx-4 mb-4 max-w-[400px] pt-20">
          <EventCard
            hostedByLabel={blok?.hostedByLabel ?? "Hosted by"}
            signUpLabel={blok?.signUpLabel ?? "Sign up"}
            name={blok?.eventHeader[0]?.title ?? "Webinar"}
            eventHosts={blok.eventHosts}
            eventSlug=""
            locale=""
            {...blok}
          />
        </div>
      </Container>
    );
  }

  return (
    <div {...storyblokEditable(blok)}>
      <SiteShellOverrider
        title={blok.stickyNavigationMessage ?? ""}
        buttonText={blok.stickyCtaLabel ?? ""}
        hideNavFooter={true}
      />

      {blok.eventHeader?.length > 0 && (
        <StoryblokComponent
          blok={blok.eventHeader[0]}
          key={blok.eventHeader[0]?._uid}
          eventType={blok.type[0]}
        />
      )}

      {blok.eventDetailsBanner?.length > 0 && (
        <StoryblokComponent
          blok={blok.eventDetailsBanner[0]}
          key={blok.eventDetailsBanner[0]._uid}
          dateTime={blok.dateTime}
        />
      )}

      {blok?.sections && <SectionsMapper sections={blok.sections} />}
    </div>
  );
};

export default WebinarEventPageV2;
