import { storyblokEditable } from "@storyblok/react/rsc";
import Container from "@/components/ui/Container";
import EbookPageContextBridge from "@/components/ui/EbookPage/EbookPageContextBridge";
import HeaderSection from "@/components/ui/EbookPage/HeaderSection";
import PublisherSection from "@/components/ui/EbookPage/PublisherSection";
import ImageAndFormSection from "@/components/ui/EbookPage/ImageAndFormSection";
import PreviewSection from "@/components/ui/EbookPage/PreviewSection";

interface Props {
    locale: string;
    blok: {
        hideNavFooter: boolean;
        stickyCTALabel: string;
        stickyNavigationMessage: string;
        navLogoOverride: string;
        Form: any[];
        formHeading: string;
        formSubheading: string;
        shareImage: {
            filename: string;
        };
        heading: string;
        bodyContent: string;
        ebookContentLabel?: string;
        ebookContents?: {
            bodyContent: string;
        }[];
        publisherLabel: string;
        publisherLogos: [];
        ebookPreview?: any[];
    };
}

const EbookPage = ({ blok }: Props) => {
    const {
        heading,
        bodyContent,
        shareImage,
        ebookContents,
        publisherLabel,
        publisherLogos,
        ebookContentLabel,
        formHeading,
        formSubheading,
        ebookPreview,
        stickyCTALabel,
        stickyNavigationMessage,
    } = blok;

    return (
        <div {...storyblokEditable(blok)} className="bg-grays-G6 pt-[4.6875rem]">
            <EbookPageContextBridge
                stickyCTALabel={stickyCTALabel}
                stickyNavigationMessage={stickyNavigationMessage}
            />

            <Container className="flex flex-col-reverse lg:flex-row justify-normal lg:justify-between container">
                <div className="w-full lg:w-[41%] pt-10 lg:pt-0 border-t border-neutral01-0 lg:border-transparent mt-0 md:mt-[-200px] lg:mt-0">
                    <HeaderSection
                        heading={heading}
                        bodyContent={bodyContent}
                        ebookContentLabel={ebookContentLabel}
                        ebookContents={ebookContents}
                    />
                    <PublisherSection publisherLabel={publisherLabel} publisherLogos={publisherLogos} />
                </div>

                <ImageAndFormSection
                    imageUrl={shareImage?.filename}
                    formHeading={formHeading}
                    formSubheading={formSubheading}
                    formComponent={blok?.Form ?? []}
                />
            </Container>

            <PreviewSection ebookPreview={ebookPreview} />
        </div>
    );
};

export default EbookPage;