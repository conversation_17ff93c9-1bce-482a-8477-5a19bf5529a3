import { SectionItem } from "@/common/types";
import { storyblokEditable, StoryblokComponent } from "@storyblok/react/rsc";

interface Props {
    blok: {
        sections: SectionItem[];
    };
}

const TestPage = ({ blok }: Props) => {

    const { sections } = blok;
    return (
        <div {...storyblokEditable(blok)} >
            <div className="py-10">
                {sections?.map((section, index) => (
                    <StoryblokComponent
                        blok={section}
                        key={index}
                        sectionIndex={index}
                    />
                ))}
            </div>

        </div>
    );
};

export default TestPage;
