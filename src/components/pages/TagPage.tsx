"use client";

import { IPsudoTagPageContent, IStory } from "@/common/types";
import Container from "../ui/Container";
import Text from "../ui/Text";
import Image from "next/image";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/solid";
import { cn } from "@/common/utils";
import { useState } from "react";
import { getStoryListByTag } from "@/common/storyblok";
import { IPopulatedLearningCollectionStory } from "../articlePage/types";
import GroupIcon from "../icons/Group";
import SlideCarousel from "../ui/SlideCarousel";
import StoryblokLink from "../ui/StoryblokLink";
import { useLabelTranslation } from "@/common/hooks/useTranslation";
type ITagPageProps = {
  blok: IPsudoTagPageContent;
  locale: string;
};

export const revalidate = 3600;

const LearningCollectionCard = (props: {
  learningCollection: IPopulatedLearningCollectionStory;
}) => {
  const { learningCollection: c } = props;
  const {
    learningCollectionEditor = "",
    cover_image: learningCollectionCoverImage,
    title: learningCollectionTitle,
    tags,
    items = [],
  } = c.content;
  const [firstLearningCollectionArticle] = items;
  if (!firstLearningCollectionArticle) {
    return null;
  }
  return (
    <div
      key={c.uuid}
      className="flex h-full flex-col gap-[19px] p-[16px] shadow-[0px_0px_10px_0px_rgba(86,65,46,0.10),4px_14px_25px_0px_rgba(86,65,46,0.20)] md:flex-row"
    >
      <div className="relative aspect-[227/131] w-full rounded-[4px] md:min-w-[30%] md:max-w-[40%]">
        <Image
          src={`${learningCollectionCoverImage.filename}`}
          alt={learningCollectionTitle}
          fill
          className="size-full rounded-[4px] object-cover"
        />
        <div className="absolute left-[7px] top-[10px] flex items-center gap-[4px] rounded-[4px] bg-[rgba(0,0,0,0.60)] p-[10px]">
          <div className="font-lato text-base font-bold leading-[120%] text-white">
            {items.length}
          </div>
          <GroupIcon className="color-white" />
        </div>
      </div>
      <div className="flex grow flex-col gap-[25px]">
        <div className="flex grow flex-col gap-[8px]">
          <Text
            tag="h5"
            style="sh5"
            mdStyle="sh6"
            className="text-[1.5rem] font-normal leading-[110%] text-black"
          >
            {learningCollectionTitle}
          </Text>
          {learningCollectionEditor && (
            <Text tag="span" style="t3" className="text-neutral01-100">
              {learningCollectionEditor}
            </Text>
          )}
          <Text tag="h3" style="b3" className="text-black">
            Topics you'll cover:{" "}
            {tags
              .map((t) => {
                return tagValueToText(t.tag);
              })
              .join(", ")}
          </Text>
        </div>
        <StoryblokLink
          link={{
            newTab: true,
            url: "/" + firstLearningCollectionArticle.full_slug,
            linktype: "url",
            cached_url: "/" + firstLearningCollectionArticle.full_slug,
          }}
        >
          <Text
            tag="span"
            style="b2"
            className="flex cursor-pointer font-bold leading-[100%] text-primary01-75 hover:text-primary01-50 active:text-primary01-50"
          >
            Get Started →
          </Text>
        </StoryblokLink>
      </div>
    </div>
  );
};

const tagValueToText = (tagValue: string) => {
  return tagValue
    .split("-")
    .map((t) => {
      return t.charAt(0).toUpperCase() + t.slice(1);
    })
    .join(" ");
};

const PAGE_SIZE = 15;

const Pagination = (props: {
  currentPage: number;
  pageSize: number;
  current: number;
  total: number;
  onChange: (page: number) => unknown;
}) => {
  const { currentPage, pageSize, total, onChange } = props;
  const totalPages = Math.ceil(total / pageSize);
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages;
  const ItemsToBeRendered = [];
  for (let i = 1; i <= totalPages; i++) {
    if (
      i === 1 ||
      i === totalPages ||
      (i >= currentPage - 1 && i <= currentPage + 1)
    ) {
      ItemsToBeRendered.push(i);
      continue;
    }
    if (ItemsToBeRendered[ItemsToBeRendered.length - 1] === "...") {
      continue;
    }
    ItemsToBeRendered.push("...");
  }
  return (
    <div className="flex">
      {!isFirstPage && (
        <div
          onClick={() => {
            onChange(currentPage - 1);
          }}
          className="flex size-6 cursor-pointer items-center justify-center text-primary01-75 hover:text-primary01-50"
        >
          <ChevronLeftIcon className="size-3 stroke-[currentColor] stroke-[2]" />
        </div>
      )}
      {ItemsToBeRendered.map((item, index) => {
        return (
          <div
            className={cn(
              `flex size-6 items-center justify-center`,
              item === currentPage
                ? "cursor-pointer rounded-[14px] bg-primary01-75 text-white hover:bg-primary01-50"
                : item === "..."
                  ? "text-primary01-75"
                  : "cursor-pointer text-primary01-75 hover:text-primary01-50",
            )}
            key={index}
            onClick={() => {
              const __ = parseInt(item as string);
              if (isNaN(__)) {
                return;
              }
              if (__ === currentPage) {
                return;
              }
              onChange(__);
            }}
          >
            <Text tag="span" style="t2">
              {item}
            </Text>
          </div>
        );
      })}
      {!isLastPage && (
        <div
          onClick={() => {
            onChange(currentPage + 1);
          }}
          className="flex size-6 cursor-pointer items-center justify-center text-primary01-75 hover:text-primary01-50"
        >
          <ChevronRightIcon className="size-3 stroke-[currentColor] stroke-[2]" />
        </div>
      )}
    </div>
  );
};

const ArticleCard = ({
  story,
  imageClassName = "",
}: {
  story: IStory;
  imageClassName?: string;
}) => {
  const { published_at, content } = story;
  const { title, titleImage = "" } = content;
  return (
    <div className="w-full">
      <div className={cn("relative w-full", imageClassName)}>
        <Image
          src={`https:${titleImage}`}
          alt={title}
          fill
          className="rounded-[4px] object-cover"
        />
      </div>
      <div className="mt-[12px]">
        <Text style="b3" mdStyle="b4" tag="span" className="text-grays-G4">
          {new Date(published_at).toLocaleDateString("en-US", {
            month: "long",
            day: "numeric",
          })}
        </Text>
      </div>
      <StoryblokLink
        link={{
          url: "/" + story.full_slug,
          linktype: "url",
          cached_url: "/" + story.full_slug,
          newTab: true,
        }}
      >
        <div className="mt-[7px]">
          <Text
            tag="h6"
            style="sh6"
            mdStyle="sh7"
            xxlStyle="sh6"
            className="font-normal"
          >
            {title}
          </Text>
        </div>
      </StoryblokLink>
    </div>
  );
};

const SearchInput = (props: {
  containerClassNames: string;
  inputClassNames?: string;
}) => {
  const { containerClassNames, inputClassNames = "" } = props;
  const { t } = useLabelTranslation();
  return (
    <div
      className={cn(
        "flex h-[46px] w-full items-center rounded-[12px] border border-[#DBD8D8] p-[12px] font-body-p",
        containerClassNames,
      )}
    >
      <MagnifyingGlassIcon className="size-6" />
      <input
        type="text"
        placeholder={t("Search all resources")}
        className={cn(
          "ml-[3px] h-[1.5rem] w-full border-none p-0 font-body-p text-b2 placeholder-grays-G5 focus:outline-none focus:ring-0",
          inputClassNames,
        )}
      />
    </div>
  );
};

const TrendingSection = (props: {
  firstTrendingArticle: IStory;
  secondTrendingArticle: IStory;
}) => {
  const { firstTrendingArticle, secondTrendingArticle } = props;
  return (
    <div className="hidden flex-col gap-[40px] border-b border-grays-G5 pb-[60px] md:flex md:gap-[60px]">
      <Text tag="h3" style="sh3" className="color-black">
        Trending
      </Text>
      <div className="flex flex-row gap-[30px]">
        <div className="flex w-[calc((100%-30px)/2)] flex-col gap-[30px]">
          <div className="w-full">
            <div className={cn("relative w-full", "aspect-square")}>
              <Image
                src={`https:${firstTrendingArticle.content.titleImage}`}
                alt={firstTrendingArticle.content.title}
                fill
                className="rounded-[4px] object-cover"
              />
            </div>
            <div className="mt-[12px]">
              <Text style="b3" tag="span" className="text-grays-G4">
                {new Date(firstTrendingArticle.published_at).toLocaleDateString(
                  "en-US",
                  {
                    month: "long",
                    day: "numeric",
                    // year: "numeric",
                  },
                )}
              </Text>
            </div>

            <div className="mt-[7px]">
              <StoryblokLink
                link={{
                  url: "/" + firstTrendingArticle.full_slug,
                  linktype: "url",
                  cached_url: "/" + firstTrendingArticle.full_slug,
                  newTab: true,
                }}
              >
                <Text tag="h6" style="sh6" className="font-normal">
                  {firstTrendingArticle.content.title}
                </Text>
              </StoryblokLink>
            </div>
          </div>
        </div>
        <div className="flex w-[calc((100%-30px)/2)] flex-col gap-[30px]">
          <div className="w-full">
            <div className={cn("relative w-full", "aspect-[81/56]")}>
              <Image
                src={`https:${secondTrendingArticle.content.titleImage}`}
                alt={secondTrendingArticle.content.title}
                fill
                className="rounded-[4px] object-cover"
              />
            </div>
            <div className="mt-[12px]">
              <Text style="b3" tag="span" className="text-grays-G4">
                {new Date(
                  secondTrendingArticle.published_at,
                ).toLocaleDateString("en-US", {
                  month: "long",
                  day: "numeric",
                })}
              </Text>
            </div>
            <div className="mt-[7px]">
              <StoryblokLink
                link={{
                  url: "/" + secondTrendingArticle.full_slug,
                  linktype: "url",
                  cached_url: "/" + secondTrendingArticle.full_slug,
                  newTab: true,
                }}
              >
                <Text tag="h6" style="sh6" className="font-normal">
                  {secondTrendingArticle.content.title}
                </Text>
              </StoryblokLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const TagPage = ({ blok, locale }: ITagPageProps) => {
  const {
    primaryTag,
    relatedTags,
    articles,
    trendingArticles = [],
    learningCollections = [],
  } = blok;
  const { stories, total: initTotal } = articles;
  const withLearningCollection = learningCollections.length > 0;
  const [firstTrendingArticle, secondTrendingArticle] = trendingArticles;
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(initTotal);
  const [currentArticles, setCurrentArticles] = useState(stories);
  const onChangePage = async (pageNo: number) => {
    const { stories, total } = await getStoryListByTag(
      locale,
      primaryTag.tag,
      pageNo,
      PAGE_SIZE,
    );
    setCurrentArticles(stories);
    setCurrentPage(pageNo);
    setTotal(total);
  };
  const topSection =
    firstTrendingArticle && secondTrendingArticle
      ? "trending"
      : withLearningCollection
        ? "learning"
        : "articles";
  return (
    <Container className="flex flex-col gap-y-[60px]">
      <div className="flex w-full flex-row-reverse lg:hidden">
        <SearchInput containerClassNames="w-full md:w-[301px]" />
      </div>
      <div className="flex w-full flex-col gap-[60px] lg:max-w-[70%]">
        {/** tag name and tag description */}
        <div className="flex flex-col gap-[20px]">
          <Text style="sh2" mdStyle="sh1" tag="h1" className="text-black">
            {primaryTag.name}
          </Text>
          <Text style="q4" mdStyle="ph2" tag="h2" className="text-black">
            {primaryTag.description}
          </Text>
        </div>
        <div className="flex flex-row flex-wrap gap-[10px] border-l-2 border-grays-G5 px-3">
          {relatedTags.map((t) => {
            const [firstChar, ...rest] = Array.from(t);
            const t2 = [firstChar?.toUpperCase() ?? "", ...rest].join("");
            return (
              <StoryblokLink
                key={t}
                link={{
                  url: `/${locale}/tag/${t}`,
                  linktype: "url",
                  cached_url: `/${locale}/tag/${t}`,
                  newTab: true,
                }}
              >
                <div className="flex h-[27px] items-center justify-center rounded-[30.5px] bg-primary01-75 px-[15px] py-[7px] text-white hover:bg-primary01-50">
                  <Text style="t3" tag="span" className="text-white">
                    {t2}
                  </Text>
                </div>
              </StoryblokLink>
            );
          })}
        </div>
      </div>
      <div className="flex justify-between">
        {/** left content section */}
        <div className="flex w-full flex-col gap-[60px] lg:w-[70%]">
          {/** trending articles */}
          {firstTrendingArticle && secondTrendingArticle && (
            <TrendingSection
              firstTrendingArticle={firstTrendingArticle}
              secondTrendingArticle={secondTrendingArticle}
            />
          )}
          {/** learning collection */}
          {withLearningCollection && (
            <div className="hidden flex-col gap-[60px] border-b border-grays-G5 pb-[60px] md:flex">
              <Text tag="h3" style="sh3" className="text-black">
                Collections
              </Text>
              {learningCollections.map(
                (c: IPopulatedLearningCollectionStory) => {
                  return (
                    <LearningCollectionCard
                      learningCollection={c}
                      key={c.uuid}
                    />
                  );
                },
              )}
            </div>
          )}
          {withLearningCollection && (
            <SlideCarousel
              header={
                <Text tag="h3" style="sh3" className="text-black">
                  Collections
                </Text>
              }
              wrapperClassName="border-b border-grays-G5 pb-[60px] md:hidden"
            >
              {learningCollections.map(
                (c: IPopulatedLearningCollectionStory) => {
                  return (
                    <LearningCollectionCard
                      learningCollection={c}
                      key={c.uuid}
                    />
                  );
                },
              )}
            </SlideCarousel>
          )}
          <div className="flex flex-col gap-[45px] md:gap-[32px]">
            {/** articles gallary */}
            <Text tag="span" style="b2" className="text-black">
              Showing {currentPage * PAGE_SIZE - PAGE_SIZE + 1}-
              {Math.min(currentPage * PAGE_SIZE, total)} of {total} articles
            </Text>
            <div className="flex flex-row flex-wrap gap-[30px]">
              {currentArticles.map((story) => (
                <div
                  key={story.uuid}
                  className="w-full md:w-[calc((100%-60px)/3)]"
                >
                  <ArticleCard story={story} imageClassName="aspect-square" />
                </div>
              ))}
            </div>
          </div>
          <div className="flex items-center justify-center">
            <Pagination
              currentPage={currentPage}
              pageSize={PAGE_SIZE}
              total={total}
              current={currentPage}
              onChange={async (pageNo: number) => {
                await onChangePage(pageNo);
              }}
            />
          </div>
        </div>
        <div
          className={cn(
            "hidden w-[20%] max-w-[262px] lg:block",
            topSection === "trending"
              ? "py-[99.6px]"
              : topSection === "learning"
                ? "py-[99.6px]"
                : "py-[56px]",
          )}
        >
          <SearchInput containerClassNames="sticky top-[50px] mb-[40px]" />
        </div>
      </div>
    </Container>
  );
};

export default TagPage;
