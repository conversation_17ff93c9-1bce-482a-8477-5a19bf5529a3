import { storyblokEditable } from "@storyblok/react/rsc";
import { bricolage, lato } from "@/app/fonts";
import CtaBanner, { ICtaBannerProps } from "@/components/bloks/CTABanner";
import CompanyLogoCarousel, {
  ICompanyLogoCarouselProps,
} from "../bloks/CompanyLogoCarousel";
import HeaderAndServicesGrid, {
  IHeaderAndServicesProps,
} from "../bloks/HeaderAndServices";
import FeaturedBannerAndCTA, {
  IBannerProps,
} from "../bloks/FeaturedBannerAndCTA";
import CaseStudiesSection from "../bloks/CaseStudiesSection";
import { ICaseStudySection } from "../bloks/CaseStudiesSection/types";

type Props = {
  locale: string;
  blok: {
    headerAndCta: ICtaBannerProps[];
    caseStudy: ICaseStudySection[];
    autoScrollCarousel: ICompanyLogoCarouselProps[];
    headerAndServicesGrid: IHeaderAndServicesProps["blok"][];
    featuredBannerAndCTA: IBannerProps["blok"][];
  };
  fullSlug: string;
};

const ResultsPage = (props: Props) => {
  const blok = props.blok;
  const ctaBanner = blok.headerAndCta[0];
  const caseStudySection = blok?.caseStudy?.[0];
  const companyLogoCarousel = blok?.autoScrollCarousel?.[0];
  const headerAndServicesGrid = blok?.headerAndServicesGrid?.[0];
  const featuredBanner = blok?.featuredBannerAndCTA?.[0];

  return (
    <div
      {...storyblokEditable(blok)}
      className={`${bricolage.variable} ${lato.variable} font-sans`}
    >
      {caseStudySection && (
        <CaseStudiesSection
          section={caseStudySection}
        />
      )}
      {featuredBanner && <FeaturedBannerAndCTA blok={featuredBanner} />}
      {companyLogoCarousel && (
        <CompanyLogoCarousel blok={companyLogoCarousel} />
      )}
      {ctaBanner && <CtaBanner blok={ctaBanner} />}
      {headerAndServicesGrid && (
        <HeaderAndServicesGrid blok={headerAndServicesGrid} />
      )}
    </div>
  );
};

export default ResultsPage;
