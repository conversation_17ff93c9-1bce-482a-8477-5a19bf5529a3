import { SbBlokData, storyblokEditable } from "@storyblok/react/rsc";
import SectionsMapper from "@/components/core/SectionsMapper";

import MiscHeader, { IMiscHeader } from "@/components/bloks/MiscHeader";

type Props = {
  blok: {
    sections: SbBlokData[];
    miscHeader: IMiscHeader[];
  };
  locale: string;
};

const GeneralPage = ({ blok, locale }: Props) => {
  const miscHeaderBlok = blok?.miscHeader?.[0];

  return (
    <section {...storyblokEditable(blok)} className="bg-grays-G6">
      {!!miscHeaderBlok && <MiscHeader miscHeaderBlok={miscHeaderBlok} />}
      {blok?.sections && (
        <SectionsMapper sections={blok.sections} locale={locale} />
      )}
    </section>
  );
};

export default GeneralPage;
