import { storyblokEditable } from "@storyblok/react/rsc";
import SectionsMapper from "@/components/core/SectionsMapper";
import SiteShellOverrider from "../ui/SiteShellOverrider";
import { getFormCount } from "@/common/utils/getFormCount";
import { IStory } from "@/common/types";

import MiscHeader, { IMiscHeader } from "@/components/bloks/MiscHeader";

type Props = {
  blok: {
    sections: [];
    miscHeader: IMiscHeader[];
    stickyNavigationMessage?: string;
    stickyCTALabel?: string;
    navLogoOverride?: string;
    hideNavFooter?: boolean;
  };
  story: IStory;
};

const CampaignPage = ({ blok, story }: Props) => {
  const { full_slug } = story;
  const miscHeaderBlok = blok?.miscHeader?.[0];

  // TODO: unhide this for release
  // if (
  //   !full_slug?.includes("campaigns/") &&
  //   !full_slug?.split("campaigns/")[1]?.includes("/")
  // ) {
  //   return (
  //     <Container>
  //       <p className="text-primary01-50">
  //         {`Campaign V2 Page only renders in "{locale}/campaigns" folder, and not in any subfolders inside "campaigns".  Please move this story to the appropriate folder.`}
  //       </p>
  //     </Container>
  //   );
  // }

  const formCount = getFormCount(blok?.sections);
  if (formCount > 1) {
    return (
      <div className="container mx-auto w-full px-4 py-24 text-center text-3xl">
        <p className="text-primary01-50">
          Multiple web-lead form detected. This page only allows one web-lead
          form.
        </p>
      </div>
    );
  }

  return (
    <section {...storyblokEditable(blok)} className="bg-grays-G6">
      {!!miscHeaderBlok && <MiscHeader miscHeaderBlok={miscHeaderBlok} />}
      {blok?.sections && <SectionsMapper sections={blok.sections} />}
      <SiteShellOverrider
        title={blok.stickyNavigationMessage}
        buttonText={blok.stickyCTALabel}
        navLogoOverride={blok.navLogoOverride}
        hideNavFooter={blok.hideNavFooter}
      />
    </section>
  );
};

export default CampaignPage;
