import { StoryblokComponent, storyblokEditable } from "@storyblok/react/rsc";
import { SectionItem } from "@/common/types";

import GpaScoreDisplay from "@/components/bloks/Calculators/GpaScoreDisplay";
import { NCEA_GPA_RESULT_KEY } from "@/components/bloks/Calculators/NceaToGpaCalculator/util";

import Text from "@/components/ui/Text";
import Container from "@/components/ui/Container";

interface Props {
  blok: {
    audio?: string;
    header: string;
    pageTitle: string;
    headerTitleLabel?: string;
    description: string;
    descriptionLabel: string;
    coverImage: string;
    excerpt: string;
    title: string;
    subtitle: string;
    blogTags: {
      tag: string;
      component: string;
    }[];
    sections: SectionItem[];
    MarkDown?: string;
    titleImage?: string;
    bottomSections: {
      link: string[];
      theme: string;
      heading: string;
      bodyContent: string;
      bodyImage: {
        image: string;
        altText: string;
        component: string;
      }[];
    }[];
  };
}

const NceaGpaCalculatorResult = ({ blok }: Props) => {
  return (
    <div className="pb-[4.75rem]">
      <Container
        size="full"
        {...storyblokEditable(blok)}
        className="relative !py-0 md:py-0"
      >
        <div
          className="relative w-full px-[25px] pb-10 md:px-[30px] md:pb-[1.875rem] lg:px-0 lg:pb-5xl"
          style={{
            background: `linear-gradient(272deg, #F0DBCA -1.82%, #F0E3D9 15.06%, #FFF2E7 46.43%, #F0E3D9 76.08%, #ECDCCF 98.82%)`,
          }}
        >
          <div className="z-8 relative flex flex-col items-center pt-[119px] md:pt-[125px] lg:pt-[138px]">
            <Text
              tag="h1"
              style="sh3"
              mdStyle="sh2"
              lgStyle="sh2"
              className="w-[20.375rem] text-center text-primary01-75 md:w-[39.625rem] lg:w-[39.75rem]"
            >
              {blok.headerTitleLabel}
            </Text>

            <div
              className={`relative mx-auto my-[20px] flex h-[105px] w-[269px] items-center justify-center rounded-[4px] transition-all duration-700 md:my-[20px] md:h-[102px] md:w-[402px] lg:my-[25px] lg:h-[140px] lg:w-[442px]`}
            >
              <div className="absolute inset-0 rounded-[4px] bg-gradient-to-br from-primary01-75 via-primary01-25 to-primary01-50 p-[2px]">
                <div className="size-full rounded-[2px] bg-neutral01-25">
                  <GpaScoreDisplay gpaScoreSessionKey={NCEA_GPA_RESULT_KEY} />
                </div>
              </div>
            </div>

            <Text
              tag="p"
              style="sh4"
              className="w-80 text-center !text-[1.75rem] text-primary01-75 md:w-[39.625rem] lg:w-[51.625rem]"
            >
              {blok.descriptionLabel ?? blok.description}
            </Text>
          </div>
        </div>
      </Container>
      {/* render sections */}
      <>
        {blok.sections?.map((section, index) => {
          if (
            section.component === "markdownSection" ||
            section.component === "tableSection"
          ) {
            return (
              <Container key={index} className="!pt-0">
                <StoryblokComponent
                  blok={section}
                  key={index}
                  sectionIndex={index}
                />
              </Container>
            );
          }
          return (
            <StoryblokComponent
              blok={section}
              key={index}
              sectionIndex={index}
            />
          );
        })}
      </>
    </div>
  );
};

export default NceaGpaCalculatorResult;
