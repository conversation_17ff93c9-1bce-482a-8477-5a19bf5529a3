import { StoryData } from "@/common/types";
import Container from "@/components/ui/Container";

import { storyblokEditable } from "@storyblok/react/rsc";
import ProfilePic from "../ui/ProfilePage/ProfilePic";
import ProfileDetailsSection from "../ui/ProfilePage/ProfileDetailsSection";
import ProfileHeaderSection from "../ui/ProfilePage/ProfileHeaderSection";
import ProfileArticlesSection from "../ui/ProfilePage/ProfileArticlesSection";
import StructuredDataPerson from "../ui/ProfilePage/StructuredDataPerson";
import { getValidSameAsUrl } from "../ui/ProfilePage/utils";
import { getArticles } from "@/common/profile/getArticles";
import { IBlogStory } from "@/components/bloks/NewsAndArticles/utils";

interface Props {
  locale: string;
  story: StoryData["story"];
  blok: {
    Name: string;
    Type: string[];
    Byline: string;
    CrimsonRole: string;
    Universities?: string;
    Awards?: string;
    Specialties?: string;
    LargeBiography: string;
    ProfilePicture: {
      filename: string;
      alt: string;
      is_external_url: boolean;
      fieldtype: string;
      id: string;
    };
    LinkedinProfile?: {
      id: string;
      url?: string;
      linktype: string;
      fieldtype: string;
      cached_url?: string;
    };
    Followers?: number;
    dateCreated?: string;
    dateModified?: string;
  };
}

const ProfilePage = async ({ blok, story, locale }: Props) => {
  const {
    Name,
    CrimsonRole,
    LargeBiography,
    ProfilePicture,
    Universities = "",
    Awards = "",
    Specialties = "",
    LinkedinProfile,
  } = blok;
  const LinkedinProfileURL = getValidSameAsUrl(LinkedinProfile);

  let articleList: IBlogStory[] = [];
  const getArticlesRes = story && (await getArticles(story, locale));
  articleList = getArticlesRes;

  return (
    <div
      {...storyblokEditable(blok)}
      className="container mx-auto mb-12 w-full"
    >
      <StructuredDataPerson
        name={Name}
        universities={Universities}
        awards={Awards}
        jobTitle={CrimsonRole}
        description={LargeBiography}
        image={ProfilePicture?.filename}
        sameAs={LinkedinProfileURL}
        followers={blok.Followers}
        dateCreated={blok.dateCreated}
        dateModified={blok.dateModified}
      />

      <Container className="lg:px-[4.7rem]">
        <div className="mt-[3.374rem] flex w-full flex-col justify-between lg:flex-row">
          <div className="order-1 w-full lg:order-2 lg:w-[54%] lg:pt-12 xl:w-[54.4%] 2xl:w-[49%]">
            <ProfileHeaderSection
              name={Name}
              role={CrimsonRole}
              biography={LargeBiography}
            />

            <ProfileArticlesSection
              visibility="desktop"
              articleList={articleList}
            />
          </div>

          <div className="relative order-2 w-full lg:order-1 lg:w-[36.5%] xl:w-[35%] 2xl:w-[39.7%]">
            <ProfilePic
              LinkedinProfileURL={LinkedinProfileURL}
              src={ProfilePicture?.filename}
              Name={Name}
              className="aspect-[2/3] w-full md:mx-auto md:w-[59%] lg:w-full"
            />
            <ProfileDetailsSection
              universities={Universities}
              awards={Awards}
              specialties={Specialties}
              className="mt-14 overflow-hidden border-b border-grays-G5 md:pb-[3.3rem] lg:border-b-0 lg:pb-0"
            />
          </div>
        </div>

        <ProfileArticlesSection visibility="mobile" articleList={articleList} />
      </Container>
    </div>
  );
};

export default ProfilePage;
