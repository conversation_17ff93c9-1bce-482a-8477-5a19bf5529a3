import { SbBlokData, storyblokEditable } from "@storyblok/react/rsc";
import SectionsMapper from "@/components/core/SectionsMapper";

import CaseStudyHeader from "@/components/bloks/CaseStudyHeader";
import { ICaseStudyHeaderBlok } from "@/components/bloks/CaseStudyHeader/types";

interface Props {
  locale: string;
  blok: {
    pageTitle: string;
    sections: SbBlokData[];
    component: string;
    caseStudyHeader: ICaseStudyHeaderBlok[];
  };
  fullSlug: string;
}

const CaseStudyPage = (props: Props) => {
  const { blok } = props;
  const caseStudyHeader = blok?.caseStudyHeader?.[0];
  return (
    <div {...storyblokEditable(blok)}>
      {!!caseStudyHeader && (
        <CaseStudyHeader
          caseStudyHeaderBlok={caseStudyHeader}
          pageTitle={blok.pageTitle}
          fullSlug={props.fullSlug}
        />
      )}
      {/** TIPS: use Container in your components to ensure consistent padding and layout */}
      {/** Component Render */}
      {blok?.sections && <SectionsMapper sections={blok.sections} />}
    </div>
  );
};

export default CaseStudyPage;
