import {
  SbBlokData,
  StoryblokComponent,
  storyblokEditable,
} from "@storyblok/react/rsc";
import NceaGpaCalculatorForm from "@/components/bloks/Calculators/NceaToGpaCalculator/NceaGpaCalculatorForm";
import { NceaGpaCalculatorPageProps } from "@/components/bloks/Calculators/NceaToGpaCalculator/types";
import BlogTag from "@/components/bloks/BlogTag";
import Text from "@/components/ui/Text";
import Container from "@/components/ui/Container";

import { getBlogTagNames } from "@/common/article/getBlogTagNames";

const DYNAMIC_CONTENT_ID = `nceaPageDynamicMainContent`;

const NceaGpaCalculator = async ({
  story,
  blok,
}: NceaGpaCalculatorPageProps) => {
  const tags = await getBlogTagNames(story.content.BlogTagV2 ?? []);
  return (
    <>
      <Container
        size="full"
        {...storyblokEditable(blok as unknown as SbBlokData)}
        className="relative !py-0 md:py-0"
      >
        <div
          className="relative w-full px-[25px] pb-10 md:px-[30px] md:pb-[1.875rem] lg:h-[34.5625rem] lg:px-0 lg:pb-0"
          style={{
            background: `linear-gradient(272deg, #F0DBCA -1.82%, #F0E3D9 15.06%, #FFF2E7 46.43%, #F0E3D9 76.08%, #ECDCCF 98.82%)`,
          }}
        >
          <div className="z-8 relative flex flex-col items-center pt-[119px] md:pt-[125px] lg:pt-[138px]">
            <Text
              tag="h1"
              style="sh3"
              className="mb-4 w-full text-center text-primary01-75 md:mb-5 lg:mb-[1.625rem] lg:w-[39.75rem]"
            >
              {blok.header}
            </Text>

            <Text
              tag="p"
              style="ph2"
              mdStyle="ph1"
              lgStyle="ph1"
              className="w-full text-center text-primary01-75 lg:w-[51.625rem]"
            >
              {blok.subHeader}
            </Text>

            <div className="mt-6 flex items-center justify-center gap-4 font-display-sans">
              {tags.length > 0 &&
                tags.map((tag, idx) => (
                  <BlogTag
                    key={idx}
                    tagLink={`/blog/tags/${tag.value.toLowerCase()}`}
                    tagName={tag.name}
                  />
                ))}
            </div>

            {/* form area container - mobile/tablet */}
            <div className="mt-10 w-full md:mt-[1.875rem] lg:hidden lg:w-[44.25rem]">
              <NceaGpaCalculatorForm blok={blok} />
            </div>
          </div>
        </div>

        {/* form area container - desktop (absolute) */}
        <div className="z-8 absolute left-1/2 top-[23.75rem] hidden w-[46.4275rem] -translate-x-1/2 lg:block">
          <NceaGpaCalculatorForm
            blok={blok}
            dynamicContentId={DYNAMIC_CONTENT_ID}
          />
        </div>
      </Container>

      {/* Main Content, text and image  */}
      <div id={DYNAMIC_CONTENT_ID} className="lg:pt-[33.125rem]">
        {blok.bottomSections?.map((section, index) => {
          if (section.component === "markdownSection") {
            return (
              <Container key={index} className="!pt-0">
                <StoryblokComponent
                  blok={section}
                  key={index}
                  sectionIndex={index}
                />
              </Container>
            );
          }
          return (
            <StoryblokComponent
              blok={section}
              key={index}
              sectionIndex={index}
            />
          );
        })}
      </div>
    </>
  );
};

export default NceaGpaCalculator;
