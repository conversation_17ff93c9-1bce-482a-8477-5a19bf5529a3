import { storyblokEditable } from "@storyblok/react/rsc";

import CtaBanner, { ICtaBannerProps } from "@/components/bloks/CTABanner";
import { IMentorsSectionProps } from "../bloks/MentorsSection/types";
import { IStudentResultsCarouselProps } from "@/components/bloks/StudentResultsCarousel/types";
import HomepageHeader, {
  IHomepageHeaderProps,
} from "@/components/bloks/HomepageHeader";
import ServicesSection, {
  IServicesSectionProps,
} from "@/components/bloks/ServicesSection";
import { IResultsProps } from "@/components/bloks/ResultStackSection/ResultStackSection";
import { INewsAndBlogProps } from "@/components/bloks/NewsAndArticles/types";
import EventsPreviewSection, {
  IEventsPreviewSectionProps,
} from "../bloks/EventsPreviewSection";
import StudentResultsStack from "@/components/bloks/ResultStackSection";
import NewsAndArticles from "../bloks/NewsAndArticles";
import StudentResultsCarousel from "@/components/bloks/StudentResultsCarousel";
import MentorsSection from "@/components/bloks/MentorsSection";
import CaseStudiesSection from "@/components/bloks/CaseStudiesSection";
import { ICaseStudySection } from "@/components/bloks/CaseStudiesSection/types";
import { ISbSiteShellStoryContent } from "@/components/ui/SiteShell/types";
import HomePageStructuredData from "../ui/HomePageStructuredData";

type Props = {
  locale: string;
  blok: {
    headerAndCta: ICtaBannerProps[];
    studentResultsCarousel: IStudentResultsCarouselProps[];
    mentorsSection: IMentorsSectionProps[];
    newsAndArticles: INewsAndBlogProps[];
    resultsSection: IResultsProps[];
    heroSection: IHomepageHeaderProps[];
    servicesSection: IServicesSectionProps["blok"][];
    eventsPreviewSection: IEventsPreviewSectionProps[];
    caseStudySection: ICaseStudySection[];
  };
  fullSlug: string;
  story: any;
  siteShellContent: ISbSiteShellStoryContent | null;
};

const HomePage = (props: Props) => {
  const blok = props.blok;
  const story = props.story;
  const siteShellContent = props.siteShellContent;

  const servicesSection = blok?.servicesSection?.[0];
  const ctaBanner = blok?.headerAndCta?.[0];
  const resultsCarousel = blok?.studentResultsCarousel?.[0];
  const homePageHero = blok?.heroSection?.[0];
  const mentorsSection = blok?.mentorsSection?.[0];
  const resultsStack = blok?.resultsSection?.[0];
  const eventsPreviewSection = blok?.eventsPreviewSection?.[0];
  const newsArticleAndBlog = blok?.newsAndArticles?.[0];
  const caseStudySection = blok?.caseStudySection?.[0];
  return (
    <div {...storyblokEditable(blok)}>
      {homePageHero && <HomepageHeader {...homePageHero} />}
      {resultsStack && <StudentResultsStack blok={resultsStack} />}

      {resultsCarousel && (
        <StudentResultsCarousel
          blok={resultsCarousel}
          className="pb-10 md:pb-[75px]"
        />
      )}
      {mentorsSection && (
        <MentorsSection {...mentorsSection} locale={props.locale} />
      )}
      <div className="bg-gradient-to-b from-[#E9DDD2] to-neutral01-0 bg-cover bg-center bg-no-repeat">
        {servicesSection && <ServicesSection blok={{ ...servicesSection }} />}
        {caseStudySection && (
          <CaseStudiesSection
            section={caseStudySection}
            transparent={true}
          />
        )}
      </div>

      {eventsPreviewSection && (
        <EventsPreviewSection
          blok={eventsPreviewSection}
          locale={props.locale}
        />
      )}
      {newsArticleAndBlog && (
        <NewsAndArticles blok={newsArticleAndBlog} locale={props.locale} />
      )}
      {ctaBanner && <CtaBanner blok={ctaBanner} />}
      <HomePageStructuredData
        locale={props.locale}
        metaDescription={
          (story as { content?: { metaDescription?: string } })?.content
            ?.metaDescription
        }
        socialMediaLinks={siteShellContent?.socialMediaIcons}
      />
    </div>
  );
};

export default HomePage;
