import { StoryblokComponent, storyblokEditable } from "@storyblok/react/rsc";
import { SectionItem } from "@/common/types";
import UshGpaScoreDisplay from "@/components/bloks/Calculators/UshToGpaCalculator/UshGpaScoreDisplay";
import Container from "@/components/ui/Container";

interface Props {
  blok: {
    heading: string;
    audio?: string;
    header: string;
    pageTitle: string;
    headerTitleLabel?: string;
    header2TitleLabel?: string;
    descriptionLabel: string;
    description: string;
    coverImage: string;
    excerpt: string;
    title: string;
    subtitle: string;
    blogTags: {
      tag: string;
      component: string;
    }[];
    sections: SectionItem[];
    MarkDown?: string;
    titleImage?: string;
    bottomSections: {
      link: string[];
      theme: string;
      heading: string;
      bodyContent: string;
      bodyImage: {
        image: string;
        altText: string;
        component: string;
      }[];
    }[];
  };
}

const UshGpaCalculatorResult = ({ blok }: Props) => {
  return (
    <div className="pb-[4.75rem]">
      <Container
        size="full"
        {...storyblokEditable(blok)}
        className="relative !py-0 md:py-0"
      >
        <div
          className="relative w-full px-[25px] pb-10 md:px-[30px] md:pb-[1.875rem] lg:px-0 lg:pb-5xl"
          style={{
            background: `linear-gradient(272deg, #F0DBCA -1.82%, #F0E3D9 15.06%, #FFF2E7 46.43%, #F0E3D9 76.08%, #ECDCCF 98.82%)`,
          }}
        >
          <UshGpaScoreDisplay
            unweightedGpaDescription={blok?.headerTitleLabel}
            weightedGpaDescription={blok?.header2TitleLabel}
            descriptionLabel={blok?.descriptionLabel}
          />
        </div>
      </Container>
      <>
        {blok.sections?.map((section, index) => {
          if (
            section.component === "markdownSection" ||
            section.component === "tableSection"
          ) {
            return (
              <Container key={index} className="!pt-0">
                <StoryblokComponent
                  blok={section}
                  key={index}
                  sectionIndex={index}
                />
              </Container>
            );
          }
          return (
            <StoryblokComponent
              blok={section}
              key={index}
              sectionIndex={index}
            />
          );
        })}
      </>
    </div>
  );
};

export default UshGpaCalculatorResult;
