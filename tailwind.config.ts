import { type Config } from "tailwindcss";
import { proseHelios } from "./tailwindcss/helios";
import { type PluginUtils } from "tailwindcss/types/config";
const plugin = require("tailwindcss/plugin");

export default {
  content: ["./src/**/*.tsx"], // rich editor styles
  safelist: [
    "tr:first-child",
    "tr:nth-child(even)",
    "tr:nth-child(odd)",
    "td",
    "th",
    "border-white",
    "border-black",
  ],
  theme: {
    fontFamily: {
      "display-sans": ["var(--font-bricolage)"],
      "display-serif": [
        "var(--font-itc-garamond)",
        "var(--font-source-han-serif-cn)",
        "var(--font-source-han-serif-jp)",
        "var(--font-source-han-serif-kr)",
      ],
      handwritten: ["var(--font-covered-by-your-grace)"],
      "body-single": ["var(--font-lato)"],
      "body-p": ["var(--font-lato)"],
    },
    colors: {
      black: "#000000",
      white: "#ffffff",
      transparent: "transparent",
      grey: {
        50: "#F3F3F7", // mist
        100: "#E5E5EC", // pale grey
        150: "#CCCCCC",
        200: "#C7C7D6",
        300: "#A9ACC0", //cadet blue
        400: "#828599",
        500: "#595D78", // stone
        600: "#474A59",
        700: "#43454D",
        800: "#303133",
        900: "#1A1A1A",
      },
      blue: {
        50: "#E3E3FC",
        200: "#4a90e2", // sky
        300: "#A4A6EB",
        500: "#464AC9", // indigo
        600: "#474866", // mulled wine
        700: "#333456", // navy
        900: "#20222C", // charcoal
      },
      red: {
        50: "#FFE5E6",
        300: "#ED4B53", // salmon
        400: "#C4120A",
        500: "#AA1E23",
        700: "#66070B",
        800: "#4A0509",
        900: "#330304",
        950: "#250205",
      },
      orange: {
        50: "#FFECE5",
        300: "#FF9473",
        500: "#FF6C3E", // orange (for salmon-coral as well)
        700: "#E55122",
        900: "#B23209",
      },
      yellow: {
        50: "#FFF1D9",
        300: "#FFD180",
        500: "#FBAA1B", // yellow
        700: "#E5970B",
        900: "#BF7A00",
      },
      maroon: {
        50: "#FFE5F2",
        300: "#CC7AA2",
        500: "#993D6A",
        700: "#751443", // mulberry
        900: "#4D0D2C",
      },
      purple: {
        50: "#F5E6FF",
        300: "#A67CBF",
        500: "#7F5499",
        700: "#643B7D",
        900: "#453052", // deep purple
      },
      green: {
        50: "#E5FFF1",
        300: "#AAF2CB",
        500: "#31E886",
        700: "#12C39A",
        900: "#0C8065",
      },
      primary01: {
        25: "#FAE2D8",
        50: "#F5170D",
        75: "#74070E",
        100: "#3A0407",
      },
      secondary01: {
        50: "#77a9f7", // light blue
        75: "#00307D",
        100: "#000042",
      },
      secondary02: {
        50: "#7373E3",
      },
      neutral01: {
        0: "#F5F1EE",
        25: "#DAC9BB",
        50: "#AE8C6F",
        75: "#56412E",
        100: "#1D150E",
      },
      grays: {
        G1: "#1E1E1E",
        G2: "#404040",
        G3: "#606060",
        G4: "#9AA0A6",
        G5: "#C0C0C0",
        G6: "#FAF9F6",
        G7: "#D8D8D8",
      },
    },
    extend: {
      boxShadow: {
        none: "none",
      },
      typography: ({ theme }: PluginUtils) => ({
        light: {
          css: {
            "--tw-prose-body": theme("colors.neutral01-100"),
            "--tw-prose-headings": theme("colors.neutral01-100"),
            "--tw-prose-links": theme("colors.neutral01-100"),
            "--tw-prose-bold": theme("colors.neutral01-100"),
            "--tw-prose-counters": theme("colors.neutral01-100"),
            "--tw-prose-bullets": theme("colors.grey[500]"),
          },
        },
        dark: {
          css: {
            "--tw-prose-body": theme("colors.white"),
            "--tw-prose-headings": theme("colors.white"),
            "--tw-prose-links": theme("colors.white"),
            "--tw-prose-bold": theme("colors.white"),
            "--tw-prose-counters": theme("colors.white"),
            "--tw-prose-bullets": theme("colors.white"),
          },
        },
        helios: proseHelios(theme),
      }),
      spacing: {
        none: "0",
        "4xs": "0.0625rem", // 1px
        "3xs": "0.125rem", // 2px
        "2xs": "0.25rem", // 4px
        xs: "0.375rem", // 6px
        sm: "0.5rem", // 8px
        md: "0.75rem", // 12px
        lg: "1rem", // 16px
        xl: "1.25rem", // 20px
        "2xl": "1.5rem", // 24px
        "3xl": "2rem", // 32px
        "4xl": "2.5rem", // 40px
        "5xl": "3.125rem", // 50px
      },
      animation: {
        "text-slide-up-14":
          "text-slide-up-14 35s cubic-bezier(0.83, 0, 0.17, 1) infinite",
        "infinite-slide-left": "infinite-slide-left 145s linear infinite",
        marquee: "marquee var(--duration) infinite linear",
        marquee2: "marquee2 var(--marquee-duration, 25s) linear infinite",
        "marquee-vertical": "marquee-vertical var(--duration) linear infinite",
      },
      keyframes: {
        "fade-in-up": {
          "0%": {
            opacity: "0",
            transform: "translateY(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "text-slide-up-14": {
          // For the 14-item list on the Animated Heading Section V2
          "0.00%, 5.71%": { transform: "translateY(-0.00%)" },
          "7.14%, 12.86%": { transform: "translateY(-6.67%)" },
          "14.29%, 20.00%": { transform: "translateY(-13.33%)" },
          "21.43%, 27.14%": { transform: "translateY(-20.00%)" },
          "28.57%, 34.29%": { transform: "translateY(-26.67%)" },
          "35.71%, 41.43%": { transform: "translateY(-33.33%)" },
          "42.86%, 48.57%": { transform: "translateY(-40.00%)" },
          "50.00%, 55.71%": { transform: "translateY(-46.67%)" },
          "57.14%, 62.86%": { transform: "translateY(-53.33%)" },
          "64.29%, 70.00%": { transform: "translateY(-60.00%)" },
          "71.43%, 77.14%": { transform: "translateY(-66.67%)" },
          "78.57%, 84.29%": { transform: "translateY(-73.33%)" },
          "85.71%, 91.43%": { transform: "translateY(-80.00%)" },
          "92.86%, 98.57%": { transform: "translateY(-86.67%)" },
          "100%": { transform: "translateY(-93.33%)" },
        },
        "infinite-slide-left": {
          "0%": { transform: "translateX(0)" },
          "100%": { transform: "translateX(-100%)" },
        },
        marquee: {
          "0%": {
            transform: "translateX(0%)",
          },
          "100%": {
            transform: "translateX(100%)",
          },
          from: {
            transform: "translateX(0)",
          },
          to: {
            transform: "translateX(calc(-100% - var(--gap)))",
          },
        },
        marquee2: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(0%)" },
        },
        "fade-in": {
          "0%": {
            opacity: "0",
            display: "none",
          },

          "100%": {
            opacity: "1",
            display: "block",
          },
        },
        "fade-out": {
          "0%": {
            opacity: "1",
            display: "block",
          },

          "100%": {
            opacity: "0",
            display: "none",
          },
        },
        "marquee-vertical": {
          from: {
            transform: "translateY(0)",
          },
          to: {
            transform: "translateY(calc(-100% - var(--gap)))",
          },
        },
      },
      fontSize: {
        // TO BE DEPRECATED - START
        body1: [
          "1rem",
          {
            lineHeight: "1.5rem",
            letterSpacing: "0",
          },
        ],
        body2: [
          "0.875rem",
          {
            lineHeight: "1.25rem",
            letterSpacing: "0",
          },
        ],
        body3: [
          "0.75rem",
          {
            lineHeight: "1rem",
            letterSpacing: "0",
          },
        ],
        breadcrumb: [
          "0.6875rem",
          {
            lineHeight: "0.9375rem",
            letterSpacing: "0",
          },
        ],
        caption: [
          "0.75rem",
          {
            lineHeight: "0.9375rem",
            letterSpacing: "0",
          },
        ],
        // Display Sans (Bricolage) sizes
        "sans-2xs": ["0.625rem", { lineHeight: "120%", fontWeight: "600" }], // 10px
        "sans-xs": ["0.75rem", { lineHeight: "120%", fontWeight: "600" }], // 12px
        "sans-sm": ["0.875rem", { lineHeight: "120%", fontWeight: "600" }], // 14px
        "sans-base": ["1rem", { lineHeight: "120%", fontWeight: "600" }], // 16px
        "sans-lg": ["1.25rem", { lineHeight: "120%", fontWeight: "600" }], // 20px
        "sans-xl": ["1.5rem", { lineHeight: "120%", fontWeight: "600" }], // 24px
        "sans-2xl": ["1.75rem", { lineHeight: "120%", fontWeight: "600" }], // 28px
        "sans-3xl": ["2rem", { lineHeight: "110%", fontWeight: "600" }], // 32px
        "sans-4xl": ["2.25rem", { lineHeight: "110%", fontWeight: "600" }], // 36px
        "sans-5xl": ["2.625rem", { lineHeight: "110%", fontWeight: "700" }], // 42px
        "sans-6xl": ["3.125rem", { lineHeight: "105%", fontWeight: "700" }], // 50px
        "sans-7xl": ["3.4375rem", { lineHeight: "105%", fontWeight: "700" }], // 55px

        // Display Serif (itcGaramond) sizes
        "serif-2xs": ["0.625rem", { fontWeight: "700", lineHeight: "1rem" }],
        "serif-xs": ["0.75rem", { fontWeight: "700", lineHeight: "1.25rem" }],
        "serif-sm": ["0.875rem", { fontWeight: "700", lineHeight: "1.5rem" }],
        "serif-base": ["1rem", { fontWeight: "700", lineHeight: "1.75rem" }],
        "serif-lg": ["1.25rem", { fontWeight: "700", lineHeight: "1.75rem" }],
        "serif-xl": ["1.5rem", { fontWeight: "700", lineHeight: "2rem" }],
        "serif-2xl": ["1.75rem", { fontWeight: "700", lineHeight: "2.25rem" }],
        "serif-3xl": ["2rem", { fontWeight: "700", lineHeight: "2.5rem" }],
        "serif-4xl": ["2.25rem", { fontWeight: "700", lineHeight: "2.75rem" }],
        "serif-5xl": [
          "2.625rem",
          { fontWeight: "700", lineHeight: "3.125rem" },
        ],
        "serif-6xl": ["3.125rem", { fontWeight: "700", lineHeight: "3.75rem" }],

        // Body Paragraph (Lato) sizes
        "body-p-2xs": ["0.75rem", { fontWeight: "400", lineHeight: "150%" }],
        "body-p-sm": ["0.875rem", { fontWeight: "400", lineHeight: "150%" }],
        "body-p-md": ["1rem", { fontWeight: "400", lineHeight: "150%" }],
        "body-p-lg": ["1.125rem", { fontWeight: "400", lineHeight: "150%" }],

        // Body Single (Lato) sizes
        "body-single-2xs": [
          "0.75rem",
          { fontWeight: "400", lineHeight: "120%" },
        ],
        "body-single-sm": [
          "0.875rem",
          { fontWeight: "400", lineHeight: "120%" },
        ],
        "body-single-md": ["1rem", { fontWeight: "400", lineHeight: "120%" }],
        // TO BE DEPRECATED - END
        // Design System Update
        h1: ["3.4375rem", { lineHeight: "105%", fontWeight: 700 }],
        h2: ["3.125rem", { lineHeight: "105%", fontWeight: 700 }],
        h3: ["2.25rem", { lineHeight: "110%", fontWeight: 600 }],
        h4: ["1.5rem", { lineHeight: "120%", fontWeight: 600 }],
        h5: ["1.25rem", { lineHeight: "120%", fontWeight: 600 }],
        h6: ["1rem", { lineHeight: "120%", fontWeight: 600 }],
        sh1: ["3.4375rem", { lineHeight: "110%", fontWeight: 400 }],
        sh2: ["3.125rem", { lineHeight: "110%", fontWeight: 400 }],
        sh3: ["2.625rem", { lineHeight: "110%", fontWeight: 400 }],
        sh4: ["2.25rem", { lineHeight: "120%", fontWeight: 400 }],
        sh5: ["1.75rem", { lineHeight: "120%", fontWeight: 400 }],
        sh6: ["1.5rem", { lineHeight: "110%", fontWeight: 400 }],
        sh7: ["1.25rem", { lineHeight: "110%", fontWeight: 400 }],
        ph1: ["2rem", { lineHeight: "120%", fontWeight: 300 }],
        ph2: ["1.75rem", { lineHeight: "120%", fontWeight: 300 }],
        q1: ["2.375rem", { lineHeight: "110%", fontWeight: 300 }],
        q2: ["2.25rem", { lineHeight: "110%", fontWeight: 300 }],
        q3: ["1.75rem", { lineHeight: "110%", fontWeight: 300 }],
        q4: ["1.5rem", { lineHeight: "120%", fontWeight: 300 }],
        q5: ["1.5rem", { lineHeight: "120%", fontWeight: 300 }],
        q6: ["1.125rem", { lineHeight: "120%", fontWeight: 300 }],
        n1: ["2.25rem", { lineHeight: "110%", fontWeight: 300 }],
        n2: ["1.5rem", { lineHeight: "120%", fontWeight: 300 }],
        a1: ["2rem", { lineHeight: "120%", fontWeight: 400 }],
        a2: ["1.25rem", { lineHeight: "120%", fontWeight: 400 }],
        b1: ["1.125rem", { lineHeight: "150%", fontWeight: 400 }],
        b2: ["1rem", { lineHeight: "150%", fontWeight: 400 }],
        b3: ["0.875rem", { lineHeight: "130%", fontWeight: 400 }],
        b4: ["0.75rem", { lineHeight: "150%", fontWeight: 400 }],
        b5: ["0.625rem", { lineHeight: "150%", fontWeight: 400 }],
        c1: ["0.875rem", { lineHeight: "130%", fontWeight: 400 }],
        button: ["0.875rem", { lineHeight: "120%", fontWeight: 600 }],
        t1: ["1.25rem", { lineHeight: "120%", fontWeight: 600 }],
        t2: ["1rem", { lineHeight: "120%", fontWeight: 600 }],
        t3: ["0.75rem", { lineHeight: "120%", fontWeight: 600 }],
        hw1: ["2.625rem", { lineHeight: "110%", fontWeight: 400 }],
        hw2: ["2.25rem", { lineHeight: "110%", fontWeight: 400 }],
        hw3: ["1.625rem", { lineHeight: "110%", fontWeight: 400 }],
        hw4: ["1.375rem", { lineHeight: "110%", fontWeight: 400 }],
        hw5: ["1.25rem", { lineHeight: "110%", fontWeight: 400 }],
        // Design System Mobile Styles
        mh1: ["2.25rem", { lineHeight: "110%", fontWeight: 600 }],
        "mh1.5": ["2rem", { lineHeight: "110%", fontWeight: 600 }],
        mb1: ["1rem", { lineHeight: "150%", fontWeight: 400 }],
        mph1: ["1.25rem", { lineHeight: "120%", fontWeight: 300 }],
      },
      screens: {
        xl: "1225px",
        "2xl": "1440px",
        "3xl": "1500px",
        "4xl": "1590px",
        max: "1900px",
        noscript: { raw: "(scripting: none)" },
        script: { raw: "(scripting: enabled)" },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
    },
  },
  plugins: [
    require("tailwind-scrollbar-hide"),
    require("@tailwindcss/forms"),
    require("@tailwindcss/typography"),
    require("autoprefixer"),
    // @ts-expect-error
    plugin(function ({ addComponents, theme }) {
      addComponents({
        ".autofill-light": {
          "&:-webkit-autofill, &:-webkit-autofill:hover, &:-webkit-autofill:focus":
            {
              "-webkit-text-fill-color": theme("colors.blue[700]"), // Text color for light theme
              "-webkit-box-shadow": `0 0 0px 1000px ${theme("colors.white")} inset`, // Background color for light theme
              transition: "background-color 5000s ease-in-out 0s",
            },
        },
        ".autofill-dark": {
          "&:-webkit-autofill, &:-webkit-autofill:hover, &:-webkit-autofill:focus":
            {
              "-webkit-text-fill-color": theme("colors.white"), // Text color for dark theme
              "-webkit-box-shadow": `0 0 0px 1000px ${theme("colors.blue[700]")} inset`, // Background color for dark theme
              transition: "background-color 5000s ease-in-out 0s",
            },
        },
      });
    }),
    require("tailwindcss-animate"),
    require("tailwind-scrollbar"),
  ],
} satisfies Config;
