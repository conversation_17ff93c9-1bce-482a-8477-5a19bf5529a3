# Component Documentation: FAQ

This document provides instructions for setting up and using the `FAQ` component and its child component, `FAQItem`, in Storyblok.

## 1. Child Component: `FAQItem`

The `FAQItem` component represents a single FAQ item with a question and answer.

### Storyblok Setup: `FAQItem`

1.  **Create a new Block**:
    -   Go to **Block Library** in Storyblok.
    -   Click **New** to create a new block.
    -   **Block Name**: `FAQItem`
    -   **Technical Name**: `FAQItem`
    -   **Block Type**: Nestable Block

#### Fields for `FAQItem`

| **Display Name**<br />**Field Name** | **Field Type** | **Source** | **Required** | **Default Value** | **Description** |
| --- | --- | --- | --- | --- | --- |
| **Question**<br />`question` | Text | - | YES | *(None)* | The FAQ question text |
| **Answer**<br />`answer` | Richtext | - | YES | *(None)* | The FAQ answer content |

## 2. Main Component: `FAQ`

The `FAQ` component displays a list of FAQ items with a heading and subheading.

### Storyblok Setup: `FAQ`

1.  **Create a new Block**:
    -   Go to **Block Library** in Storyblok.
    -   Click **New** to create a new block.
    -   **Block Name**: `FAQ`
    -   **Technical Name**: `FAQ`

#### Fields for `FAQ`

| **Display Name**<br />**Field Name** | **Field Type** | **Source** | **Required** | **Default Value** | **Description** |
| --- | --- | --- | --- | --- | --- |
| **Sub Heading**<br />`subHeading` | Text | - | NO | *(None)* | Optional subheading text |
| **Heading**<br />`heading` | Text | - | YES | *(None)* | Main heading for the FAQ section |
| **FAQs**<br />`faqs` | Blocks | - | YES | *(None)* | A container for FAQ items. Only `FAQItem` blocks should be allowed here. |

## 3. Usage

After setting up both blocks in Storyblok, you can add the `FAQ` component to any page.

### Adding the FAQ Component

1.  **Add the main FAQ block**:
    -   In a `sections` or `body` field (type: Blocks), click **Add Block** and select **FAQ**.
    -   Fill in the `Sub Heading` and `Heading` fields.

2.  **Add FAQ items**:
    -   In the `FAQs` field, click **Add Block**. You will only be able to select **FAQItem**.
    -   Add as many `FAQItem` blocks as needed.