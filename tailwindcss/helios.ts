import { type PluginUtils } from "tailwindcss/types/config";

export const proseHelios = (theme: PluginUtils["theme"]) => ({
  css: {
    // Rich Text Colors Configuration
    "--tw-prose-headings": theme("colors.black"),
    "--tw-prose-body": theme("colors.black"),
    "--tw-prose-th-borders": theme("colors.white"),
    "--tw-prose-td-borders": theme("colors.white"),

    // Font Configuration
    fontFamily: theme("fontFamily.body-single"),
    lineHeight: "150%",
    fontWeight: 400,
    "h1, h2, h3, h4, h5, h6": {
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "600 !important",
      fontStyle: "normal !important",
      color: theme("colors.black"),
    },
    h2: {
      marginTop: "3.5rem",
      fontSize: "2.25rem !important",
      lineHeight: "110%",
      marginBottom: "1rem",
    },
    h3: {
      marginTop: "2.5rem",
      marginBottom: "0.5rem",
      fontSize: "1.875rem !important",
      lineHeight: "120%",
    },
    h4: {
      fontSize: "1.25rem !important",
      marginBottom: "0.5rem",
    },
    table: {
      borderRadius: "0.5rem",
      borderSpacing: 0,
      backgroundColor: theme("colors.white"),
      color: theme("colors.neutral01.75"),
      width: "99%",
      overflow: "hidden",
    },
    "table, table > div, tr, tr > div, td, td > div, tbody, tbody > div, th, th > div":
      {
        borderCollapse: "collapse",
        border: "0 !important",
        borderStyle: "hidden !important",
      },
    "table tr:first-child": {
      backgroundColor: theme("colors.neutral01.25"),
      fontWeight: "700 !important",
    },
    // "table tr:nth-child(-n+2)": {
    //   backgroundColor: `${theme("colors.neutral01.25")} !important`,
    //   fontWeight: "700 !important",
    // },
    th: {
      textAlign: "center !important",
    },
    td: {
      lineHeight: "120%",
    },
    "table tr:nth-child(even):not(:first-child)": {
      backgroundColor: theme("colors.white"),
    },
    "table tr:nth-child(odd):not(:first-child)": {
      backgroundColor: theme("colors.neutral01.0"),
    },
    "table td, table th": {
      padding: "0.75rem 1rem",
    },
    "td:not(:first-child)": {
      textAlign: "center",
    },
    li: {
      listStylePosition: "inside",
    },
    "h2 > ol > li::marker": {
      color: theme("colors.primary01.75"),
    },
    "h3 > ol > li::marker": {
      color: theme("colors.white"),
      backgroundColor: theme("colors.primary01.75"),
      borderRadius: "20px",
      width: "24px",
      height: "24px",
      display: "inline-block",
    },
    a: {
      "&:hover": {
        transition: "color 300ms",
        color: theme("colors.primary01.50"),
      },
    },
    "a:hover span": {
      color: `${theme("colors.primary01.50")}!important`,
    },
    hr: {
      backgroundColor: `${theme("colors.grays.G5")} !important`,
      height: "1px !important",
      marginTop: "0 !important",
      marginBottom: "0 !important",
    },

    // number list styles
    ".slate-indent-1 ol > li": {
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontSize: "2.25rem",
      lineHeight: "2.5rem",
      position: "relative",
    },
    ".slate-indent-1 ol > li::marker": {
      color: `${theme("colors.primary01.75")} !important`,
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
      lineHeight: "2.5rem",
    },
    ".slate-indent-2 ol > li": {
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
      fontSize: "1.875rem",
      lineHeight: "2.25rem",
      marginLeft: "2rem",
      paddingLeft: "2.1rem",
      position: "relative",
    },
    ".slate-indent-2 ol > li::marker": {
      content: "''",
      fontSize: "0",
    },
    ".slate-indent-2 ol > li::before": {
      content: "counter(list-item) ",
      display: "inline-flex",
      alignItems: "center",
      justifyContent: "center",
      width: "24px",
      height: "24px",
      marginRight: "8px",
      backgroundColor: theme("colors.primary01.75"),
      color: theme("colors.white"),
      borderRadius: "50%",
      fontSize: theme("fontSize.sm"),
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
      position: "absolute",
      left: "0",
      top: "7px",
    },

    // Unordered list styles
    ".slate-indent-1 ul > li": {
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
      fontSize: "2.25rem",
      lineHeight: "2.5rem",
      position: "relative",
      paddingLeft: "1.75rem",
    },
    ".slate-indent-1 ul > li::marker": {
      content: "'– '",
      color: theme("colors.primary01.75"),
      fontSize: "2.25rem",
      lineHeight: "2.5rem",
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
    },

    ".slate-indent-2 ul > li": {
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
      fontSize: "1.875rem",
      lineHeight: "2.25rem",
      position: "relative",
      paddingLeft: "1.75rem",
    },
    ".slate-indent-2 ul > li::marker": {
      content: "'– '",
      color: theme("colors.red.900"),
      fontSize: "1.875rem",
      lineHeight: "2.25rem",
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
    },
    ".slate-indent-3 ul > li": {
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
      fontSize: theme("fontSize.xl"),
      lineHeight: theme("lineHeight.normal"),
      paddingLeft: "1.75rem",
    },
    ".slate-indent-3 ul > li::marker": {
      content: "'– '",
      color: theme("colors.red.900"),
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontWeight: "400",
    },
    "li span[data-slate-leaf='true']": {
      all: "unset",
      fontFamily: "var(--font-itc-garamond) !important",
      fontWeight: "400 !important",
      fontSize: "inherit !important",
      lineHeight: "inherit !important",
      color: "inherit !important",
      backgroundColor: "transparent !important",
    },
    // blockquote
    "blockquote [data-slate-font-size]": {
      fontSize: "inherit !important",
    },
    ".quote-author": {
      fontFamily: theme("fontFamily.body-single"),
      fontWeight: "400",
      color: theme("colors.grays.G4"),
    },
    blockquote: {
      position: "relative",
      fontFamily: (theme("fontFamily.display-serif") as string[]).join(", "),
      fontSize: "1.875rem !important",
      fontWeight: "400",
      fontStyle: "italic",
      lineHeight: "2.5rem",
      color: theme("colors.black"),
      paddingLeft: "2rem",
      marginBottom: "1rem",
      borderLeft: "none",
    },
    "blockquote .blockquote-icon": {
      position: "absolute",
      top: "0",
      left: "0",
      color: theme("colors.primary01.50"),
      width: "1.25rem",
      height: "1rem",
    },
    // code
    ".helios-code-block": {
      backgroundColor: "#F9F9F9",
      paddingLeft: "1.5rem !important",
      paddingRight: "1.5rem !important",
    },
    ".helios-code-block pre": {
      backgroundColor: "#F9F9F9",
      height: "auto !important",
      padding: "0 !important",
    },
    code: {
      display: "block",
      position: "relative",
      height: "auto !important",
      width: "auto !important",
      borderRadius: "0.375rem",
      paddingTop: "2rem",
      paddingBottom: "2rem",
      backgroundColor: "#F9F9F9",
      fontFamily: theme("fontFamily.body-single"),
      color: `${theme("colors.primary01.100")} !important`,
      fontSize: "1rem !important",
      lineHeight: "1.5rem !important",
      fontWeight: "400 !important",
      "&::before": {
        content: "none",
      },
      "&::after": {
        content: "none",
      },
    },

    // q&a
    ".qa-step-section": {
      counterReset: "qa-step",
    },
    ".qa-step-section h2": {
      marginTop: "1rem",
    },
    ".qa-step-section p": {
      marginBottom: "0",
      lineHeight: "1rem !important",
    },
    ".qa-step-header": {
      counterIncrement: "qa-step",
      position: "relative",
      marginTop: "2.5rem",
      marginBottom: "1rem !important",
    },

    "h3.qa-step-header ": {
      paddingLeft: "2rem !important",
    },

    ".qa-step-header::before": {
      content: "counter(qa-step) !important",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      width: "1.5rem",
      height: "1.5rem",
      marginRight: "0.25rem",
      marginBottom: "0.25rem",
      borderRadius: theme("borderRadius.DEFAULT"),
      borderWidth: "1px",
      borderStyle: "solid",
      borderColor: theme("colors.border.DEFAULT") ?? theme("colors.gray.300"),
      backgroundColor: theme("colors.neutral01.0"),
      color: theme("colors.primary01.75"),
      fontSize: theme("fontSize.sm"),
      fontFamily: theme("fontFamily.body-single"),
      fontWeight: "400",
      lineHeight: "1",
      position: "absolute",
      left: "0",
      top: "0.1rem",
    },
    // essay
    ".essay-excerpt-section": {
      backgroundColor: theme("colors.neutral01.0"),
    },
    ".essay-excerpt-section .essay-excerpt-title": {
      marginTop: "1rem",
      fontSize: theme("space.2xl"),
      fontFamily: theme("fontFamily.display-sans"),
      color: `${theme("colors.primary01.75")} !important`,
    },
    ".essay-excerpt-section .essay-excerpt-subTitle": {
      fontFamily: theme("fontFamily.body-single"),
    },
    ".essay-excerpt-body": {
      borderTop: "1px solid",
      borderColor: theme("colors.neutral01.25"),
    },
    ".media-embed-caption": {
      fontFamily: theme("fontFamily.body-single"),
      color: theme("colors.grays.G4"),
    },
    ".aspect-video": {
      borderRadius: "0.25rem",
    },
    ".heilos-plate-image": {
      marginTop: "0",
      marginBottom: "0",
    },
  },
});
