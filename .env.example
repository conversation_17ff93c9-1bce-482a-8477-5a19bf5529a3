# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Example:
# SERVERVAR="foo"
# NEXT_PUBLIC_CLIENTVAR="bar"

REGION=ap # Change this to your region (ap, eu, us) as needed
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX
NEXT_PUBLIC_ONETRUST_ID=XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
NEXT_PUBLIC_STORYBLOK_PREVIEW_TOKEN=XXXXXXXXXXXXXXXXXXXXXXXX
NEXT_PUBLIC_BUILD_ENV=staging
NEXT_PUBLIC_SITE_URL="https://staging.crimsoneducation.org"
NEXT_PUBLIC_API_URL=https://api.staging.www.crimsoneducation.io

STORYBLOK_WEBHOOK_SECRET="test"
