# Crimson Websites V4 -> Next.js

This guide will help you set up and understand the Crimson Websites Next.js project.

## Prerequisites

- Node.js (Use NVM for version management)
- A Storyblok account and API tokens
- Git

## Initial Setup

1. Clone the repository:

```bash
git clone [repository-url]
cd crimson-websites-next
```

2. Install the correct version of Node.js using NVM:

```bash
nvm install
nvm use
```

3. Install dependencies:

```bash
pnpm install
```

4. Set up environment variables:
   Create a `.env.local` file in the root directory with the variables found in the `.env.example` file.

## Development

Start the development server:

```bash
pnpm run dev
```

### SSL Proxy for HTTPS Development

You may need to run the website under https for different reasons, for instance, having a Visusal Composer URl that's bound to your local environment. To do this we make use of `local-ssl-proxy`.

To do this, you'll nee a local certfificate which you can get using `mkcert`. See (local-ssl-proxy docs)[https://www.npmjs.com/package/local-ssl-proxy#run-ssl-proxy-with-a-self-signed-trusted-certificate] for more.
you can know more detail in SB link: https://www.storyblok.com/faq/setup-dev-server-https-proxy

Once you have that installed, run:

```bash
pnpm run proxy
```

## Storyblok Integration

This project uses Storyblok as its headless CMS. Here's how it's structured:

### Configuration

The Storyblok integration is configured in multiple places:

1. **StoryblokProvider** (`src/components/core/StoryblokProvider.tsx`):

   - Initializes Storyblok
   - Registers all available components
   - Maps Storyblok components to React components

2. **Visual Composer** (`src/app/visual-composer/page.tsx`):
   - Serves as the preview environment for Storyblok
   - Handles real-time updates in draft mode
   - Currently supports these page types:
     - WebinarEventPageV2
     - homePage

### Component Structure

Components are organized into two main categories:

1. **Pages**: High-level page templates

   - WebinarEventPageV2
   - HomePage

2. **Bloks**: Reusable components that can be composed in Storyblok
   - Form components
   - Layout components
   - UI elements

### Working with Storyblok

1. **Preview Mode**:

   - The visual-composer route (`/visual-composer`) is used for previewing content
   - Draft mode is enabled by default for immediate content updates

2. **Content Fetching**:
   - Use `fetchStoryBySlug` or `fetchData` functions from `src/common/storyblok.ts`
   - Content is automatically resolved and cached appropriately

## Project Structure

src/
├── app/ # Next.js app router pages
├── common/ # Shared utilities and types
├── components/
│ ├── bloks/ # Storyblok components
│ ├── core/ # Core shared application components
│ ├── context/ # React context providers
│ └── pages/ # Page templates
│ └── ui/ # UI components used to build the bloks
├── public/ # Static assets
├── styles/ # Global styles
├── types/ # TypeScript types
└── utils/ # Utility functions

## Available Scripts

- `pnpm run dev` - Start development server
- `pnpm run build` - Build production bundle
- `pnpm run start` - Start production server
- `pnpm run lint` - Run ESLint
- `pnpm run test` - Run tests
- `pnpm run proxy` - Start SSL proxy for local HTTPS
- `pnpm run analyze` - Analyze bundle size

## Important Notes

1. **Image Handling**:

   - The project is configured to work with Storyblok's image service
   - Remote patterns are configured in `next.config.js`

2. **Type Safety**:

   - TypeScript is used throughout the project
   - Storyblok types are defined in `src/common/types.ts`

3. **Styling**:
   - The project uses Tailwind CSS for styling
   - Class variance authority is used for component variants

## Troubleshooting

If you encounter issues with the Storyblok preview:

- Ensure your preview token is correct
- Check that the component is registered in StoryblokProvider
- Verify the page type is included in `approvedPageTypes` in the visual-composer

## Code Editors and Plugins

- We use [Tailwind CSS](https://tailwindcss.com/) for styling. The following VS Code plugins add the necessary intellisense: [Tailwind CSS Intellisense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)
- You should also make sure to have `Prettier - Code formatter` and `ESLint` installed in your code editor to make use of automatic syntax warning & error highligting

# General Coding Standards

## PR's and Code Reviews

- All code changes should be submitted via a pull request.
- All pull requests should be reviewed by at least one other developer who did not write the code.
- Lets try to keep PRs focused. If you are working on a feature, try to keep the PR focused on that feature. If you are fixing a bug, try to keep the PR focused on that bug. If there's a design sytem update, focus on on updating the design system to avoid bottlenecks with feature review. Large PR's are harder to review and can lead to more bugs. [Read more on Small CL's](https://google.github.io/eng-practices/review/developer/small-cls.html)
- PR Naming: If you have a specific ticket, the branch name and PR prefix should match the ticket ID. eg: `WEB-1234` followed by a description of what the PR does (for searchability). If you don't have a ticket, then prefixing your PR with `(feat|feature|fix|bug|tweak)` etc... is ok, just make sure the name is descriptive and easy to understand.
- PR Descriptions: Add the link of your ticket (if there is one) along with a short description of what will be changed
- When merging a branch, we can just use the regular `merge` option.
- You should be adding the demo link to the ClickUp (or whatever ticketing system we use) ticket action items, for both the code reviewer and the PM team.

## Code Structure & Naming Conventions

```
- components
  - ui (for sharable UI components)
    - Alert
      - // localised components can be placed in directories
      - index.tsx
      - utils.ts
    - Popup.tsx
  - pages (representing storyblok pages)
    - TestPage1.tsx
    - TestPage1.tsx
  - bloks // what is used to build storyblok "bloks"
    - HeaderAndCta
      - HeaderAndCta.test.ts
      - index.tsx
```

## State Management

- We use Jotai for state management. An `atoms.ts` file can be created in the directory of wherever this will be used to manage atom declaration and interaction.

## Style Standards

### 🧐 Goals and Purpose

To describe the style standards and usage of Tailwind CSS within the codebase.
Here is quick link to toole used in our system:

- [tailwindcss](https://tailwindcss.com/) - UI framework focused on using utility classes
- [headless UI](https://headlessui.com/) - Unstyled JS components that work really well with Tailwind
- [heroicons](https://heroicons.com/) - SVG icons that also work well with Tailwind
- [cva](https://cva.style/docs) - a utility library that simplifies managing CSS classes and variants in a consistent and scalable way

### Moving forward and things we won't use anymore

- Other UI Frameworks or design systems: We are moving away from having multiple framworks and UI tools in our system and will be using Tailwind CSS for styling. That means styles, components, icons etc. A focus on performance should be front-of-mind when building components.
- An assumption can be made that all new components will be styled with Tailwind CSS
- An assumption can be made that the default icon library will be Hero Icons
- An assumption can be made that the default component library will be Headless UI
- The branded design system is our style standard, and it should'nt be broken, to maintain consistency

### How to use Tailwind CSS

In our system, the preferred way to use Tailwind CSS is inline with classNames (+ the common `cn` method for class merging and logic) OR via variant creation with `cva` (Class Variant Authority).
Our code base is mobile-first, so we use the `sm`, `md`, `lg`, and `xl` breakpoints to adjust styles as needed.
These breakpoints should be the only ones used in the codebase moving forward, and this is communicated in the design system.

This will help to maintain readability and consistency across the codebase.

#### Tailwind CSS Packages & Plugins

We also make use of Tailwind CSS plugins to extend the functionality of Tailwind CSS. These plugins are added to the `tailwind.config.js` file and can be used in the same way as the base Tailwind CSS classes.

## Additional Resources

- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [tRPC](https://trpc.io)
- [Storyblok Documentation](https://www.storyblok.com/docs)
- [Storyblok React SDK](https://www.storyblok.com/docs/guide/5-using-storyblok-with-react)
- [Jotai](https://jotai.com)
